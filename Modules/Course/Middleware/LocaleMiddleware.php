<?php

namespace Modules\Course\Middleware;

use Closure;
use Illuminate\Http\Request;

/**
 * 多语言中间件
 * 根据请求头 Accept-Language 自动设置语言环境
 */
class LocaleMiddleware
{
    /**
     * 支持的语言列表
     * URL参数 lang 的值映射到内部语言代码
     */
    protected array $supportedLocales = [
        'zh_cn' => 'zh_CN',  // 简体中文
        'en' => 'en',        // 英文
        'zh_hk' => 'zh_HK',  // 香港繁体中文
    ];

    /**
     * 默认语言
     */
    protected string $defaultLocale = 'zh_CN';

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 1. 检查URL参数中的lang
        $locale = $request->get('lang');

        // 2. 如果没有lang参数，使用默认语言
        if (!$locale) {
            $locale = $this->defaultLocale;
        }

        // 3. 验证并转换语言代码
        $locale = $this->validateLocale($locale);

        // 4. 设置语言环境
        T_locale($locale);
        app()->setLocale($locale);

        return $next($request);
    }



    /**
     * 验证语言是否支持
     *
     * @param string $locale
     * @return string
     */
    protected function validateLocale(string $locale): string
    {
        // 转换为小写进行匹配
        $locale = strtolower($locale);

        // 如果是支持的语言代码，返回对应的内部代码
        if (isset($this->supportedLocales[$locale])) {
            return $this->supportedLocales[$locale];
        }

        // 不支持的语言，返回默认语言
        return $this->defaultLocale;
    }

    /**
     * 获取支持的语言列表
     *
     * @return array
     */
    public function getSupportedLocales(): array
    {
        return $this->supportedLocales;
    }

    /**
     * 获取当前语言的显示名称
     *
     * @param string $locale
     * @return string
     */
    public function getLocaleName(string $locale): string
    {
        $names = [
            'zh_CN' => '简体中文',
            'en' => 'English',
            'zh_HK' => '繁體中文',
        ];

        return $names[$locale] ?? $locale;
    }
}
