<?php

namespace Modules\Course\Middleware;

use Closure;
use Illuminate\Http\Request;

/**
 * 多语言中间件
 * 根据请求头 Accept-Language 自动设置语言环境
 */
class LocaleMiddleware
{
    /**
     * 支持的语言列表
     */
    protected array $supportedLocales = [
        'zh-CN' => 'zh_CN',
        'zh' => 'zh_CN',
        'en' => 'en',
        'zh-HK' => 'zh_HK',
        'zh-TW' => 'zh_HK',
    ];

    /**
     * 默认语言
     */
    protected string $defaultLocale = 'zh_CN';

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 1. 优先检查URL参数中的locale
        $locale = $request->get('locale');
        
        // 2. 如果URL参数中没有，检查Accept-Language请求头
        if (!$locale) {
            $acceptLanguage = $request->header('Accept-Language');
            $locale = $this->parseAcceptLanguage($acceptLanguage);
        }
        
        // 3. 如果都没有，使用默认语言
        if (!$locale) {
            $locale = $this->defaultLocale;
        }
        
        // 4. 验证语言是否支持
        $locale = $this->validateLocale($locale);
        
        // 5. 设置语言环境
        T_locale($locale);
        app()->setLocale($locale);
        
        return $next($request);
    }

    /**
     * 解析Accept-Language请求头
     *
     * @param string|null $acceptLanguage
     * @return string|null
     */
    protected function parseAcceptLanguage(?string $acceptLanguage): ?string
    {
        if (!$acceptLanguage) {
            return null;
        }

        // 解析Accept-Language头，格式如: zh-CN,zh;q=0.9,en;q=0.8
        $languages = [];
        $parts = explode(',', $acceptLanguage);
        
        foreach ($parts as $part) {
            $part = trim($part);
            if (strpos($part, ';') !== false) {
                [$lang, $quality] = explode(';', $part, 2);
                $quality = (float) str_replace('q=', '', $quality);
            } else {
                $lang = $part;
                $quality = 1.0;
            }
            
            $lang = trim($lang);
            if ($lang) {
                $languages[$lang] = $quality;
            }
        }
        
        // 按质量值排序
        arsort($languages);
        
        // 查找第一个支持的语言
        foreach (array_keys($languages) as $lang) {
            if (isset($this->supportedLocales[$lang])) {
                return $this->supportedLocales[$lang];
            }
            
            // 尝试匹配语言前缀 (如 zh-CN 匹配 zh)
            $langPrefix = explode('-', $lang)[0];
            if (isset($this->supportedLocales[$langPrefix])) {
                return $this->supportedLocales[$langPrefix];
            }
        }
        
        return null;
    }

    /**
     * 验证语言是否支持
     *
     * @param string $locale
     * @return string
     */
    protected function validateLocale(string $locale): string
    {
        $supportedValues = array_values($this->supportedLocales);
        
        if (in_array($locale, $supportedValues)) {
            return $locale;
        }
        
        // 如果传入的是键名，转换为值
        if (isset($this->supportedLocales[$locale])) {
            return $this->supportedLocales[$locale];
        }
        
        return $this->defaultLocale;
    }

    /**
     * 获取支持的语言列表
     *
     * @return array
     */
    public function getSupportedLocales(): array
    {
        return $this->supportedLocales;
    }

    /**
     * 获取当前语言的显示名称
     *
     * @param string $locale
     * @return string
     */
    public function getLocaleName(string $locale): string
    {
        $names = [
            'zh_CN' => '简体中文',
            'en' => 'English',
            'zh_HK' => '繁體中文',
        ];
        
        return $names[$locale] ?? $locale;
    }
}
