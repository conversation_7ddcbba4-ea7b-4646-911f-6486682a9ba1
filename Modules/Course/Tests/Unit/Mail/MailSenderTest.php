<?php

declare(strict_types=1);

namespace Modules\Course\Tests\Unit\Mail;

use Tests\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Modules\Course\Domain\Mail\MailConfig;
use Modules\Course\Domain\Mail\MailLogger;
use Modules\Course\Domain\Mail\MailSender;
use Illuminate\Support\Facades\Mail;
use Illuminate\Mail\Message;

/**
 * 邮件发送器单元测试
 */
class MailSenderTest extends TestCase
{
    /**
     * 邮件配置Mock对象
     * @var MailConfig|MockObject
     */
    private $mailConfig;

    /**
     * 邮件日志记录器Mock对象
     * @var MailLogger|MockObject
     */
    private $mailLogger;

    /**
     * 邮件发送器实例
     * @var MailSender
     */
    private $mailSender;

    /**
     * 测试前准备
     */
    protected function setUp(): void
    {
        parent::setUp();

        // 创建邮件配置Mock
        $this->mailConfig = $this->createMock(MailConfig::class);
        $this->mailConfig->method('getHost')->willReturn('smtp.example.com');
        $this->mailConfig->method('getPort')->willReturn(587);
        $this->mailConfig->method('getUsername')->willReturn('<EMAIL>');
        $this->mailConfig->method('getPassword')->willReturn('password');
        $this->mailConfig->method('getEncryption')->willReturn('tls');
        $this->mailConfig->method('getFromAddress')->willReturn('<EMAIL>');
        $this->mailConfig->method('getFromName')->willReturn('Test Sender');

        // 创建邮件日志记录器Mock
        $this->mailLogger = $this->createMock(MailLogger::class);
        
        // 创建邮件发送器实例
        $this->mailSender = new MailSender($this->mailConfig, $this->mailLogger);
        
        // 禁用实际发送邮件
        Mail::fake();
    }

    /**
     * 测试发送邮件成功
     */
    public function testSendEmailSuccess(): void
    {
        // 准备测试数据
        $to = '<EMAIL>';
        $subject = '测试邮件标题';
        $content = '<p>这是一封测试邮件内容</p>';
        
        // 期望日志记录器被调用一次，并记录成功状态
        $this->mailLogger->expects($this->once())
            ->method('log')
            ->with(
                $this->equalTo($to),
                $this->equalTo($subject),
                $this->equalTo($content),
                $this->equalTo('success'),
                $this->isNull()
            );
        
        // 执行测试
        $result = $this->mailSender->send($to, $subject, $content);
        
        // 验证结果
        $this->assertTrue($result, '邮件应该发送成功');
        
        // 验证邮件发送
        Mail::assertSent(function ($mail) use ($to, $subject, $content) {
            return $mail->hasTo($to) && 
                   $mail->subject === $subject && 
                   str_contains($mail->html, $content);
        });
    }
    
    /**
     * 测试带附件的邮件发送
     */
    public function testSendEmailWithAttachments(): void
    {
        // 准备测试数据
        $to = '<EMAIL>';
        $subject = '测试带附件的邮件';
        $content = '<p>这是一封带附件的测试邮件</p>';
        $attachments = [
            [
                'path' => storage_path('app/test_attachment.txt'),
                'name' => '测试附件.txt'
            ]
        ];
        
        // 创建测试附件
        file_put_contents(storage_path('app/test_attachment.txt'), '测试附件内容');
        
        // 期望日志记录器被调用一次，并记录成功状态
        $this->mailLogger->expects($this->once())
            ->method('log')
            ->with(
                $this->equalTo($to),
                $this->equalTo($subject),
                $this->equalTo($content),
                $this->equalTo('success'),
                $this->isNull()
            );
        
        // 执行测试
        $result = $this->mailSender->send($to, $subject, $content, $attachments);
        
        // 验证结果
        $this->assertTrue($result, '带附件的邮件应该发送成功');
        
        // 验证邮件发送
        Mail::assertSent(function ($mail) use ($to, $subject, $content) {
            return $mail->hasTo($to) && 
                   $mail->subject === $subject && 
                   str_contains($mail->html, $content);
        });
        
        // 删除测试附件
        @unlink(storage_path('app/test_attachment.txt'));
    }
    
    /**
     * 测试发送邮件失败的情况
     */
    public function testSendEmailFailure(): void
    {
        // 准备测试数据
        $to = 'invalid-email';  // 无效的邮箱地址
        $subject = '测试邮件失败';
        $content = '<p>这是一封测试失败的邮件</p>';
        
        // 设置邮件发送失败
        Mail::shouldReceive('send')->andThrow(new \Exception('邮件发送失败'));
        
        // 期望日志记录器被调用一次，并记录失败状态
        $this->mailLogger->expects($this->once())
            ->method('log')
            ->with(
                $this->equalTo($to),
                $this->equalTo($subject),
                $this->equalTo($content),
                $this->equalTo('failed'),
                $this->isType('string')
            );
        
        // 执行测试
        $result = $this->mailSender->send($to, $subject, $content);
        
        // 验证结果
        $this->assertFalse($result, '邮件应该发送失败');
    }
} 