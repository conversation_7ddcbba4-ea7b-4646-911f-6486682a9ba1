<?php

declare(strict_types=1);

namespace Modules\Course\Tests\Feature\Mail;

use Tests\TestCase;
use Illuminate\Support\Facades\Mail;
use Modules\Course\Domain\Mail\Mailable\TestEmail;

/**
 * 邮件发送功能测试
 */
class MailSendingTest extends TestCase
{
    /**
     * 设置测试环境
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // 禁用实际发送邮件
        Mail::fake();
    }
    
    /**
     * 测试邮件模板直接发送
     */
    public function testDirectMailableSending(): void
    {
        // 创建测试邮件
        $email = new TestEmail(
            '直接发送测试',
            '<p>这是通过邮件模板直接发送的测试邮件</p>'
        );
        
        // 发送邮件
        Mail::to('<EMAIL>')->send($email);
        
        // 验证邮件发送
        Mail::assertSent(TestEmail::class, function ($mail) {
            return $mail->hasTo('<EMAIL>') && 
                   $mail->subject === '直接发送测试';
        });
    }
    
    /**
     * 测试邮件附件发送
     */
    public function testMailWithAttachments(): void
    {
        // 创建测试文件
        $filePath = storage_path('app/test_mail_attachment.txt');
        file_put_contents($filePath, '附件测试内容');
        
        try {
            // 附件配置
            $attachments = [
                [
                    'path' => $filePath,
                    'name' => '测试附件.txt'
                ]
            ];
            
            // 创建带附件的测试邮件
            $email = new TestEmail(
                '带附件的测试邮件',
                '<p>这是一封带附件的测试邮件</p>',
                $attachments
            );
            
            // 发送邮件
            Mail::to('<EMAIL>')->send($email);
            
            // 验证邮件发送
            Mail::assertSent(TestEmail::class, function ($mail) {
                return $mail->hasTo('<EMAIL>') && 
                       $mail->subject === '带附件的测试邮件';
            });
        } finally {
            // 清理测试文件
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }
} 