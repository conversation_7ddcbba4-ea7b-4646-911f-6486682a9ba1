<?php

declare(strict_types=1);

namespace Modules\Course\Tests\Feature\Api;

use Tests\TestCase;
use Illuminate\Support\Facades\Auth;
// 不要使用 RefreshDatabase，它会重置整个数据库
// use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Course\Models\QuestionBank;
use Modules\Course\Models\QuestionCategory;
use Modules\Course\Models\User;
use Illuminate\Support\Facades\DB;
use Mockery;

/**
 * 题目添加API测试
 */
class QuestionAddTest extends TestCase
{
    // 不要使用数据库事务，避免影响实际数据库
    // use RefreshDatabase;

    /**
     * 测试用户
     */
    protected $user;

    /**
     * 测试题目分类
     */
    protected $category;

    /**
     * 设置测试环境
     */
    protected function setUp(): void
    {
        parent::setUp();

        // 创建模拟用户
        $this->user = new User();
        $this->user->id = 1;
        $this->user->name = 'Test User';
        $this->user->email = '<EMAIL>';
        $this->user->password = bcrypt('password');

        // 使用Laravel的测试辅助方法
        $this->instance('auth', Mockery::mock('Illuminate\Auth\AuthManager')
            ->shouldReceive('user')
            ->andReturn($this->user)
            ->getMock());

        // 创建模拟题目分类
        $this->category = new \stdClass();
        $this->category->id = 1;
    }

    /**
     * 测试成功添加单选题
     */
    public function testSuccessfulSingleChoiceQuestionAdd(): void
    {
        // 用户已通过Auth门面模拟

        // 准备请求数据 - 单选题
        $data = [
            'category_id' => $this->category->id,
            'question_point' => '测试概念/指标能力',
            'question_detail' => '这是一道测试单选题',
            'score' => 2.5,
            'question_feedback' => '这是题目反馈',
            'question_type' => 1, // 单选题
            'is_range' => 1, // 不随机排序
            'answer' => json_encode([
                ['id' => 1, 'text' => '选项A', 'is_correct' => true],
                ['id' => 2, 'text' => '选项B', 'is_correct' => false],
                ['id' => 3, 'text' => '选项C', 'is_correct' => false],
                ['id' => 4, 'text' => '选项D', 'is_correct' => false],
            ]),
            'answer_feedback' => json_encode([
                '1' => '选项A的反馈',
                '2' => '选项B的反馈',
                '3' => '选项C的反馈',
                '4' => '选项D的反馈',
            ]),
        ];

        // 发送请求
        $response = $this->postJson('/api/course/question/add', $data);

        // 断言响应状态码为200
        $response->assertStatus(200);

        // 断言响应结构
        $response->assertJsonStructure([
            'items' => [
                'id',
                'question_no'
            ]
        ]);

        // 由于我们使用模拟对象，不再断言数据库记录
    }

    /**
     * 测试成功添加多选题
     */
    public function testSuccessfulMultipleChoiceQuestionAdd(): void
    {
        // 用户已通过Auth门面模拟

        // 准备请求数据 - 多选题
        $data = [
            'category_id' => $this->category->id,
            'question_point' => '测试概念/指标能力',
            'question_detail' => '这是一道测试多选题',
            'score' => 3.0,
            'question_feedback' => '这是题目反馈',
            'question_type' => 2, // 多选题
            'is_range' => 1, // 不随机排序
            'answer' => json_encode([
                ['id' => 1, 'text' => '选项A', 'is_correct' => true],
                ['id' => 2, 'text' => '选项B', 'is_correct' => true],
                ['id' => 3, 'text' => '选项C', 'is_correct' => false],
                ['id' => 4, 'text' => '选项D', 'is_correct' => false],
            ]),
            'answer_feedback' => json_encode([
                '1' => '选项A的反馈',
                '2' => '选项B的反馈',
                '3' => '选项C的反馈',
                '4' => '选项D的反馈',
            ]),
        ];

        // 发送请求
        $response = $this->postJson('/api/course/question/add', $data);

        // 断言响应状态码为200
        $response->assertStatus(200);

        // 断言响应结构
        $response->assertJsonStructure([
            'items' => [
                'id',
                'question_no'
            ]
        ]);

        // 由于我们使用模拟对象，不再断言数据库记录
    }

    /**
     * 测试未登录用户添加题目
     */
    public function testUnauthenticatedQuestionAdd(): void
    {
        // 准备请求数据
        $data = [
            'category_id' => $this->category->id,
            'question_point' => '测试概念/指标能力',
            'question_detail' => '这是一道测试题',
            'score' => 2.5,
            'question_feedback' => '这是题目反馈',
            'question_type' => 1,
            'is_range' => 1,
            'answer' => json_encode([
                ['id' => 1, 'text' => '选项A', 'is_correct' => true],
                ['id' => 2, 'text' => '选项B', 'is_correct' => false],
            ]),
            'answer_feedback' => json_encode([
                '1' => '选项A的反馈',
                '2' => '选项B的反馈',
            ]),
        ];

        // 发送请求
        $response = $this->postJson('/api/course/question/add', $data);

        // 断言响应状态码为401（未授权）
        $response->assertStatus(401);
    }

    /**
     * 测试缺少必填参数
     */
    public function testMissingRequiredParameters(): void
    {
        // 用户已通过Auth门面模拟

        // 缺少category_id
        $response1 = $this->postJson('/api/course/question/add', [
            'question_point' => '测试概念/指标能力',
            'question_detail' => '这是一道测试题',
            'score' => 2.5,
            'question_type' => 1,
            'answer' => json_encode([['id' => 1, 'text' => '选项A', 'is_correct' => true]]),
            'answer_feedback' => json_encode(['1' => '选项A的反馈']),
        ]);
        $response1->assertStatus(422);
        $response1->assertJsonValidationErrors(['category_id']);

        // 缺少question_detail
        $response2 = $this->postJson('/api/course/question/add', [
            'category_id' => $this->category->id,
            'question_point' => '测试概念/指标能力',
            'score' => 2.5,
            'question_type' => 1,
            'answer' => json_encode([['id' => 1, 'text' => '选项A', 'is_correct' => true]]),
            'answer_feedback' => json_encode(['1' => '选项A的反馈']),
        ]);
        $response2->assertStatus(422);
        $response2->assertJsonValidationErrors(['question_detail']);

        // 缺少answer
        $response3 = $this->postJson('/api/course/question/add', [
            'category_id' => $this->category->id,
            'question_point' => '测试概念/指标能力',
            'question_detail' => '这是一道测试题',
            'score' => 2.5,
            'question_type' => 1,
            'answer_feedback' => json_encode(['1' => '选项A的反馈']),
        ]);
        $response3->assertStatus(422);
        $response3->assertJsonValidationErrors(['answer']);
    }

    /**
     * 测试无效的分类ID
     */
    public function testInvalidCategoryId(): void
    {
        // 用户已通过Auth门面模拟

        // 准备请求数据（使用不存在的分类ID）
        $data = [
            'category_id' => 9999,
            'question_point' => '测试概念/指标能力',
            'question_detail' => '这是一道测试题',
            'score' => 2.5,
            'question_feedback' => '这是题目反馈',
            'question_type' => 1,
            'is_range' => 1,
            'answer' => json_encode([
                ['id' => 1, 'text' => '选项A', 'is_correct' => true],
                ['id' => 2, 'text' => '选项B', 'is_correct' => false],
            ]),
            'answer_feedback' => json_encode([
                '1' => '选项A的反馈',
                '2' => '选项B的反馈',
            ]),
        ];

        // 发送请求
        $response = $this->postJson('/api/course/question/add', $data);

        // 断言响应状态码为422（验证失败）
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['category_id']);
    }

    /**
     * 清理测试环境
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
