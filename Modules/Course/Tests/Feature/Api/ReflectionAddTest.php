<?php

declare(strict_types=1);

namespace Modules\Course\Tests\Feature\Api;

use Tests\TestCase;
use Illuminate\Support\Facades\Auth;
// 不要使用 RefreshDatabase，它会重置整个数据库
// use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Course\Models\ReflectionReport;
use Modules\Course\Models\ReflectionSubmissionRecord;
use Modules\Course\Models\User;
use Illuminate\Support\Facades\DB;

/**
 * 反思报告提交API测试
 */
class ReflectionAddTest extends TestCase
{
    // 不要使用数据库事务，避免影响实际数据库
    // use RefreshDatabase;

    /**
     * 测试用户
     */
    protected $user;

    /**
     * 测试反思报告
     */
    protected $reflectionReport;

    /**
     * 设置测试环境
     */
    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试用户
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // 创建测试反思报告
        $this->reflectionReport = ReflectionReport::create([
            'course_id' => 1,
            'unit_id' => 1,
            'title' => '测试反思报告',
            'description' => '这是一个测试反思报告',
            'is_hidden' => 0,
            'start_date' => now()->subDays(10),
            'end_date' => now()->addDays(10),
            'sort_order' => 1,
            'creator_id' => 1,
            'created_at' => time(),
            'updated_at' => time(),
            'deleted_at' => 0,
        ]);
    }

    /**
     * 测试成功提交反思报告
     */
    public function testSuccessfulReflectionSubmission(): void
    {
        // 模拟用户登录
        $this->actingAs($this->user);

        // 准备请求数据
        $data = [
            'reflection_id' => $this->reflectionReport->id,
            'file_url' => 'https://example.com/uploads/test_reflection.pdf',
        ];

        // 发送请求
        $response = $this->postJson('/api/course/reflection/add', $data);

        // 断言响应状态码为200
        $response->assertStatus(200);

        // 断言响应结构
        $response->assertJsonStructure([
            'code',
            'message',
            'data' => [
                'success',
                'message',
                'data' => [
                    'id',
                    'reflection_id',
                    'student_id',
                    'file_url',
                    'upload_time',
                    'submission_time',
                    'submission_status',
                    'overall_status',
                    'similarity_status',
                    'creator_id',
                    'created_at',
                    'updated_at',
                ]
            ]
        ]);

        // 断言响应内容
        $response->assertJson([
            'code' => 0,
            'message' => 'success',
            'data' => [
                'success' => true,
                'message' => '反思报告提交成功',
                'data' => [
                    'reflection_id' => $this->reflectionReport->id,
                    'student_id' => $this->user->id,
                    'file_url' => 'https://example.com/uploads/test_reflection.pdf',
                    'submission_status' => 2, // 已提交状态
                ]
            ]
        ]);

        // 断言数据库中存在记录
        $this->assertDatabaseHas('reflection_submission_record', [
            'reflection_id' => $this->reflectionReport->id,
            'student_id' => $this->user->id,
            'file_url' => 'https://example.com/uploads/test_reflection.pdf',
            'submission_status' => 2, // 已提交状态
        ]);
    }

    /**
     * 测试未登录用户提交反思报告
     */
    public function testUnauthenticatedReflectionSubmission(): void
    {
        // 准备请求数据
        $data = [
            'reflection_id' => $this->reflectionReport->id,
            'file_url' => 'https://example.com/uploads/test_reflection.pdf',
        ];

        // 发送请求
        $response = $this->postJson('/api/course/reflection/add', $data);

        // 断言响应状态码为401（未授权）
        $response->assertStatus(401);
    }

    /**
     * 测试缺少必填参数
     */
    public function testMissingRequiredParameters(): void
    {
        // 模拟用户登录
        $this->actingAs($this->user);

        // 缺少reflection_id
        $response1 = $this->postJson('/api/course/reflection/add', [
            'file_url' => 'https://example.com/uploads/test_reflection.pdf',
        ]);
        $response1->assertStatus(422);
        $response1->assertJsonValidationErrors(['reflection_id']);

        // 缺少file_url
        $response2 = $this->postJson('/api/course/reflection/add', [
            'reflection_id' => $this->reflectionReport->id,
        ]);
        $response2->assertStatus(422);
        $response2->assertJsonValidationErrors(['file_url']);
    }

    /**
     * 测试无效的反思报告ID
     */
    public function testInvalidReflectionId(): void
    {
        // 模拟用户登录
        $this->actingAs($this->user);

        // 准备请求数据（使用不存在的反思报告ID）
        $data = [
            'reflection_id' => 9999,
            'file_url' => 'https://example.com/uploads/test_reflection.pdf',
        ];

        // 发送请求
        $response = $this->postJson('/api/course/reflection/add', $data);

        // 断言响应状态码为422（验证失败）
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['reflection_id']);
    }
}
