<?php

declare(strict_types=1);

namespace Modules\Course\Tests\Feature\Api;

use Tests\TestCase;
use Illuminate\Support\Facades\Auth;
use Modules\Course\Models\User;
use Modules\Course\Models\Quiz;
use Modules\Course\Models\QuestionsAnswer;
use Illuminate\Support\Facades\DB;
use Mockery;

/**
 * 测验提交API测试
 * 
 * 注意：不使用 RefreshDatabase trait，避免重置数据库
 */
class QuizSubmitTest extends TestCase
{
    /**
     * 测试用户
     */
    protected $user;

    /**
     * 模拟的Quiz服务
     */
    protected $quizServiceMock;

    /**
     * 设置测试环境
     */
    protected function setUp(): void
    {
        parent::setUp();

        // 创建模拟用户
        $this->user = new \stdClass();
        $this->user->id = 1;
        $this->user->name = 'Test User';
        $this->user->email = '<EMAIL>';

        // 使用Laravel的测试辅助方法模拟Auth
        $this->instance('auth', Mockery::mock('Illuminate\Auth\AuthManager')
            ->shouldReceive('user')
            ->andReturn($this->user)
            ->getMock());

        // 模拟QuizService
        $this->quizServiceMock = Mockery::mock('Modules\Course\Services\QuizService');
        $this->app->instance('Modules\Course\Services\QuizService', $this->quizServiceMock);
    }

    /**
     * 清理测试环境
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 测试成功提交答卷
     */
    public function testSuccessfulQuizSubmission(): void
    {
        // 准备请求数据
        $quizId = 2;
        $data = [
            'action' => 'submit',
            'answers' => [
                [
                    'question_id' => 5,
                    'selected_option_ids' => [1]
                ],
                [
                    'question_id' => 4,
                    'selected_option_ids' => [1]
                ],
                [
                    'question_id' => 9,
                    'selected_option_ids' => [1]
                ],
                [
                    'question_id' => 12,
                    'selected_option_ids' => [1]
                ],
                [
                    'question_id' => 8,
                    'selected_option_ids' => [1]
                ]
            ]
        ];

        // 模拟服务层返回结果
        $expectedResult = [
            'id' => 123,
            'quiz_id' => $quizId,
            'student_id' => $this->user->id,
            'total_score' => 100,
            'actual_score' => 80,
            'status' => 2,
            'status_text' => '已完成',
            'start_time' => '2023-11-15 10:00:00',
            'end_time' => '2023-11-15 10:30:00',
            'duration' => 1800,
            'questions' => [],
            'user_answers' => $data['answers'],
            'grade_answers' => []
        ];

        // 设置模拟服务的行为
        $this->quizServiceMock->shouldReceive('submitQuizAnswer')
            ->once()
            ->with($quizId, $this->user->id, $data['answers'], 2)
            ->andReturn($expectedResult);

        // 发送请求
        $response = $this->postJson("/api/course/quiz/{$quizId}", $data);

        // 断言响应状态码为200
        $response->assertStatus(200);

        // 断言响应结构
        $response->assertJsonStructure([
            'items' => [
                'id',
                'quiz_id',
                'student_id',
                'total_score',
                'actual_score',
                'status',
                'status_text',
                'start_time',
                'end_time',
                'duration',
                'user_answers',
                'grade_answers'
            ]
        ]);

        // 断言响应内容
        $response->assertJson([
            'items' => $expectedResult
        ]);
    }

    /**
     * 测试保存答卷（不提交）
     */
    public function testSaveQuizWithoutSubmitting(): void
    {
        // 准备请求数据
        $quizId = 2;
        $data = [
            'action' => 'save',
            'answers' => [
                [
                    'question_id' => 5,
                    'selected_option_ids' => [1]
                ],
                [
                    'question_id' => 4,
                    'selected_option_ids' => [1]
                ]
            ]
        ];

        // 模拟服务层返回结果
        $expectedResult = [
            'id' => 123,
            'quiz_id' => $quizId,
            'student_id' => $this->user->id,
            'total_score' => 100,
            'status' => 1,
            'status_text' => '进行中',
            'start_time' => '2023-11-15 10:00:00',
            'duration' => 600,
            'questions' => [],
            'user_answers' => $data['answers']
        ];

        // 设置模拟服务的行为
        $this->quizServiceMock->shouldReceive('submitQuizAnswer')
            ->once()
            ->with($quizId, $this->user->id, $data['answers'], 1)
            ->andReturn($expectedResult);

        // 发送请求
        $response = $this->postJson("/api/course/quiz/{$quizId}", $data);

        // 断言响应状态码为200
        $response->assertStatus(200);

        // 断言响应结构
        $response->assertJsonStructure([
            'items' => [
                'id',
                'quiz_id',
                'student_id',
                'total_score',
                'status',
                'status_text',
                'start_time',
                'duration',
                'user_answers'
            ]
        ]);

        // 断言响应内容
        $response->assertJson([
            'items' => $expectedResult
        ]);
    }

    /**
     * 测试未登录用户提交答卷
     */
    public function testUnauthenticatedQuizSubmission(): void
    {
        // 重置Auth模拟，使其返回null
        $this->instance('auth', Mockery::mock('Illuminate\Auth\AuthManager')
            ->shouldReceive('user')
            ->andReturn(null)
            ->getMock());

        // 准备请求数据
        $quizId = 2;
        $data = [
            'action' => 'submit',
            'answers' => [
                [
                    'question_id' => 5,
                    'selected_option_ids' => [1]
                ]
            ]
        ];

        // 发送请求
        $response = $this->postJson("/api/course/quiz/{$quizId}", $data);

        // 断言响应状态码为401（未授权）
        $response->assertStatus(401);
    }

    /**
     * 测试提交格式不正确的答卷
     */
    public function testInvalidAnswerFormat(): void
    {
        // 准备请求数据 - 缺少answers数组
        $quizId = 2;
        $data = [
            'action' => 'submit'
        ];

        // 设置模拟服务的行为 - 抛出异常
        $this->quizServiceMock->shouldReceive('submitQuizAnswer')
            ->never(); // 不应该调用此方法

        // 发送请求
        $response = $this->postJson("/api/course/quiz/{$quizId}", $data);

        // 断言响应状态码为422（验证失败）或400（请求错误）
        $response->assertStatus(400);
    }
}
