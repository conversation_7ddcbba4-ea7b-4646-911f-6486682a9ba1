<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * Permission模型类
 * 
 * 该模型用于管理系统权限，包括权限名称、代码、模块和类型等
 * 
 * @property int $id 权限ID
 * @property string $name 权限名称
 * @property string $code 权限代码
 * @property string $description 权限描述
 * @property string $risk_ids 风险ID列表
 * @property string $module 所属模块
 * @property string $type 权限类型
 * @property int $parent_id 父权限ID
 * @property int $sort_order 排序顺序
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 * @property array $risk_list 风险列表
 */
class Permission extends Model
{
    protected $table = 'permission';

    protected $fillable = [
        'id', 'name', 'code', 'description','risk_ids', 'module', 'type', 'parent_id', 'sort_order', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    /**
     * 隐藏字段，查询结果中不会显示的字段
     *
     * @var array
     */
    protected $hidden = ['risk_ids', 'deleted_at', 'creator_id', 'created_at', 'updated_at'];

    /**
     * 追加到模型数组的访问器
     *
     * @var array
     */
    protected $appends = ['risk_list'];

    const TREE_CACHE_TIME = 3600; // 权限树缓存时间1小时,单位秒
    
    protected $casts = [
        'risk_ids' => 'array',
    ];

    /**
     * 风险常量
     */
    // 隐私风险
    const PRIVACY_RISK = 1; 
    // 跨网站脚本攻击
    const CROSS_SITE_SCRIPTING_RISK = 2; 
    // 垃圾资讯风险
    const JUNK_INFORMATION_RISK = 4;
    // 资料丢失风险
    const DATA_LOSS_RISK = 5;
    // 设定风险
    const SETTING_RISK = 6;
    // 管理信任风险
    const MANAGEMENT_TRUST_RISK = 7;
    
    /**
     * 风险数组
     */
    public static $riskArray = [
        self::PRIVACY_RISK => [
            'name' => '隐私风险',
            'icon' => 'privacy',    
        ],
        self::CROSS_SITE_SCRIPTING_RISK => [
            'name' => '跨网站脚本攻击',
        ],
        self::JUNK_INFORMATION_RISK => [
            'name' => '垃圾资讯风险',   
            'icon' => 'junk_information',
        ],
        self::DATA_LOSS_RISK => [
            'name' => '资料丢失风险',
            'icon' => 'data_loss',
        ],
        self::SETTING_RISK => [
            'name' => '设定风险',
            'icon' => 'setting',
        ],
        self::MANAGEMENT_TRUST_RISK => [
            'name' => '管理信任风险',
            'icon' => 'management_trust',
        ],
    ];

    /**
     * 获取风险列表
     * 
     * @return array
     */
    public function getRiskListAttribute()
    {
        $riskList = [];
        $riskIds = $this->risk_ids ?? [];

        foreach ($riskIds as $riskId) {
            if (isset(self::$riskArray[$riskId])) {
                $risk = self::$riskArray[$riskId];
                $risk['id'] = $riskId;
                $riskList[] = $risk;
            }
        }

        return $riskList;
    }
}
