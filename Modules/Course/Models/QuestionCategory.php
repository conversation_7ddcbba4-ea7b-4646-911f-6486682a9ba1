<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * QuestionCategory模型类
 *
 * 该模型用于管理题目分类，支持多级分类结构
 *
 * @property int $id 分类ID
 * @property string $name 分类名称
 * @property string $description 分类描述
 * @property int $parent_id 父分类ID
 * @property int $level 分类层级
 * @property int $sort_order 排序顺序
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class QuestionCategory extends Model
{
    protected $table = 'question_category';

    protected $fillable = [
        'id', 'name', 'description', 'parent_id', 'level', 'sort_order', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    protected $casts = [
        'deleted_at' => 'integer',
    ];
}
