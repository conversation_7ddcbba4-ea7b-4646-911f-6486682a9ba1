<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;


class QuestionsAnswer extends Model
{
    protected $table = 'questions_answer';

    protected $fillable = [
        'id', 'quiz_id', 'student_id', 'course_id', 'total_score', 'actual_score', 'status', 'start_time', 'end_time', 'duration', 'question', 'user_answer', 'grade_answer', 'created_at', 'updated_at', 'deleted_at',
    ];

}
