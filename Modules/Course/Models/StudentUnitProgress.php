<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * StudentUnitProgress模型类
 * 
 * 该模型用于跟踪和记录学生在课程单元中的学习进度
 * 
 * @property int $id 进度记录ID
 * @property int $student_id 学生ID
 * @property int $course_id 课程ID
 * @property int $unit_id 单元ID
 * @property string $start_time 开始学习时间
 * @property string $complete_time 完成学习时间
 * @property string $status 学习状态
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class StudentUnitProgress extends Model
{
    protected $table = 'student_unit_progress';

    protected $fillable = [
        'id', 'student_id', 'course_id', 'unit_id', 'start_time', 'complete_time', 'status', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

}
