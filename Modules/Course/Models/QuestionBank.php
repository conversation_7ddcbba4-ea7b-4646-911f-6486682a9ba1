<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * QuestionBank模型类
 *
 * 该模型用于管理题库中的题目，包括题目内容、选项和正确答案等
 *
 * @property int $id 题目ID
 * @property int $category_id 题目分类ID
 * @property string $question_point 概念/指标能力
 * @property string $question_detail 试题文字
 * @property float $score 题目分值
 * @property string $question_feedback 试题回馈
 * @property string $question_no 试题编号
 * @property int $question_type 题目类型 1单选、2多选
 * @property int $is_range 是否随机排序 1否、2是
 * @property array $answer 答案
 * @property array $answer_feedback 反馈信息
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class QuestionBank extends Model
{
    protected $table = 'question_bank';

    protected $fillable = [
        'id', 'category_id', 'question_point', 'question_detail',
        'score', 'question_feedback', 'question_no', 'question_type',
        'is_range', 'answer', 'answer_feedback', 'creator_id',
        'created_at', 'updated_at', 'deleted_at'
    ];

    protected $casts = [
        'id' => 'integer',
        'category_id' => 'integer',
        'score' => 'float',
        'question_type' => 'integer',
        'is_range' => 'integer',
        'creator_id' => 'integer',
        'deleted_at' => 'integer'
    ];

    /**
     * 获取答案
     *
     * @param string $value
     * @return array
     */
    public function getAnswerAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }

    /**
     * 设置答案
     *
     * @param mixed $value
     * @return void
     */
    public function setAnswerAttribute($value)
    {
        $this->attributes['answer'] = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 获取答案反馈
     *
     * @param string $value
     * @return array
     */
    public function getAnswerFeedbackAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }

    /**
     * 设置答案反馈
     *
     * @param mixed $value
     * @return void
     */
    public function setAnswerFeedbackAttribute($value)
    {
        $this->attributes['answer_feedback'] = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 获取题目所属分类
     */
    public function category()
    {
        return $this->belongsTo(QuestionCategory::class, 'category_id');
    }

    /**
     * 获取题目创建者
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * 获取未删除的题目
     */
    public function scopeNotDeleted($query)
    {
        return $query->where('deleted_at', 0);
    }
}
