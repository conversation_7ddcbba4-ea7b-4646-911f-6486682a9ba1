<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * 反思报告模型
 */
class ReflectionReport extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'reflection_report';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'id', 'course_id', 'unit_id', 'title', 'description', 'is_hidden',
        'start_date', 'end_date', 'total_score', 'sort_order', 'creator_id',
        'created_at', 'updated_at', 'deleted_at'
    ];

    /**
     * 日期字段转换
     *
     * @var array
     */
    protected $dates = [
        'start_date', 'end_date'
    ];


    /**
     * 评分标准定义
     */

    // 对题目和理论的理解评分标准
    const SCORE_UNDERSTANDING_LEVEL_1 = 0; // 對相關理論和題目不甚理解 / 誤解
    const SCORE_UNDERSTANDING_LEVEL_2 = 1; // 對相關理論和題目展示足夠的理解

    // 认知层次评分标准
    const SCORE_COGNITION_LEVEL_1 = 0; // 未能展示任何分析、批判性思维及综合能力
    const SCORE_COGNITION_LEVEL_2 = 1; // 展示合理的分析、批判性思维及综合能力

    // 讨论评分标准
    const SCORE_DISCUSSION_LEVEL_1 = 0; // 讨论散乱、欠缺逻辑及理据
    const SCORE_DISCUSSION_LEVEL_2 = 1; // 讨论清晰、具合理思路及理据

    // 文笔评分标准
    const SCORE_WRITING_LEVEL_1 = 0; // 文不达意，难於理解
    const SCORE_WRITING_LEVEL_2 = 1; // 结构清晰、表达准确、文笔流畅

    /**
     * 获取所有评分标准
     *
     * @return array
     */
    public static function getScoringCriteria()
    {
        // 手动加载翻译命名空间以确保翻译正常工作
        app('translator')->addNamespace('Course', base_path('Modules/Course/Lang'));

        return [
            'understanding' => [
                self::SCORE_UNDERSTANDING_LEVEL_1 => T("Course::validation.reflection_report.understanding_level_1"),
                self::SCORE_UNDERSTANDING_LEVEL_2 => T("Course::validation.reflection_report.understanding_level_2"),
            ],
            'cognition' => [
                self::SCORE_COGNITION_LEVEL_1 => T("Course::validation.reflection_report.cognition_level_1"),
                self::SCORE_COGNITION_LEVEL_2 => T("Course::validation.reflection_report.cognition_level_2"),
            ],
            'discussion' => [
                self::SCORE_DISCUSSION_LEVEL_1 => T("Course::validation.reflection_report.discussion_level_1"),
                self::SCORE_DISCUSSION_LEVEL_2 => T("Course::validation.reflection_report.discussion_level_2"),
            ],
            'writing' => [
                self::SCORE_WRITING_LEVEL_1 => T("Course::validation.reflection_report.writing_level_1"),
                self::SCORE_WRITING_LEVEL_2 => T("Course::validation.reflection_report.writing_level_2"),
            ],
        ];
    }

    public static function getScoringCriteriaTitle()
    {
        // 手动加载翻译命名空间以确保翻译正常工作
        app('translator')->addNamespace('Course', base_path('Modules/Course/Lang'));

        return [
            'understanding' => T("Course::validation.reflection_report.understanding"),
            'cognition' => T("Course::validation.reflection_report.cognition"),
            'discussion' => T("Course::validation.reflection_report.discussion"),
            'writing' => T("Course::validation.reflection_report.writing"),
        ];
    }

    /**
     * 获取格式化的评分标准（用于API返回）
     *
     * @return array
     */
    public static function getFormattedScoringCriteria()
    {
        // 手动加载翻译命名空间以确保翻译正常工作
        app('translator')->addNamespace('Course', base_path('Modules/Course/Lang'));

        return [
            [
                'name' => 'understanding',
                'title' => T("Course::validation.reflection_report.understanding"),
                'positive' => T("Course::validation.reflection_report.understanding_level_2"),
                'negative' => T("Course::validation.reflection_report.understanding_level_1")
            ],
            [
                'name' => 'cognition',
                'title' => T("Course::validation.reflection_report.cognition"),
                'positive' => T("Course::validation.reflection_report.cognition_level_2"),
                'negative' => T("Course::validation.reflection_report.cognition_level_1")
            ],
            [
                'name' => 'discussion',
                'title' => T("Course::validation.reflection_report.discussion"),
                'positive' => T("Course::validation.reflection_report.discussion_level_2"),
                'negative' => T("Course::validation.reflection_report.discussion_level_1")
            ],
            [
                'name' => 'writing',
                'title' => T("Course::validation.reflection_report.writing"),
                'positive' => T("Course::validation.reflection_report.writing_level_2"),
                'negative' => T("Course::validation.reflection_report.writing_level_1")
            ]
        ];
    }
}

