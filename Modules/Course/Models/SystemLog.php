<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Support\Facades\Auth;

/**
 * SystemLog模型类
 * 
 * 该模型用于记录系统操作日志，包括用户行为、操作对象和系统环境信息等
 * 
 * @property int $id 日志ID
 * @property int $user_id 用户ID
 * @property string $action 操作
 * @property string $target_type 目标类型
 * @property int $target_id 目标ID
 * @property string $description 操作描述
 * @property string $ip_address IP地址
 * @property string $user_agent 用户代理信息
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class SystemLog extends Model
{
    protected $table = 'system_log';

    protected $fillable = [
        'id', 'user_id', 'action', 'target_type', 'target_id', 'description', 'ip_address', 'user_agent', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];
    
    /**
     * 操作类型常量定义
     */
    const ACTION_LOGIN = 'login';
    const ACTION_LOGOUT = 'logout';
    const ACTION_RESET_PASSWORD = 'reset_password';
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_DELETE = 'delete';
    const ACTION_IMPORT = 'import';

    /**
     * 目标类型常量定义
     */
    const TARGET_TYPE_ROLE = 'role';
    const TARGET_TYPE_USER = 'user';
    const TARGET_TYPE_COURSE = 'course';
    const TARGET_TYPE_CLASS = 'class';
    const TARGET_TYPE_UNIT = 'unit';
    const TARGET_TYPE_VIDEO = 'video';
    const TARGET_TYPE_QUIZ = 'quiz';
    const TARGET_TYPE_REFLECTION = 'reflection';
    const TARGET_TYPE_RUBRIC = 'rubric';
    const TARGET_TYPE_ASSESSMENT = 'assessment';
    const TARGET_TYPE_COURSE_USER = 'course_user';
    const TARGET_TYPE_FILE = 'file';

    /**
     * 操作类型映射关系
     */
    const ACTION_MAP = [
        self::ACTION_LOGIN => '登录',
        self::ACTION_LOGOUT => '登出',
        self::ACTION_CREATE => '创建',
        self::ACTION_UPDATE => '更新',
        self::ACTION_DELETE => '删除',
        self::ACTION_RESET_PASSWORD => '重置密码',
        self::ACTION_IMPORT => '导入',
    ];

    /**
     * 目标类型映射关系
     */
    const TARGET_TYPE_MAP = [
        self::TARGET_TYPE_USER => '用户',
        self::TARGET_TYPE_COURSE => '课程',
        self::TARGET_TYPE_CLASS => '班级',
        self::TARGET_TYPE_UNIT => '单元',
        self::TARGET_TYPE_VIDEO => '视频',
        self::TARGET_TYPE_QUIZ => '测验',
        self::TARGET_TYPE_REFLECTION => '反思报告',
        self::TARGET_TYPE_RUBRIC => '评分标准',
        self::TARGET_TYPE_ASSESSMENT => '评估记录',
        self::TARGET_TYPE_COURSE_USER => '课程用户',
        self::TARGET_TYPE_FILE => '文件',
    ];


/**
     * 创建系统日志
     * 
     * @param array $data 日志数据
     * @return SystemLog
     */
    public static function create(array $data): SystemLog
    {
        // 获取ip地址
        $ipAddress = request()->ip();
        // 获取用户代理信息
        $userAgent = request()->userAgent();
       
        // 获取当前登录用户
        $user = Auth::user();
   
        // 创建系统日志记录
        $systemLog = new self();
        $systemLog->user_id = $user->id ?? 0;
        $systemLog->action = $data['action'];
        $systemLog->target_type = $data['target_type'];
        $systemLog->target_id = $data['target_id'];
        $systemLog->description = $data['description'];
        $systemLog->ip_address = $ipAddress ?? null;
        $systemLog->user_agent = $userAgent ?? null;
        $systemLog->creator_id = $user->id ?? 0;
        $systemLog->save();

        return $systemLog;
    }
}
