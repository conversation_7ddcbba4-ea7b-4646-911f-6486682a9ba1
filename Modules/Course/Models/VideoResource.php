<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * VideoResource模型类
 * 
 * 该模型用于管理课程视频资源，包括视频标题、描述和播放设置等
 * 
 * @property int $id 视频ID
 * @property int $course_id 课程ID
 * @property int $unit_id 单元ID
 * @property string $title 视频标题
 * @property string $description 视频描述
 * @property string $video_url 视频URL
 * @property int $duration 视频时长（秒）
 * @property bool $allow_fast_forward 是否允许快进
 * @property int $sort_order 排序顺序
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class VideoResource extends Model
{
    protected $table = 'video_resource';

    protected $fillable = [
        'id', 'course_id', 'unit_id', 'title', 'description', 'video_url', 'duration', 'allow_fast_forward', 'sort_order', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    protected $hidden = [
        'deleted_at',
        'created_at',
        'updated_at',
        'creator_id',
    ];

}
