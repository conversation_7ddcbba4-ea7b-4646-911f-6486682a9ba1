<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

class Info extends Model
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'info';

    /**
     * 指示是否自动维护时间戳
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 可以批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'description',
        'type',
        'sort_order',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'type' => 'integer',
        'sort_order' => 'integer',
        'creator_id' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'deleted_at' => 'integer',
    ];

    /**
     * 信息类型常量
     */
    const TYPE_FAQ = 1;          // FAQ
    const TYPE_ANNOUNCEMENT = 2;  // 公告
    const TYPE_CONTACT = 3;       // 联系我们

    /**
     * 获取类型名称
     *
     * @param int $type
     * @return string
     */
    public static function getTypeName(int $type): string
    {
        return match ($type) {
            self::TYPE_FAQ => 'FAQ',
            self::TYPE_ANNOUNCEMENT => '公告',
            self::TYPE_CONTACT => '联系我们',
            default => '未知类型',
        };
    }
}
