<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * CourseCategory模型类
 *
 * 该模型用于管理课程分类，支持多级分类结构
 *
 * @property int $id 分类ID
 * @property int $parent_id 父分类ID
 * @property string $name 分类名称
 * @property string $description 分类描述
 * @property string $cate_id_num 分类ID号
 * @property int $sort_order 排序顺序
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class CourseCategory extends Model
{
    protected $table = 'course_category'; // 对应数据库中的 edu_course_category 表

    protected $fillable = [
        'id', 'parent_id', 'name', 'description', 'cate_id_num', 'sort_order', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    protected $casts = [
        'parent_id' => 'integer',
        'sort_order' => 'integer',
        'creator_id' => 'integer',
        'deleted_at' => 'integer',
    ];

    /**
     * 获取未删除的分类
     */
    public function scopeNotDeleted($query)
    {
        return $query->where('deleted_at', 0);
    }

    /**
     * 获取父级分类
     */
    public function parent()
    {
        return $this->belongsTo(CourseCategory::class, 'parent_id');
    }

    /**
     * 获取子分类
     */
    public function children()
    {
        return $this->hasMany(CourseCategory::class, 'parent_id');
    }
}
