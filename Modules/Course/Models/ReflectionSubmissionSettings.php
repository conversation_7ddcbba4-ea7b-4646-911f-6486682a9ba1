<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * ReflectionSubmissionSettings模型类
 * 
 * 该模型用于管理课程反思作业的提交设置，包括提交时间、文件要求、查重设置等
 * 
 * @property int $id 设置ID
 * @property int $course_id 课程ID
 * @property string $start_date 开始提交日期
 * @property string $due_date 截止日期
 * @property bool $late_submission_allowed 是否允许延迟提交
 * @property string $late_submission_deadline 延迟提交截止日期
 * @property int $max_file_size 最大文件大小（字节）
 * @property bool $similarity_check_required 是否需要查重
 * @property float $similarity_threshold 相似度阈值
 * @property string $file_name_format 文件名格式
 * @property int $word_count_min 最小字数要求
 * @property int $word_count_max 最大字数限制
 * @property string $instructions 提交说明
 * @property string $declaration_text 声明文本
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class ReflectionSubmissionSettings extends Model
{
    protected $table = 'reflection_submission_settings';

    protected $fillable = [
        'id', 'course_id', 'start_date', 'due_date', 'late_submission_allowed', 'late_submission_deadline', 'max_file_size', 'similarity_check_required', 'similarity_threshold', 'file_name_format', 'word_count_min', 'word_count_max', 'instructions', 'declaration_text', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

}
