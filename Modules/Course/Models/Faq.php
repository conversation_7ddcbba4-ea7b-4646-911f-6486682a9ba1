<?php

namespace Modules\Course\Models;

use Illuminate\Database\Eloquent\Model;

class Faq extends Model
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'faq';

    /**
     * 指示是否自动维护时间戳
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * 可以批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'description',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'creator_id' => 'integer',
        'created_at' => 'integer',
        'updated_at' => 'integer',
        'deleted_at' => 'integer',
    ];
}
