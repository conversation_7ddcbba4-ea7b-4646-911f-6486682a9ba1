<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * FolderResource模型类
 * 
 * 该模型用于管理课程单元中的文件夹资源，支持文件夹和文件的层级结构
 * 
 * @property int $id 资源ID
 * @property int $unit_id 单元ID
 * @property int|null $parent_id 父文件夹ID，null表示顶级文件夹
 * @property string $title 资源标题
 * @property string $description 资源描述
 * @property string $resource_type 资源类型：folder或file
 * @property string|null $file_url 文件URL，仅当resource_type为file时有效
 * @property string|null $file_type 文件类型，仅当resource_type为file时有效
 * @property int $sort_order 排序顺序
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class FolderResource extends Model
{
    protected $table = 'folder_resource';

    protected $fillable = [
        'id', 'unit_id', 'parent_id', 'title', 'description', 'resource_type', 'file_url', 'file_type', 'sort_order', 'tree_id', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    protected $hidden = [
        'deleted_at',
        'created_at',
        'updated_at',
        'creator_id',
    ];

    const TYPE_FOLDER = 'folder';
    const TYPE_FILE = 'file';
    
    /**
     * 获取子资源（文件夹或文件）
     * 
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'id')
            ->orderBy('sort_order');
    }
    
    /**
     * 获取父文件夹
     * 
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }
    
    /**
     * 判断当前资源是否为文件夹
     * 
     * @return bool
     */
    public function isFolder()
    {
        return $this->resource_type === self::TYPE_FOLDER;
    }
    
    /**
     * 判断当前资源是否为文件
     * 
     * @return bool
     */
    public function isFile()
    {
        return $this->resource_type === self::TYPE_FILE;
    }
}
