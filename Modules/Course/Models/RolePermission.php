<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * RolePermission模型类
 * 
 * 该模型用于管理角色与权限之间的关联关系
 * 
 * @property int $id 关联ID
 * @property int $role_id 角色ID
 * @property int $permission_id 权限ID
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class RolePermission extends Model
{
    protected $table = 'role_permission';

    protected $fillable = [
        'id', 'role_id', 'permission_id', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    public function role()
    {
        return $this->belongsTo(Role::class, 'role_id', 'id');
    }

    public function permission()    
    {
        return $this->belongsTo(Permission::class, 'permission_id', 'id');
    }

}
