<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;


/**
 * Course模型类
 *
 * 该模型用于管理课程信息
 *
 * @property int $id 课程ID
 * @property string $name 课程全名
 * @property string $short_name 课程名简称
 * @property int $category_id 课程分类ID
 * @property int $status 课程状态： 1准备中 2 已发布 3已完成
 * @property int $is_show 是否可见： 1 可见 2 不可见
 * @property string $start_date 开课日期
 * @property string $end_date 结课日期
 * @property string $course_id 课程ID号码
 * @property int $is_user 是否包含用户 1 包含 2 不包含
 * @property string $roles 包含用户角色 格式：1,2,3
 * @property string $image_url 课程图片URL
 * @property string $description 课程描述
 * @property string $report_deadline 反思报告提交截止日期
 * @property int $max_students 最大学生数
 * @property int $sort_order 排序
 * @property bool $is_active 是否激活
 * @property int $creator_id 创建人ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class Course extends Model
{
    protected $table = 'course'; // 对应数据库中的 edu_course 表

     protected $fillable = [
        'id', 'name', 'short_name', 'category_id', 'status', 'is_show',
        'start_date', 'end_date', 'course_id', 'is_user', 'roles',
        'image_url', 'description', 'report_deadline', 'max_students','sort_order',
        'is_active', 'creator_id', 'created_at', 'updated_at', 'deleted_at'
    ];

    protected $casts = [
        'category_id' => 'integer',
        'status' => 'integer',
        'is_show' => 'integer',
        'is_user' => 'integer',
        'max_students' => 'integer',
        'is_active' => 'integer',
        'sort_order' => 'integer',
        'creator_id' => 'integer',
        'deleted_at' => 'integer',
     ];

    /**
     * 课程状态常量
     */
    const STATUS_PREPARING = 1; // 准备中
    const STATUS_PUBLISHED = 2; // 已发布
    const STATUS_COMPLETED = 3; // 已完成

    /**
     * 可见性常量
     */
    const SHOW_VISIBLE = 1;   // 可见
    const SHOW_INVISIBLE = 2; // 不可见

    /**
     * 是否包含用户常量
     */
    const USER_INCLUDED = 1;    // 包含用户
    const USER_NOT_INCLUDED = 2; // 不包含用户

    /**
     * 激活状态常量
     */
    const ACTIVE_NO = 1;  // 未激活
    const ACTIVE_YES = 2; // 已激活

    /**
     * 获取未删除的课程
     */
    public function scopeNotDeleted($query)
    {
        return $query->where('deleted_at', 0);
    }

    /**
     * 获取课程所属分类
     */
    public function category()
    {
        return $this->belongsTo(CourseCategory::class, 'category_id');
    }

    /**
     * 获取课程创建者
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    /**
     * 获取课程角色数组
     *
     * @return array
     */
    public function getRolesArray()
    {
        if (empty($this->roles)) {
            return [];
        }

        return array_map('intval', explode(',', $this->roles));
    }

    /**
     * 设置课程角色
     *
     * @param array $roles
     * @return void
     */
    public function setRolesArray(array $roles)
    {
        $this->roles = implode(',', array_filter(array_map('intval', $roles)));
    }

    /**
     * 获取参与该课程的用户
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'course_user', 'course_id', 'user_id')
            ->where('course_user.deleted_at', 0);
    }
 }

