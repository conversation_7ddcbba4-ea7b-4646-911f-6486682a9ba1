<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * StudentVideoWatching模型类
 * 
 * 该模型用于记录和跟踪学生观看课程视频的学习行为
 * 
 * @property int $id 观看记录ID
 * @property int $student_id 学生ID
 * @property int $video_id 视频ID
 * @property int $course_id 课程ID
 * @property string $start_time 开始观看时间
 * @property int $last_position 最后观看位置（秒）
 * @property int $watch_duration 观看时长（秒）
 * @property float $completion_percentage 完成百分比
 * @property bool $is_completed 是否完成观看
 * @property string $completed_time 完成观看时间
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class StudentVideoWatching extends Model
{
    /**
     * 关联的数据表
     *
     * @var string
     */
    protected $table = 'student_video_watching';

    protected $fillable = [
        'id', 'student_id', 'video_id', 'course_id', 'unit_id', 'start_time', 'last_position', 'watch_duration', 'completion_percentage', 'is_completed', 'completed_time', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    /**
     * 关联学生
     *
     * @return BelongsTo
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'student_id', 'id');
    }

    /**
     * 关联视频
     *
     * @return BelongsTo
     */
    public function video(): BelongsTo
    {
        return $this->belongsTo(VideoResource::class, 'video_id', 'id');
    }
}
