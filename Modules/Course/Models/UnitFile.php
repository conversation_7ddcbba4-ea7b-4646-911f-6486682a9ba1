<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * UnitFile模型类
 * 
 * 该模型用于管理课程单元相关的文件资源，包括文件标题、描述和类型等
 * 
 * @property int $id 文件ID
 * @property int $unit_id 单元ID
 * @property string $title 文件标题
 * @property string $description 文件描述
 * @property string $file_url 文件URL
 * @property string $file_type 文件类型
 * @property bool $is_required 是否必需
 * @property int $sort_order 排序顺序
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class UnitFile extends Model
{
    protected $table = 'unit_file';

    protected $fillable = [
        'id', 'unit_id', 'title', 'description', 'file_url', 'file_type', 'is_required', 'sort_order', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    protected $hidden = [
        'deleted_at',
        'created_at',
        'updated_at',
        'creator_id',
    ];

}
