<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * QuizQuestions模型类
 * 
 * 该模型用于管理课程测验中的题目信息，包括题目顺序、分数和必答性等
 * 
 * @property int $id 题目记录ID
 * @property int $unit_id 单元ID
 * @property int $student_id 学生ID
 * @property int $question_id 题目ID
 * @property int $sequence 题目顺序
 * @property float $score 题目分数
 * @property bool $is_required 是否必答
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $quiz_record_id 测验记录ID
 * @property int $deleted_at 删除时间（软删除）
 */
class QuizQuestions extends Model
{
    protected $table = 'quiz_questions';

    protected $fillable = [
        'id', 'unit_id', 'student_id', 'question_id', 'sequence', 'score', 'is_required', 'creator_id', 'created_at', 'updated_at', 'quiz_record_id', 'deleted_at', 
    ];

    protected $hidden = [
        'deleted_at',
        'created_at',
        'updated_at',
    ];
}
