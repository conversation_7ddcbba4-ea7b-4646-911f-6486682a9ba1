<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Auth\Authenticatable;
use Illuminate\Support\Facades\Hash;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;

/**
 * User模型类
 * 
 * 该模型用于管理系统用户信息，包括用户基本信息、联系方式和账户状态等
 * 
 * @property int $id 用户ID
 * @property string $account 账户名
 * @property string $password 密码
 * @property int $role_id 角色ID
 * @property string $first_name 名
 * @property string $last_name 姓
 * @property string $email 邮箱
 * @property string $show_email_type 邮箱显示类型
 * @property string $moodlenet_account Moodle网络账户
 * @property string $city_address 城市地址
 * @property string $country 国家
 * @property string $timezone 时区
 * @property string $introduction 个人介绍
 * @property string $code 用户代码
 * @property string $avatar_url 头像URL
 * @property string $avatar_description 头像描述
 * @property string $interests 兴趣爱好
 * @property string $phone_number 电话号码
 * @property string $mobile_number 手机号码
 * @property string $detailed_address 详细地址
 * @property string $department 部门
 * @property string $additional_last_name 附加姓
 * @property string $additional_first_name 附加名
 * @property string $additional_middle_name 附加中间名
 * @property string $additional_alias 附加别名
 * @property string $status 用户状态
 * @property string $last_login_time 最后登录时间
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class User extends Model implements AuthenticatableContract
{
    use Authenticatable;

    protected $table = 'user';

    protected $fillable = [
        'id', 'account', 'password', 'role_id', 'first_name', 'last_name', 'email', 'show_email_type', 'moodlenet_account', 'city_address', 'country', 'timezone', 'introduction', 'code', 'avatar_url', 'avatar_description', 'interests', 'phone_number', 'mobile_number', 'detailed_address', 'department', 'additional_last_name', 'additional_first_name', 'additional_middle_name', 'additional_alias', 'status', 'last_login_time', 'creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

    /**
     * 默认隐藏的敏感属性
     *
     * @var array
     */
    protected $hidden = [
        'password'
    ];

    /**
     * token过期时间（2小时，单位：秒）
     */
    const TOKEN_EXPIRED_TIME = 7200;

    const ALLOWED_UPDATE_FIELDS = [
        'show_email_type',      // 邮箱显示类型
        'moodlenet_account',    // Moodle网络账号
        'city_address',         // 城市地址
        'country',              // 国家
        'timezone',             // 时区
        'introduction',         // 个人简介
        'avatar_url',           // 头像URL
        'avatar_description',   // 头像描述
        'interests',            // 兴趣爱好
        'phone_number',         // 电话号码
        'mobile_number',        // 手机号码
        'detailed_address',     // 详细地址
        'department',           // 科系
        'additional_last_name', // 附加姓氏
        'additional_first_name',// 附加名字
        'additional_middle_name',// 附加中间名
        'additional_alias',     // 附加别名
        'status'                // 状态
    ];

    const SHOW_EMAIL_TYPE_PUBLIC = 1;      // 所有用户可见
    const SHOW_EMAIL_TYPE_HIDDEN = 2;   // 不显示
    const SHOW_EMAIL_TYPE_ADMIN = 3;    // 仅管理员可见

    const SHOW_EMAIL_TYPE_MAP = [
        self::SHOW_EMAIL_TYPE_PUBLIC => '所有用户可见',
        self::SHOW_EMAIL_TYPE_HIDDEN => '不显示',
        self::SHOW_EMAIL_TYPE_ADMIN => '仅管理员可见',
    ];


    /**
     * 获取用于身份验证的密码
     *
     * @return string
     */
    public function getAuthPassword()
    {
        return $this->password;
    }

    /**
     * 获取用于身份验证的用户名
     *
     * @return string
     */
    public function getAuthIdentifierName()
    {
        return 'account';
    }

    /**
     * 密码哈希
     *
     * @param string $password
     * @return string
     */
    public static function hashPassword($password,$defaultPassword = '123456')
    {
        if(empty($password)){
            $password = $defaultPassword;
        }
        return Hash::make($password);
    }

    /**
     * 获取用户参与的课程
     */
    public function courses()
    {
        return $this->belongsToMany(Course::class, 'course_user', 'user_id', 'course_id')
            ->where('course_user.deleted_at', 0)
            ->where('course.deleted_at', 0);
    }

    /**
     * 获取用户角色
     *
     * @return Role
     */
    public function role()
    {
        return $this->belongsTo(Role::class, 'role_id', 'id');
    }
}
