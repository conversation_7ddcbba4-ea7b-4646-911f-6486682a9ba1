<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * CourseUser模型类
 * 
 * 该模型用于管理课程与用户之间的关联关系，记录用户参与课程的信息
 * 
 * @property int $id 关联ID
 * @property int $user_id 用户ID
 * @property int $course_id 课程ID
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class CourseUser extends Model
{
    /**
     * 关联的数据表
     *
     * @var string
     */
    protected $table = 'course_user';

    protected $fillable = [
        'id', 'user_id', 'course_id', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

    /**
     * 关联用户
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联课程
     *
     * @return BelongsTo
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class, 'course_id', 'id');
    }
}
