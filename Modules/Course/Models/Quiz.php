<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * 测验模型类
 *
 * 该模型用于管理测验资源，包括测验标题、描述和所属分类等
 *
 * @property int $id 测验ID
 * @property int $course_id 所属课程ID
 * @property int $unit_id 所属单元ID
 * @property string $title 测验标题
 * @property string $description 测验描述
 * @property int $question_category_id 所属分类ID
 * @property int $question_num 题目数量
 * @property int $sort_order 顺序
 * @property string $type 类型
 * @property int $pass_score 及格分数
 * @property int $total_score 总分数
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间
 *
 */
class Quiz extends Model
{
    protected $table = 'quiz';

    protected $fillable = [
        'id', 'course_id', 'unit_id', 'title', 'description', 'question_category_id', 'question_num', 'sort_order', 'type', 'pass_score', 'total_score','creator_id', 'created_at', 'updated_at', 'deleted_at',
    ];

}
