<?php

namespace Modules\Course\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * 用户偏好模型
 * 
 * 该模型用于管理用户的个性化设置和偏好，包括但不限于：
 * - 语言偏好
 * - 讨论区设置
 * - 编辑器偏好
 * - 日历设置
 * - 资源库设置
 * - 消息通知设置
 * 
 * 每个用户可以有多个偏好设置，通过key-value形式存储
 * 
 * @property int $id 偏好ID
 * @property int $user_id 用户ID
 * @property string $key 偏好键名
 * @property string $value 偏好值 (json值)
 * @property string $description 偏好描述
 * @property int $creator_id 创建人ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间
 */

class UserPreference extends Model
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'user_preference';

    /**
     * 是否主动维护时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 日期字段存储格式
     * 
     * @var string
     */
    protected $dateFormat = 'U';

    /**
     * 可批量赋值属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'key',
        'value',
        'description',
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * 隐藏属性
     *
     * @var array
     */
    protected $hidden = [
        'creator_id',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * 应该被转换为原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'value' => 'array',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
        'deleted_at' => 'datetime:Y-m-d H:i:s'
    ];

    /**
     * 偏好键名常量
     */
    const KEY_LANGUAGE = 'language';                  // 语言偏好
    const KEY_DISCUSSION = 'discussion_preferences';  // 讨论区偏好
    const KEY_EDITOR = 'editor_preferences';          // 编辑器偏好
    const KEY_CALENDAR = 'calendar_preferences';      // 行事历偏好
    const KEY_RESOURCE = 'resource_preferences';      // 教材库设定偏好
    const KEY_MESSAGE = 'message_preferences';        // 讯息偏好

    /**
     * 语言偏好常量
     */
    const LANG_ZH_CN = 'zh_CN';  // 中文
    const LANG_EN_US = 'en_US';  // 英文
    
    /**
     * 讨论区偏好默认值
     */
    const DEFAULT_DISCUSSION_PREFERENCES = [
        'auto_subscribe' => 1,      // 1: 自动订阅, 0: 不自动订阅
        'email_digest_type' => 1,   // 1: 无, 2: 完整, 3: 主题
        'use_nested_discussions' => 0, // 1: 使用嵌套讨论视图, 0: 不使用
        'track_forums' => 1,        // 1: 跟踪讨论区, 0: 不跟踪
        'notify_on_post' => 1,      // 1: 标记为已读, 0: 不标记
    ];

    /**
     * 编辑器偏好值常量
     */
    const EDITOR_DEFAULT = '1';       // 预设编辑器
    const EDITOR_ATTO_HTML = '2';     // atto html
    const EDITOR_TINYMCE_HTML = '3';  // tinymce html
    const EDITOR_PLAIN_TEXT = '4';    // 纯文本

    /**
     * 行事历偏好默认值
     */
    const DEFAULT_CALENDAR_PREFERENCES = [
        'time_format' => 1,     // 1: 预设, 2: 24小時制, 3: 12小時制(上午/下午)
        'week_start' => 1,      // 0：星期日, 1：星期一，2：星期二，3：星期三，4：星期四，5：星期五，6：星期六
        'max_events' => 10,     // 最多顯示幾件即將來臨的事件 : 1-20次
        'show_days' => 1,       // 顯示幾日內即將來臨的事件 : 单位：天
        'remember_filters' => 0, // 記住過濾器的設定
    ];

    /**
     * 教材库设定偏好默认值
     */
    const DEFAULT_RESOURCE_PREFERENCES = [
        'default_content_visibility' => 1, // 1: public, 0: unlisted
    ];

    /**
     * 讯息偏好默认值
     */
    const DEFAULT_MESSAGE_PREFERENCES = [
        'who_can_send_message' => 2, // 1: 限通訊錄內, 2: 我的通訊錄和我的課程中所有人
        'notify_on_message' => [1],  // 1: Email, 2: 行動裝置
        'use_input_key' => 0,        // 1: 使用輸入鍵發送, 0: 不使用
    ];

    /**
     * 获取此偏好设置所属的用户
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 获取创建者用户
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'creator_id');
    }
}
