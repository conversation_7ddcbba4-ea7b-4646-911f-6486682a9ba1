<?php

namespace Modules\Course\Models;

use Bingo\Base\BingoModel as Model;

/**
 * StudentQuizAttempt模型类
 * 
 * 该模型用于记录学生参加课程测验的尝试记录和成绩
 * 
 * @property int $id 尝试记录ID
 * @property int $student_id 学生ID
 * @property int $course_id 课程ID
 * @property int $unit_id 单元ID
 * @property int $attempt_number 尝试次数
 * @property string $start_time 开始时间
 * @property string $end_time 结束时间
 * @property float $score 得分
 * @property int $total_questions 总题目数
 * @property string $quiz_content 测验内容（JSON格式）
 * @property string $status 测验状态
 * @property int $creator_id 创建者ID
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @property int $deleted_at 删除时间（软删除）
 */
class StudentQuizAttempt extends Model
{
    protected $table = 'student_quiz_attempt';

    protected $fillable = [
        'id', 'student_id', 'course_id', 'unit_id', 'attempt_number', 'start_time', 'end_time', 'score', 'total_questions', 'quiz_content', 'status', 'creator_id', 'created_at', 'updated_at', 'deleted_at', 
    ];

}
