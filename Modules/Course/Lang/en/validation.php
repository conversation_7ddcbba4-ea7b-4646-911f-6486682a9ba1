<?php

return [
    'login_required' => 'Please login first',
    'login_expired' => 'Login expired, please login again',
    'USER_LOGIN_FAILED' => 'Login failed',
    'USER_NOT_FOUND' => 'User not found',
    'ROLE_NOT_FOUND' => 'Role not found',
    'PERMISSION_NOT_FOUND' => 'Permission not found',
    'PERMISSION_TREE_GET_FAILED' => 'Failed to get permission tree',
    'USER_NOT_LOGIN' => 'User not logged in',
    'USER_LOGOUT_FAILED' => 'Logout failed',
    'USER_ALREADY_LOGIN' => 'User already logged in',
    'USER_LOGIN_EXPIRED' => 'Login expired',
    'USER_LOGIN_KICKED' => 'Your account is logged in on another device',
    'USER_OLD_PASSWORD_INCORRECT' => 'Old password is incorrect',
    'USER_PASSWORD_NOT_MATCH' => 'Passwords do not match',
    'USER_NO_PERMISSION' => 'No permission',
    'USER_PREFERENCE_NOT_FOUND' => 'User preference not found',
    'USER_PREFERENCE_GET_FAILED' => 'User preference get failed',
    'ROLE_SAVE_FAILED' => 'Role save failed',
    'USER_COURSE_MEMBERS_NOT_FOUND' => 'User course members not found',
    'USER_COURSE_MEMBERS_GET_FAILED' => 'User course members get failed',
    'TOKEN_INVALID' => 'Invalid token',
    'TOKEN_REFRESH_FAILED' => 'Token refresh failed',
    'TOKEN_EXPIRED' => 'Token has expired',
    'TOKEN_TYPE_INVALID' => 'Invalid token type',
    'TOKEN_NOT_FOUND' => 'Token not found',
    'UPLOAD_FILE_FAILED' => 'Failed to upload file',
    'SAVE_FAILED' => 'Save failed',
    'VIDEO_NOT_FOUND' => 'Video resource not found',
    'SUBMIT_TOO_FREQUENTLY' => 'Submitting too frequently, please try again later',
    'VIDEO_SAVE_FAILED' => 'Failed to save video progress',
    'VIDEO_PROGRESS_EXCEPTION' => 'Video playback progress is abnormal, please watch normally',
    'VIDEO_WATCHING_EXCEPTION' => 'Watching duration is abnormal, please watch video normally',
    'VIDEO_PLAY_SESSION_EXPIRED' => 'Playback session has expired, please refresh the page and start again',
    'VIDEO_PLAY_SESSION_NOT_MATCH' => 'Playback session does not match, please refresh the page and start again',
    'VIDEO_PLAY_SESSION_IP_CHANGE' => 'IP address change detected, please login again',
    'VIDEO_PLAY_SESSION_USER_AGENT_CHANGE' => 'Device information change detected, please login again',
    'VIDEO_PLAY_SESSION_REFERRER_CHANGE' => 'Please watch the video from the course page',
    'VIDEO_PLAY_SESSION_TIME_DIFF_TOO_LONG' => 'Playback time interval is abnormal, please watch again',
    'VIDEO_PLAY_SESSION_TIME_DIFF_TOO_SHORT' => 'Playback progress is abnormal, please watch the video normally',
    'FILE_INVALID' => 'Invalid file',
    'FILE_TYPE_INVALID' => 'Invalid file type',
    'FILE_EMPTY' => 'File is empty',
    'COURSE_USER_IMPORT_NOT_FOUND' => 'Course user import data not found',
    'USER_NOT_ROLE' => 'User has no role',
    'PERMISSION_DENIED' => 'No operation permission',
    'REFLECTION_NOT_FOUND' => 'Reflection report not found',
    'REFLECTION_FILE_NOT_FOUND' => 'Reflection report file not found',
    'SYSTEM_ERROR' => 'System error',
    'RESOURCE_NOT_FOUND' => 'Resource not found',
    'TRY_REFRESH_NON_CURRENT_TOKEN' => 'Attempt to refresh non-current token',
    'TRY_REFRESH_BLACKLIST_TOKEN' => 'Attempt to refresh blacklisted token',
    'TOKEN_REFRESH_TIMES_LIMIT' => 'Token refresh times limit exceeded',
    'TOKEN_REFRESH_INTERVAL_TOO_SHORT' => 'Token refresh interval too short',
    'INVALID_PARAMS' => 'Invalid parameters',
    'SUBMISSION_NOT_FOUND' => 'Submission not found',

    'user_login' => [
        'account' => [
            'required' => 'Account is required'
        ],
        'password' => [
            'required' => 'Password is required'
        ]
    ],
    'user_forget_password' => [
        'account' => [
            'required_without' => 'Account is required when email is not provided'
        ],
        'email' => [
            'required_without' => 'Email is required when account is not provided',
            'email' => 'Please enter a valid email address'
        ],
        'send_email' => [
            'boolean' => 'Send email must be a boolean value'
        ]
    ],
    'user_verify_reset_password_token' => [
        'token' => [
            'required' => 'Token is required'
        ]
    ],
    'user_reset_password' => [
        'id' => [
            'required' => 'User ID is required'
        ],
        'password' => [
            'required' => 'Password is required'
        ],
        'confirm_password' => [
            'required' => 'Confirm password is required'
        ],
        'token' => [
            'required' => 'Token is required'
        ]
    ],
    'user_update' => [
        'id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'show_email_type' => [
            'in' => 'Invalid email display type'
        ],
        'avatar_url' => [
            'url' => 'Please enter a valid avatar URL'
        ],
        'status' => [
            'in' => 'Invalid status value'
        ]
    ],
    'user_update_password' => [
        'id' => [
            'required' => 'User ID is required'
        ],
        'old_password' => [
            'required' => 'Old password is required'
        ],
        'password' => [
            'required' => 'New password is required'
        ],
        'confirm_password' => [
            'required' => 'Confirm password is required'
        ]
    ],
    'role_save' => [
        'name' => [
            'required' => 'Role name is required',
            'string' => 'Role name must be a string',
            'max' => 'Role name cannot exceed 50 characters'
        ],
        'status' => [
            'integer' => 'Status must be an integer',
            'in' => 'Invalid status value'
        ],
        'permissions' => [
            'array' => 'Permissions must be an array',
            '_integer' => 'Permissions must be an integer'
        ]
    ],
    'user_preference_get' => [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'key' => [
            'required' => 'Key is required',
            'string' => 'Key must be a string'
        ]
    ],
    'user_preference_update' => [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ],
        'key' => [
            'required' => 'Key is required',
            'string' => 'Key must be a string'
        ],
        'value' => [
            'required' => 'Value is required',
            'string' => 'Value must be a string'
        ]
    ],
    'user_preference_list' => [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be an integer'
        ]
    ],
    'user_course_members' => [
        'course_id' => [
            'required' => 'Course ID is required',
            'integer' => 'Course ID must be an integer'
        ],
        'first_initial' => [
            'string' => 'First initial must be a string',
            'max' => 'First initial cannot exceed 1 character'
        ],
        'last_initial' => [
            'string' => 'Last initial must be a string',
            'max' => 'Last initial cannot exceed 1 character'
        ],
        'keyword' => [
            'string' => 'Keyword must be a string',
            'max' => 'Keyword cannot exceed 50 characters'
        ],
        'role_id' => [
            'integer' => 'Role ID must be an integer'
        ],
        'page' => [
            'integer' => 'Page must be an integer'
        ],
        'limit' => [
            'integer' => 'Limit must be an integer'
        ]
    ],
    'unit_save' => [
        'course_id' => [
            'required' => 'Course ID is required',
            'exists' => 'Course ID does not exist'
        ],
        'units' => [
            'required' => 'Units are required',
            'array' => 'Units must be an array',
            'id' => [
                'nullable' => 'Unit ID can be null',
                'integer' => 'Unit ID must be an integer'
            ]
        ],
        'title_cn' => [
            'required' => 'Chinese title is required'
        ],
        'title_en' => [
            'required' => 'English title is required'
        ],
        'title' => [
            'required' => 'Title is required'
        ],
        'resources' => [
            'required' => 'Resources are required',
            'array' => 'Resources must be an array',
            'id' => [
                'nullable' => 'Resource ID can be null',
                'integer' => 'Resource ID must be an integer'
            ]
        ],
        'resource_type' => [
            'required' => 'Resource type is required',
            'invalid' => 'Invalid resource type'
        ],
        'resource_title' => [
            'required' => 'Resource title is required'
        ],
        'resource_url' => [
            'required' => 'Resource URL is required'
        ],
        'resource_sort_order' => [
            'required' => 'Resource sort order is required'
        ],
        'file_type' => [
            'required' => 'File type is required'
        ],
        'file_size' => [
            'required' => 'File size is required'
        ],
        'file_url' => [
            'required' => 'File URL is required'
        ],
        'duration' => [
            'required' => 'Duration is required'
        ],
        'allow_fast_forward' => [
            'required' => 'Allow fast forward option is required'
        ],
        'video_url' => [
            'required' => 'Video URL is required'
        ],
        'quiz_category_id' => [
            'required' => 'Quiz category ID is required',
            'integer' => 'Quiz category ID must be an integer',
            'exists' => 'Quiz category ID does not exist'
        ],
        'question_num' => [
            'required' => 'Question number is required',
            'integer' => 'Question number must be an integer',
            'min' => 'Question number must be greater than or equal to 0'
        ],
        'score_type' => [
            'required' => 'Score type is required',
            'string' => 'Score type must be a string'
        ],
        'pass_score' => [
            'required' => 'Pass score is required',
            'decimal' => 'Pass score must be a decimal',
            'min' => 'Pass score must be greater than or equal to 0'
        ],
        'total_score' => [
            'required' => 'Total score is required',
            'decimal' => 'Total score must be a decimal',
            'min' => 'Total score must be greater than or equal to 0',
            'integer' => 'Total score must be an integer'
        ]
    ],
    'unit_list' => [
        'course_id' => [
            'required' => 'Course ID is required',
            'integer' => 'Course ID must be an integer'
        ]
    ],
    'folder_save' => [
        'id' => [
            'integer' => 'Folder ID must be an integer',
            'exists' => 'Folder does not exist'
        ],
        'unit_id' => [
            'required' => 'Unit ID is required',
            'exists' => 'Unit does not exist'
        ],
        'title' => [
            'required' => 'Title is required',
            'string' => 'Title must be a string',
            'max' => 'Title cannot exceed 200 characters'
        ],
        'description' => [
            'string' => 'Description must be a string'
        ],
        'sort_order' => [
            'integer' => 'Sort order must be an integer',
            'min' => 'Sort order must be greater than or equal to 0'
        ],
        'children' => [
            'array' => 'Children must be an array',
            'id' => [
                'integer' => 'Child folder ID must be an integer',
                'exists' => 'Child folder does not exist'
            ],
            'title' => [
                'required' => 'Child folder title is required',
                'string' => 'Child folder title must be a string',
                'max' => 'Child folder title cannot exceed 200 characters'
            ],
            'description' => [
                'string' => 'Child folder description must be a string'
            ],
            'sort_order' => [
                'integer' => 'Child folder sort order must be an integer',
                'min' => 'Child folder sort order must be greater than or equal to 0'
            ],
            'resource_type' => [
                'required' => 'Child folder resource type is required',
                'in' => 'Invalid child folder resource type'
            ],
            'file_url' => [
                'required_if' => 'File URL is required when resource type is file',
                'string' => 'File URL must be a string',
                'max' => 'File URL cannot exceed 500 characters'
            ],
            'file_type' => [
                'required_if' => 'File type is required when resource type is file',
                'string' => 'File type must be a string',
                'max' => 'File type cannot exceed 50 characters'
            ],
            'children' => [
                'array' => 'Child folder children must be an array',
                'required_if' => 'Children are required when resource type is folder'
            ]
        ]
    ],
    'folder_tree' => [
        'root_directory_id' => [
            'required' => 'Root directory ID is required',
            'integer' => 'Root directory ID must be an integer'
        ]
    ],
    'resource_upload' => [
        'file' => [
            'required' => 'File is required',
            'file' => 'The uploaded file is invalid',
            'max' => 'The file size cannot exceed :size KB'
        ]
    ],
    'resource_save_video_progress' => [
        'student_id' => [
            'required' => 'Student ID is required',
            'integer' => 'Student ID must be an integer',
            'exists' => 'Student does not exist'
        ],
        'position' => [
            'required' => 'Playback position is required',
            'integer' => 'Playback position must be an integer'
        ],
        'duration' => [
            'required' => 'Watching duration is required',
            'integer' => 'Watching duration must be an integer'
        ],
        'course_id' => [
            'required' => 'Course ID is required',
            'integer' => 'Course ID must be an integer',
            'exists' => 'Course does not exist'
        ],
        'start_time' => [
            'required' => 'Start time is required',
            'date' => 'Start time format is incorrect'
        ]
    ],
    'unit_student_video_report' => [
        'page' => [
            'integer' => 'Page must be an integer'
        ],
        'limit' => [
            'integer' => 'Limit must be an integer'
        ],
        'first_initial' => [
            'string' => 'First initial must be a string'
        ],
        'last_initial' => [
            'string' => 'Last initial must be a string'
        ]
    ],
    'getCourseReflectionList' => [
        'page' => [
            'integer' => 'Page must be an integer',
            'min' => 'Page must be greater than or equal to 1'
        ],
        'limit' => [
            'integer' => 'Limit must be an integer',
            'min' => 'Limit must be greater than or equal to 1'
        ],
        'status' => [
            'integer' => 'Status must be an integer',
            'min' => 'Status must be greater than or equal to 0'
        ],
        'sort_field' => [
            'string' => 'Sort field must be a string'
        ],
        'sort_order' => [
            'string' => 'Sort order must be a string',
            'in' => 'Sort order must be asc or desc'
        ],
        'first_initial' => [
            'string' => 'First initial must be a string',
            'min' => 'First initial must be at least 1 character'
        ],
        'last_initial' => [
            'string' => 'Last initial must be a string',
            'min' => 'Last initial must be at least 1 character'
        ]
    ],
    'batchToggleLock' => [
        'ids' => [
            'required' => 'IDs are required',
            'array' => 'IDs must be an array',
            '*.required' => 'Each ID is required',
            '*.integer' => 'Each ID must be an integer'
        ]
    ],
    'batchDownload' => [
        'ids' => [
            'required' => 'IDs are required',
            'array' => 'IDs must be an array',
            '*.required' => 'Each ID is required',
            '*.integer' => 'Each ID must be an integer'
        ]
    ],
    'batchBackDraft' => [
        'ids' => [
            'required' => 'IDs are required',
            'array' => 'IDs must be an array',
            '*.required' => 'Each ID is required',
            '*.integer' => 'Each ID must be an integer'
        ]
    ],
    'batchDelaySubmit' => [
        'ids' => [
            'required' => 'IDs are required',
            'array' => 'IDs must be an array',
            '*.required' => 'Each ID is required',
            '*.integer' => 'Each ID must be an integer'
        ],
        'delay_days' => [
            'required' => 'Delay days are required',
            'date_format' => 'Delay days must be in the format Y-m-d H:i:s'
        ]
    ],
    'score' => [
        'id' => [
            'required' => 'ID is required',
            'integer' => 'ID must be an integer'
        ],
        'teacher_comment' => [
            'string' => 'Teacher comment must be a string'
        ],
        'score_result' => [
            'understanding' => [
                'is_passed' => [
                    'required' => 'Understanding pass status is required',
                    'integer' => 'Understanding pass status must be an integer',
                    'in' => 'Understanding pass status must be 0 or 1'
                ],
                'comment' => [
                    'string' => 'Understanding comment must be a string'
                ]
            ],
            'cognition' => [
                'is_passed' => [
                    'required' => 'Cognition pass status is required',
                    'integer' => 'Cognition pass status must be an integer',
                    'in' => 'Cognition pass status must be 0 or 1'
                ],
                'comment' => [
                    'string' => 'Cognition comment must be a string'
                ]
            ],
            'discussion' => [
                'is_passed' => [
                    'required' => 'Discussion pass status is required',
                    'integer' => 'Discussion pass status must be an integer',
                    'in' => 'Discussion pass status must be 0 or 1'
                ],
                'comment' => [
                    'string' => 'Discussion comment must be a string'
                ]
            ],
            'writing' => [
                'is_passed' => [
                    'required' => 'Writing pass status is required',
                    'integer' => 'Writing pass status must be an integer',
                    'in' => 'Writing pass status must be 0 or 1'
                ],
                'comment' => [
                    'string' => 'Writing comment must be a string'
                ]
            ]
        ]
    ],
    'resource_video_info' => [
        'with_progress' => [
            'boolean' => 'With progress must be a boolean value'
        ],
        'student_id' => [
            'required_if' => 'Student ID is required when with progress is true',
            'integer' => 'Student ID must be an integer',
            'exists' => 'Student does not exist'
        ],
        'unit_id' => [
            'required_if' => 'Unit ID is required when with progress is true',
            'integer' => 'Unit ID must be an integer',
            'exists' => 'Unit does not exist'
        ]
    ],
    'folder_root' => [
        'page' => [
            'integer' => 'Page must be an integer'
        ],
        'limit' => [
            'integer' => 'Limit must be an integer'
        ],
        'unit_id' => [
            'integer' => 'Unit ID must be an integer'
        ]
    ],
    'reflection_report' => [
        'understanding' => 'Understanding',
        'cognition' => 'Cognition',
        'discussion' => 'Discussion',
        'writing' => 'Writing',
        'understanding_level_1' => 'Limited understanding or misunderstanding of relevant theories and topics',
        'understanding_level_2' => 'Demonstrates sufficient understanding of relevant theories and topics',
        'cognition_level_1' => 'Fails to demonstrate any analysis, critical thinking, or synthesis abilities',
        'cognition_level_2' => 'Demonstrates reasonable analysis, critical thinking, and synthesis abilities',
        'discussion_level_1' => 'Discussion is disorganized, lacks logic and reasoning',
        'discussion_level_2' => 'Discussion is clear, with logical flow and reasoning',
        'writing_level_1' => 'Writing is unclear and difficult to understand',
        'writing_level_2' => 'Clear structure, accurate expression, and fluent writing'
    ],
    'resource_update_video' => [
        'title' => [
            'string' => 'Title must be a string'
        ],
        'description' => [
            'string' => 'Description must be a string'
        ],
        'video_url' => [
            'string' => 'Video URL must be a string'
        ],
        'duration' => [
            'integer' => 'Duration must be an integer'
        ],
        'allow_fast_forward' => [
            'boolean' => 'Allow fast forward must be a boolean value'
        ]
    ],
    'resource_update_file' => [
        'title' => [
            'string' => 'Title must be a string',
            'max' => 'Title cannot exceed 200 characters'
        ],
        'description' => [
            'string' => 'Description must be a string'
        ],
        'file_url' => [
            'string' => 'File URL must be a string',
            'max' => 'File URL cannot exceed 500 characters'
        ],
        'file_type' => [
            'string' => 'File type must be a string',
            'max' => 'File type cannot exceed 50 characters'
        ],
        'is_required' => [
            'boolean' => 'Is required must be a boolean value'
        ],
        'sort_order' => [
            'integer' => 'Sort order must be an integer',
            'min' => 'Sort order must be greater than or equal to 0'
        ]
    ],
    'resource_update_folder' => [
        'title' => [
            'string' => 'Title must be a string',
            'max' => 'Title cannot exceed 200 characters'
        ],
        'description' => [
            'string' => 'Description must be a string'
        ],
        'order_sort' => [
            'integer' => 'Sort order must be an integer',
            'min' => 'Sort order must be greater than or equal to 0'
        ]
    ],
    'resource_update_question' => [
        'title' => [
            'string' => 'Title must be a string',
            'max' => 'Title cannot exceed 200 characters'
        ],
        'description' => [
            'string' => 'Description must be a string'
        ],
        'question_category_id' => [
            'integer' => 'Question category ID must be an integer',
            'exists' => 'Question category does not exist'
        ],
        'question_num' => [
            'integer' => 'Question number must be an integer',
            'min' => 'Question number must be greater than or equal to 1'
        ],
        'type' => [
            'string' => 'Type must be a string',
            'max' => 'Type cannot exceed 200 characters'
        ],
        'pass_score' => [
            'numeric' => 'Pass score must be a number',
            'min' => 'Pass score must be greater than or equal to 0'
        ],
        'total_score' => [
            'numeric' => 'Total score must be a number',
            'min' => 'Total score must be greater than or equal to 0'
        ]
    ],
    'resource_update_reflection' => [
        'title' => [
            'string' => 'Title must be a string',
            'max' => 'Title cannot exceed 200 characters'
        ],
        'description' => [
            'string' => 'Description must be a string'
        ],
        'is_hidden' => [
            'boolean' => 'Is hidden must be a boolean value'
        ],
        'start_date' => [
            'date' => 'Start date must be a valid date'
        ],
        'end_date' => [
            'date' => 'End date must be a valid date',
            'after_or_equal' => 'End date must be after or equal to start date'
        ],
        'total_score' => [
            'numeric' => 'Total score must be a number',
            'min' => 'Total score must be greater than or equal to 0',
            'max' => 'Total score cannot exceed 100'
        ],
        'sort_order' => [
            'integer' => 'Sort order must be an integer',
            'min' => 'Sort order must be greater than or equal to 0'
        ]
    ]
];
