<?php

return [
    'login_required' => '請先登錄',
    'login_expired' => '登錄已過期，請重新登錄',
    'USER_LOGIN_FAILED' => '用戶登錄失敗',
    'USER_NOT_FOUND' => '用戶不存在',
    'ROLE_NOT_FOUND' => '角色不存在',
    'PERMISSION_NOT_FOUND' => '權限不存在',
    'PERMISSION_TREE_GET_FAILED' => '權限樹獲取失敗',
    'USER_NOT_LOGIN' => '用戶未登錄',
    'USER_LOGOUT_FAILED' => '用戶登出失敗',
    'USER_ALREADY_LOGIN' => '用戶已登錄',
    'USER_LOGIN_EXPIRED' => '登錄已過期',
    'USER_LOGIN_KICKED' => '賬號在其他設備登錄',
    'USER_OLD_PASSWORD_INCORRECT' => '舊密碼不正確',
    'USER_PASSWORD_NOT_MATCH' => '新密碼和確認密碼不匹配',
    'USER_NO_PERMISSION' => '無權限',
    'USER_PREFERENCE_GET_FAILED' => '用戶偏好獲取失敗',
    'USER_PREFERENCE_NOT_FOUND' => '用戶偏好設置不存在',
    'ROLE_SAVE_FAILED' => '角色保存失敗',
    'USER_COURSE_MEMBERS_NOT_FOUND' => '用戶課程成員不存在',
    'USER_COURSE_MEMBERS_GET_FAILED' => '用戶課程成員獲取失敗',
    'TOKEN_INVALID' => '無效的令牌',
    'TOKEN_REFRESH_FAILED' => '刷新令牌失敗',
    'TOKEN_EXPIRED' => '令牌已過期',
    'TOKEN_TYPE_INVALID' => '令牌類型錯誤',
    'TOKEN_NOT_FOUND' => '令牌不存在',
    'UPLOAD_FILE_FAILED' => '上傳文件失敗',
    'SAVE_FAILED' => '保存失敗',
    'VIDEO_NOT_FOUND' => '視頻資源不存在',
    'SUBMIT_TOO_FREQUENTLY' => '提交過於頻繁，請稍後再試',
    'VIDEO_SAVE_FAILED' => '保存視頻進度失敗',
    'VIDEO_PROGRESS_EXCEPTION' => '視頻播放進度異常，請正常觀看',
    'VIDEO_WATCHING_EXCEPTION' => '觀看時長異常，請正常觀看視頻',
    'VIDEO_PLAY_SESSION_EXPIRED' => '播放會話已過期，請刷新頁面重新開始播放',
    'VIDEO_PLAY_SESSION_NOT_MATCH' => '播放會話不匹配，請刷新頁面重新開始播放',
    'VIDEO_PLAY_SESSION_IP_CHANGE' => '檢測到IP變更，請重新登錄',
    'VIDEO_PLAY_SESSION_USER_AGENT_CHANGE' => '檢測到設備信息變更，請重新登錄',
    'VIDEO_PLAY_SESSION_REFERRER_CHANGE' => '請從課程頁面觀看視頻',
    'VIDEO_PLAY_SESSION_TIME_DIFF_TOO_LONG' => '播放時間間隔異常，請重新觀看',
    'VIDEO_PLAY_SESSION_TIME_DIFF_TOO_SHORT' => '播放進度異常，請正常觀看視頻',
    'FILE_INVALID' => '文件無效',
    'FILE_TYPE_INVALID' => '文件類型無效',
    'FILE_EMPTY' => '文件為空',
    'COURSE_USER_IMPORT_NOT_FOUND' => '課程用戶導入數據不存在',
    'USER_NOT_ROLE' => '用戶沒有角色',
    'PERMISSION_DENIED' => '沒有操作權限',
    'REFLECTION_NOT_FOUND' => '反思報告不存在',
    'REFLECTION_FILE_NOT_FOUND' => '反思報告文件不存在',
    'SYSTEM_ERROR' => '系統錯誤',
    'RESOURCE_NOT_FOUND' => '資源不存在',
    'TRY_REFRESH_NON_CURRENT_TOKEN' => '嘗試刷新非當前令牌',
    'TRY_REFRESH_BLACKLIST_TOKEN' => '嘗試刷新黑名單中的令牌',
    'TOKEN_REFRESH_TIMES_LIMIT' => '令牌刷新次數超過限制',
    'TOKEN_REFRESH_INTERVAL_TOO_SHORT' => '令牌刷新間隔過短',
    'INVALID_PARAMS' => '參數錯誤',
    'SUBMISSION_NOT_FOUND' => '提交記錄不存在',

    'user_login' => [
        'account' => [
            'required' => '賬號不能為空'
        ],
        'password' => [
            'required' => '密碼不能為空'
        ]
    ],
    'user_forget_password' => [
        'account' => [
            'required_without' => '賬號不能為空'
        ],
        'email' => [
            'required_without' => '郵箱不能為空',
            'email' => '請輸入正確的郵箱地址'
        ],
        'send_email' => [
            'boolean' => '發送郵件必須為布爾值'
        ]
    ],
    'user_verify_reset_password_token' => [
        'token' => [
            'required' => '令牌不能為空'
        ]
    ],
    'user_reset_password' => [
        'id' => [
            'required' => '用戶ID不能為空'
        ],
        'password' => [
            'required' => '密碼不能為空'
        ],
        'confirm_password' => [
            'required' => '確認密碼不能為空'
        ],
        'token' => [
            'required' => '令牌不能為空'
        ]
    ],
    'user_update' => [
        'id' => [
            'required' => '用戶ID不能為空',
            'integer' => '用戶ID必須為整數'
        ],
        'show_email_type' => [
            'in' => '郵箱顯示類型無效'
        ],
        'avatar_url' => [
            'url' => '請輸入正確的頭像URL'
        ],
        'status' => [
            'in' => '狀態值無效'
        ]
    ],
    'user_update_password' => [
        'id' => [
            'required' => '用戶ID不能為空'
        ],
        'old_password' => [
            'required' => '舊密碼不能為空'
        ],
        'password' => [
            'required' => '新密碼不能為空'
        ],
        'confirm_password' => [
            'required' => '確認密碼不能為空'
        ]
    ],
    'role_save' => [
        'name' => [
            'required' => '角色名稱不能為空',
            'string' => '角色名稱必須是字符串',
            'max' => '角色名稱不能超過50個字符'
        ],
        'status' => [
            'integer' => '狀態必須是整數',
            'in' => '狀態值無效'
        ],
        'permissions' => [
            'array' => '權限必須是數組',
            '_integer' => '權限必須是整數'
        ]
    ],
    'user_preference_get' => [
        'user_id' => [
            'required' => '用戶ID不能為空',
            'integer' => '用戶ID必須為整數'
        ],
        'key' => [
            'required' => '鍵名不能為空',
            'string' => '鍵名必須為字符串'
        ]
    ],
    'user_preference_update' => [
        'user_id' => [
            'required' => '用戶ID不能為空',
            'integer' => '用戶ID必須為整數'
        ],
        'key' => [
            'required' => '鍵名不能為空',
            'string' => '鍵名必須為字符串'
        ],
        'value' => [
            'required' => '值不能為空',
            'string' => '值必須為字符串'
        ]
    ],
    'user_preference_list' => [
        'user_id' => [
            'required' => '用戶ID不能為空',
            'integer' => '用戶ID必須為整數'
        ]
    ],
    'user_course_members' => [
        'course_id' => [
            'required' => '課程ID不能為空',
            'integer' => '課程ID必須為整數'
        ],
        'first_initial' => [
            'string' => '名字首字母必須為字符串',
            'max' => '名字首字母不能超過1個字符'
        ],
        'last_initial' => [
            'string' => '姓氏首字母必須為字符串',
            'max' => '姓氏首字母不能超過1個字符'
        ],
        'keyword' => [
            'string' => '關鍵詞必須為字符串',
            'max' => '關鍵詞不能超過50個字符'
        ],
        'role_id' => [
            'integer' => '角色ID必須為整數'
        ],
        'page' => [
            'integer' => '頁碼必須為整數'
        ],
        'limit' => [
            'integer' => '每頁條數必須為整數'
        ]
    ],
    'unit_save' => [
        'course_id' => [
            'required' => '課程ID不能為空',
            'exists' => '課程不存在'
        ],
        'units' => [
            'required' => '單元不能為空',
            'array' => '單元必須為數組',
            'id' => [
                'nullable' => '單元ID可以為空',
                'integer' => '單元ID必須為整數'
            ]
        ],
        'title_cn' => [
            'required' => '中文標題不能為空'
        ],
        'title_en' => [
            'required' => '英文標題不能為空'
        ],
        'title' => [
            'required' => '標題不能為空'
        ],
        'resources' => [
            'required' => '資源不能為空',
            'array' => '資源必須為數組',
            'id' => [
                'nullable' => '資源ID可以為空',
                'integer' => '資源ID必須為整數'
            ]
        ],
        'resource_type' => [
            'required' => '資源類型不能為空',
            'invalid' => '資源類型無效'
        ],
        'resource_title' => [
            'required' => '資源標題不能為空'
        ],
        'resource_url' => [
            'required' => '資源URL不能為空'
        ],
        'resource_sort_order' => [
            'required' => '資源排序不能為空'
        ],
        'file_type' => [
            'required' => '文件類型不能為空'
        ],
        'file_size' => [
            'required' => '文件大小不能為空'
        ],
        'file_url' => [
            'required' => '文件URL不能為空'
        ],
        'duration' => [
            'required' => '視頻時長不能為空'
        ],
        'allow_fast_forward' => [
            'required' => '允許快進選項不能為空'
        ],
        'video_url' => [
            'required' => '視頻URL不能為空'
        ],
        'quiz_category_id' => [
            'required' => '測驗分類ID不能為空',
            'integer' => '測驗分類ID必須為整數',
            'exists' => '測驗分類ID不存在'
        ],
        'question_num' => [
            'required' => '題目數量不能為空',
            'integer' => '題目數量必須為整數',
            'min' => '題目數量必須大於等於0'
        ],
        'score_type' => [
            'required' => '得分類型不能為空',
            'string' => '得分類型必須為字符串'
        ],
        'pass_score' => [
            'required' => '及格分數不能為空',
            'decimal' => '及格分數必須為小數',
            'min' => '及格分數必須大於等於0'
        ],
        'total_score' => [
            'required' => '總分數不能為空',
            'decimal' => '總分數必須為小數',
            'min' => '總分數必須大於等於0',
            'integer' => '總分數必須為整數'
        ]
    ],
    'unit_list' => [
        'course_id' => [
            'required' => '課程ID不能為空',
            'integer' => '課程ID必須為整數'
        ]
    ],
    'folder_save' => [
        'id' => [
            'integer' => '文件夾ID必須為整數',
            'exists' => '文件夾不存在'
        ],
        'unit_id' => [
            'required' => '單元ID不能為空',
            'exists' => '單元不存在'
        ],
        'title' => [
            'required' => '標題不能為空',
            'string' => '標題必須為字符串',
            'max' => '標題不能超過200個字符'
        ],
        'description' => [
            'string' => '描述必須為字符串'
        ],
        'sort_order' => [
            'integer' => '排序必須為整數',
            'min' => '排序必須大於等於0'
        ],
        'children' => [
            'array' => '子項必須為數組',
            'id' => [
                'integer' => '子文件夾ID必須為整數',
                'exists' => '子文件夾不存在'
            ],
            'title' => [
                'required' => '子文件夾標題不能為空',
                'string' => '子文件夾標題必須為字符串',
                'max' => '子文件夾標題不能超過200個字符'
            ],
            'description' => [
                'string' => '子文件夾描述必須為字符串'
            ],
            'sort_order' => [
                'integer' => '子文件夾排序必須為整數',
                'min' => '子文件夾排序必須大於等於0'
            ],
            'resource_type' => [
                'required' => '子文件夾資源類型不能為空',
                'in' => '子文件夾資源類型無效'
            ],
            'file_url' => [
                'required_if' => '當資源類型為文件時，文件URL不能為空',
                'string' => '文件URL必須為字符串',
                'max' => '文件URL不能超過500個字符'
            ],
            'file_type' => [
                'required_if' => '當資源類型為文件時，文件類型不能為空',
                'string' => '文件類型必須為字符串',
                'max' => '文件類型不能超過50個字符'
            ],
            'children' => [
                'array' => '子文件夾子項必須為數組',
                'required_if' => '當資源類型為文件夾時，子項不能為空'
            ]
        ]
    ],
    'folder_tree' => [
        'root_directory_id' => [
            'required' => '根目錄ID不能為空',
            'integer' => '根目錄ID必須為整數'
        ]
    ],
    'resource_upload' => [
        'file' => [
            'required' => '請選擇要上傳的文件',
            'file' => '上傳的文件無效',
            'max' => '文件大小不能超過 :size KB'
        ]
    ],
    'resource_save_video_progress' => [
        'student_id' => [
            'required' => '學生ID不能為空',
            'integer' => '學生ID必須為整數',
            'exists' => '學生不存在'
        ],
        'position' => [
            'required' => '播放位置不能為空',
            'integer' => '播放位置必須為整數'
        ],
        'duration' => [
            'required' => '觀看時長不能為空',
            'integer' => '觀看時長必須為整數'
        ],
        'course_id' => [
            'required' => '課程ID不能為空',
            'integer' => '課程ID必須為整數',
            'exists' => '課程不存在'
        ],
        'start_time' => [
            'required' => '開始時間不能為空',
            'date' => '開始時間格式不正確'
        ]
    ],
    'unit_student_video_report' => [
        'page' => [
            'integer' => '頁碼必須為整數'
        ],
        'limit' => [
            'integer' => '每頁條數必須為整數'
        ],
        'first_initial' => [
            'string' => '名字首字母必須為字符串'
        ],
        'last_initial' => [
            'string' => '姓氏首字母必須為字符串'
        ]
    ],
    'getCourseReflectionList' => [
        'page' => [
            'integer' => '頁碼必須為整數',
            'min' => '頁碼必須大於等於1'
        ],
        'limit' => [
            'integer' => '每頁條數必須為整數',
            'min' => '每頁條數必須大於等於1'
        ],
        'status' => [
            'integer' => '狀態必須為整數',
            'min' => '狀態必須大於等於0'
        ],
        'sort_field' => [
            'string' => '排序字段必須為字符串'
        ],
        'sort_order' => [
            'string' => '排序方式必須為字符串',
            'in' => '排序方式必須為asc或desc'
        ],
        'first_initial' => [
            'string' => '名字首字母必須為字符串',
            'min' => '名字首字母至少需要1個字符'
        ],
        'last_initial' => [
            'string' => '姓氏首字母必須為字符串',
            'min' => '姓氏首字母至少需要1個字符'
        ]
    ],
    'batchToggleLock' => [
        'ids' => [
            'required' => 'ID列表不能為空',
            'array' => 'ID必須為數組',
            '*.required' => '每個ID不能為空',
            '*.integer' => '每個ID必須為整數'
        ]
    ],
    'batchDownload' => [
        'ids' => [
            'required' => 'ID列表不能為空',
            'array' => 'ID必須為數組',
            '*.required' => '每個ID不能為空',
            '*.integer' => '每個ID必須為整數'
        ]
    ],
    'batchBackDraft' => [
        'ids' => [
            'required' => 'ID列表不能為空',
            'array' => 'ID必須為數組',
            '*.required' => '每個ID不能為空',
            '*.integer' => '每個ID必須為整數'
        ]
    ],
    'batchDelaySubmit' => [
        'ids' => [
            'required' => 'ID列表不能為空',
            'array' => 'ID必須為數組',
            '*.required' => '每個ID不能為空',
            '*.integer' => '每個ID必須為整數'
        ],
        'delay_days' => [
            'required' => '延期天數不能為空',
            'date_format' => '延期日期的格式必須為Y-m-d H:i:s'
        ]
    ],
    'score' => [
        'id' => [
            'required' => 'ID不能為空',
            'integer' => 'ID必須為整數'
        ],
        'teacher_comment' => [
            'string' => '教師評語必須為字符串'
        ],
        'score_result' => [
            'understanding' => [
                'is_passed' => [
                    'required' => '理解能力通過狀態不能為空',
                    'integer' => '理解能力通過狀態必須為整數',
                    'in' => '理解能力通過狀態必須為0或1'
                ],
                'comment' => [
                    'string' => '理解能力評語必須為字符串'
                ]
            ],
            'cognition' => [
                'is_passed' => [
                    'required' => '認知能力通過狀態不能為空',
                    'integer' => '認知能力通過狀態必須為整數',
                    'in' => '認知能力通過狀態必須為0或1'
                ],
                'comment' => [
                    'string' => '認知能力評語必須為字符串'
                ]
            ],
            'discussion' => [
                'is_passed' => [
                    'required' => '討論能力通過狀態不能為空',
                    'integer' => '討論能力通過狀態必須為整數',
                    'in' => '討論能力通過狀態必須為0或1'
                ],
                'comment' => [
                    'string' => '討論能力評語必須為字符串'
                ]
            ],
            'writing' => [
                'is_passed' => [
                    'required' => '寫作能力通過狀態不能為空',
                    'integer' => '寫作能力通過狀態必須為整數',
                    'in' => '寫作能力通過狀態必須為0或1'
                ],
                'comment' => [
                    'string' => '寫作能力評語必須為字符串'
                ]
            ]
        ]
    ],
    'resource_video_info' => [
        'with_progress' => [
            'boolean' => '是否包含進度必須為布爾值'
        ],
        'student_id' => [
            'required_if' => '當需要包含進度時，學生ID不能為空',
            'integer' => '學生ID必須為整數',
            'exists' => '學生不存在'
        ],
        'unit_id' => [
            'required_if' => '當需要包含進度時，單元ID不能為空',
            'integer' => '單元ID必須為整數',
            'exists' => '單元不存在'
        ]
    ],
    'folder_root' => [
        'page' => [
            'integer' => '頁碼必須為整數'
        ],
        'limit' => [
            'integer' => '每頁條數必須為整數'
        ],
        'unit_id' => [
            'integer' => '單元ID必須為整數'
        ]
    ],
    'reflection_report' => [
        'understanding' => '理解能力',
        'cognition' => '認知能力',
        'discussion' => '討論能力',
        'writing' => '寫作能力',
        'understanding_level_1' => '對相關理論和題目不甚理解/誤解',
        'understanding_level_2' => '對相關理論和題目展示足夠的理解',
        'cognition_level_1' => '未能展示任何分析、批判性思維及綜合能力',
        'cognition_level_2' => '展示合理的分析、批判性思維及綜合能力',
        'discussion_level_1' => '討論散亂、欠缺邏輯及理據',
        'discussion_level_2' => '討論清晰、具合理思路及理據',
        'writing_level_1' => '文不達意，難於理解',
        'writing_level_2' => '結構清晰、表達準確、文筆流暢'
    ],
    'resource_update_video' => [
        'title' => [
            'string' => '標題必須為字符串'
        ],
        'description' => [
            'string' => '描述必須為字符串'
        ],
        'video_url' => [
            'string' => '視頻URL必須為字符串'
        ],
        'duration' => [
            'integer' => '時長必須為整數'
        ],
        'allow_fast_forward' => [
            'boolean' => '允許快進必須為布爾值'
        ]
    ],
    'resource_update_file' => [
        'title' => [
            'string' => '標題必須為字符串',
            'max' => '標題不能超過200個字符'
        ],
        'description' => [
            'string' => '描述必須為字符串'
        ],
        'file_url' => [
            'string' => '文件URL必須為字符串',
            'max' => '文件URL不能超過500個字符'
        ],
        'file_type' => [
            'string' => '文件類型必須為字符串',
            'max' => '文件類型不能超過50個字符'
        ],
        'is_required' => [
            'boolean' => '是否必填必須為布爾值'
        ],
        'sort_order' => [
            'integer' => '排序必須為整數',
            'min' => '排序必須大於等於0'
        ]
    ],
    'resource_update_folder' => [
        'title' => [
            'string' => '標題必須為字符串',
            'max' => '標題不能超過200個字符'
        ],
        'description' => [
            'string' => '描述必須為字符串'
        ],
        'order_sort' => [
            'integer' => '排序必須為整數',
            'min' => '排序必須大於等於0'
        ]
    ],
    'resource_update_question' => [
        'title' => [
            'string' => '標題必須為字符串',
            'max' => '標題不能超過200個字符'
        ],
        'description' => [
            'string' => '描述必須為字符串'
        ],
        'question_category_id' => [
            'integer' => '題目分類ID必須為整數',
            'exists' => '題目分類不存在'
        ],
        'question_num' => [
            'integer' => '題目數量必須為整數',
            'min' => '題目數量必須大於等於1'
        ],
        'type' => [
            'string' => '類型必須為字符串',
            'max' => '類型不能超過200個字符'
        ],
        'pass_score' => [
            'numeric' => '及格分數必須為數字',
            'min' => '及格分數必須大於等於0'
        ],
        'total_score' => [
            'numeric' => '總分必須為數字',
            'min' => '總分必須大於等於0'
        ]
    ],
    'resource_update_reflection' => [
        'title' => [
            'string' => '標題必須為字符串',
            'max' => '標題不能超過200個字符'
        ],
        'description' => [
            'string' => '描述必須為字符串'
        ],
        'is_hidden' => [
            'boolean' => '是否隱藏必須為布爾值'
        ],
        'start_date' => [
            'date' => '開始日期必須是有效的日期'
        ],
        'end_date' => [
            'date' => '結束日期必須是有效的日期',
            'after_or_equal' => '結束日期必須大於等於開始日期'
        ],
        'total_score' => [
            'numeric' => '總分必須為數字',
            'min' => '總分必須大於等於0',
            'max' => '總分不能超過100'
        ],
        'sort_order' => [
            'integer' => '排序必須為整數',
            'min' => '排序必須大於等於0'
        ]
    ],
    'reflection_send_report' => [
        'courseId' => [
            'required' => '課程ID不能為空',
            'numeric' => '課程ID必須為數字'
        ],
        'assignmentName' => [
            'required' => '作業名稱不能為空',
            'string' => '作業名稱必須為字符串'
        ],
        'submissionId' => [
            'required' => '提交ID不能為空',
            'numeric' => '提交ID必須為數字'
        ],
        'cmid' => [
            'required' => 'CMID不能為空',
            'numeric' => 'CMID必須為數字'
        ],
        'FileName' => [
            'required' => '文件名不能為空',
            'string' => '文件名必須為字符串'
        ],
        'FileCheckSum' => [
            'required' => '文件校驗和不能為空',
            'string' => '文件校驗和必須為字符串'
        ],
        'studentId' => [
            'required' => '學生ID不能為空',
            'numeric' => '學生ID必須為數字'
        ],
        'FilePath' => [
            'required' => '文件路徑不能為空',
            'file' => '文件路徑必須為有效文件'
        ]
    ],
    'reflection_add' => [
        'reflection_id' => [
            'required' => '反思報告ID不能為空',
            'integer' => '反思報告ID必須為整數',
            'exists' => '反思報告不存在'
        ],
        'file_url' => [
            'required' => '文件URL不能為空',
            'string' => '文件URL必須為字符串',
            'max' => '文件URL不能超過500個字符'
        ]
    ],
    'getCourseReflectionList' => [
        'page' => [
            'integer' => '頁碼必須為整數',
            'min' => '頁碼必須大於0'
        ],
        'limit' => [
            'integer' => '每頁條數必須為整數',
            'min' => '每頁條數必須大於0'
        ],
        'status' => [
            'integer' => '狀態必須為整數'
        ],
        'sort_field' => [
            'string' => '排序字段必須為字符串'
        ],
        'sort_order' => [
            'string' => '排序方向必須為字符串',
            'in' => '排序方向只能為asc或desc'
        ],
        'first_initial' => [
            'string' => '名字首字母必須為字符串',
            'min' => '名字首字母至少1個字符'
        ],
        'last_initial' => [
            'string' => '姓氏首字母必須為字符串',
            'min' => '姓氏首字母至少1個字符'
        ]
    ],
    'batchToggleLock' => [
        'ids' => [
            'required' => 'ID列表不能為空',
            'array' => 'ID列表必須為數組'
        ],
        'ids.*' => [
            'required' => 'ID不能為空',
            'integer' => 'ID必須為整數'
        ]
    ]
];