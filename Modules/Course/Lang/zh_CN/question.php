<?php

return [
    // Question validation messages
    'validation' => [
        'category_id' => [
            'required' => '分类ID不能为空',
            'integer' => '分类ID必须是整数',
        ],
        'question_name' => [
            'required' => '题目名称不能为空',
            'max' => '题目名称不能超过255个字符',
        ],
        'question_text' => [
            'required' => '题目内容不能为空',
        ],
        'choices' => [
            'required' => '选项不能为空',
            'array' => '选项必须是数组',
            'min' => '至少需要一个选项',
        ],
        'choices.*.text' => [
            'required' => '选项文本不能为空',
        ],
        'choices.*.is_correct' => [
            'required' => '必须指定选项是否正确',
            'boolean' => '选项是否正确必须是布尔值',
        ],
        'choices.*.grade' => [
            'required' => '选项分值不能为空',
            'numeric' => '选项分值必须是数字',
        ],
    ],
    
    // API response messages
    'api' => [
        'add_success' => '题目添加成功',
        'add_failure' => '题目添加失败',
        'validation_failed' => '参数验证失败',
    ],
];
