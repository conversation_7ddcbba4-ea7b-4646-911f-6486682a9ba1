<?php

return [
    'login_required' => '请先登录',
    'login_expired' => '登录已过期，请重新登录',
    'USER_LOGIN_FAILED' => '用户登录失败',
    'USER_NOT_FOUND' => '用户不存在',
    'ROLE_NOT_FOUND' => '角色不存在',
    'PERMISSION_NOT_FOUND' => '权限不存在',
    'PERMISSION_TREE_GET_FAILED' => '权限树获取失败',
    'USER_NOT_LOGIN' => '用户未登录',
    'USER_LOGOUT_FAILED' => '用户登出失败',
    'USER_ALREADY_LOGIN' => '用户已登录',
    'USER_LOGIN_EXPIRED' => '登录已过期',
    'USER_LOGIN_KICKED' => '账号在其他设备登录',
    'USER_OLD_PASSWORD_INCORRECT' => '旧密码不正确',
    'USER_PASSWORD_NOT_MATCH' => '新密码和确认密码不匹配',
    'USER_NO_PERMISSION' => '无权限',
    'USER_PREFERENCE_GET_FAILED' => '用户偏好获取失败',
    'USER_PREFERENCE_NOT_FOUND' => '用户偏好设置不存在',
    'ROLE_SAVE_FAILED' => '角色保存失败',
    'USER_COURSE_MEMBERS_NOT_FOUND' => '用户课程成员不存在',
    'USER_COURSE_MEMBERS_GET_FAILED' => '用户课程成员获取失败',
    'TOKEN_INVALID' => '无效的令牌',
    'TOKEN_REFRESH_FAILED' => '刷新令牌失败',
    'TOKEN_EXPIRED' => '令牌已过期',
    'TOKEN_TYPE_INVALID' => '令牌类型错误',
    'TOKEN_NOT_FOUND' => '令牌不存在',
    'UPLOAD_FILE_FAILED' => '上传文件失败',
    'SAVE_FAILED' => '保存失败',
    'VIDEO_NOT_FOUND' => '视频资源不存在',
    'SUBMIT_TOO_FREQUENTLY' => '提交过于频繁，请稍后再试',
    'VIDEO_SAVE_FAILED' => '保存视频进度失败',
    'VIDEO_PROGRESS_EXCEPTION' => '视频播放进度异常，请正常观看',
    'VIDEO_WATCHING_EXCEPTION' => '观看时长异常，请正常观看视频',
    'VIDEO_PLAY_SESSION_EXPIRED' => '播放会话已过期，请刷新页面重新开始播放',
    'VIDEO_PLAY_SESSION_NOT_MATCH' => '播放会话不匹配，请刷新页面重新开始播放',
    'VIDEO_PLAY_SESSION_IP_CHANGE' => '检测到IP变更，请重新登录',
    'VIDEO_PLAY_SESSION_USER_AGENT_CHANGE' => '检测到设备信息变更，请重新登录',
    'VIDEO_PLAY_SESSION_REFERRER_CHANGE' => '请从课程页面观看视频',
    'VIDEO_PLAY_SESSION_TIME_DIFF_TOO_LONG' => '播放时间间隔异常，请重新观看',
    'VIDEO_PLAY_SESSION_TIME_DIFF_TOO_SHORT' => '播放进度异常，请正常观看视频',
    'FILE_INVALID' => '文件无效',
    'FILE_TYPE_INVALID' => '文件类型无效',
    'FILE_EMPTY' => '文件为空',
    'COURSE_USER_IMPORT_NOT_FOUND' => '课程用户导入数据不存在',
    'USER_NOT_ROLE' => '用户没有角色',
    'PERMISSION_DENIED' => '没有操作权限',
    'REFLECTION_NOT_FOUND' => '反思报告不存在',
    'REFLECTION_FILE_NOT_FOUND' => '反思报告文件不存在',
    'SYSTEM_ERROR' => '系统错误',
    'RESOURCE_NOT_FOUND' => '资源不存在',
    'TRY_REFRESH_NON_CURRENT_TOKEN' => '尝试刷新非当前令牌',
    'TRY_REFRESH_BLACKLIST_TOKEN' => '尝试刷新黑名单中的令牌',
    'TOKEN_REFRESH_TIMES_LIMIT' => '令牌刷新次数超过限制',
    'TOKEN_REFRESH_INTERVAL_TOO_SHORT' => '令牌刷新间隔过短',
    'INVALID_PARAMS' => '参数错误',
    'SUBMISSION_NOT_FOUND' => '提交记录不存在',

    'user_login' => [
        'account' => [
            'required' => '账号不能为空'
        ],
        'password' => [
            'required' => '密码不能为空'
        ]
    ],
    'user_forget_password' => [
        'account' => [
            'required_without' => '账号不能为空'
        ],
        'email' => [
            'required_without' => '邮箱不能为空',
            'email' => '请输入正确的邮箱地址'
        ],
        'send_email' => [
            'boolean' => '发送邮件必须为布尔值'
        ]
    ],
    'user_verify_reset_password_token' => [
        'token' => [
            'required' => '令牌不能为空'
        ]
    ],
    'user_reset_password' => [
        'id' => [
            'required' => '用户ID不能为空'
        ],
        'password' => [
            'required' => '密码不能为空'
        ],
        'confirm_password' => [
            'required' => '确认密码不能为空'
        ],
        'token' => [
            'required' => '令牌不能为空'
        ]
    ],
    'user_update' => [
        'id' => [
            'required' => '用户ID不能为空',
            'integer' => '用户ID必须为整数'
        ],
        'show_email_type' => [
            'in' => '邮箱显示类型无效'
        ],
        'avatar_url' => [
            'url' => '请输入正确的头像URL'
        ],
        'status' => [
            'in' => '状态值无效'
        ]
    ],
    'user_update_password' => [
        'id' => [
            'required' => '用户ID不能为空'
        ],
        'old_password' => [
            'required' => '旧密码不能为空'
        ],
        'password' => [
            'required' => '新密码不能为空'
        ],
        'confirm_password' => [
            'required' => '确认密码不能为空'
        ]
    ],
    'role_save' => [
        'name' => [
            'required' => '角色名称不能为空',
            'string' => '角色名称必须是字符串',
            'max' => '角色名称不能超过50个字符'
        ],
        'status' => [
            'integer' => '状态必须是整数',
            'in' => '状态值无效'
        ],
        'permissions' => [
            'array' => '权限必须是数组',
            '_integer' => '权限必须是整数'
        ]
    ],
    'user_preference_get' => [
        'user_id' => [
            'required' => '用户ID不能为空',
            'integer' => '用户ID必须为整数'
        ],
        'key' => [
            'required' => '键名不能为空',
            'string' => '键名必须为字符串'
        ]
    ],
    'user_preference_update' => [
        'user_id' => [
            'required' => '用户ID不能为空',
            'integer' => '用户ID必须为整数'
        ],
        'key' => [
            'required' => '键名不能为空',
            'string' => '键名必须为字符串'
        ],
        'value' => [
            'required' => '值不能为空',
            'string' => '值必须为字符串'
        ]
    ],
    'user_preference_list' => [
        'user_id' => [
            'required' => '用户ID不能为空',
            'integer' => '用户ID必须为整数'
        ]
    ],
    'user_course_members' => [
        'course_id' => [
            'required' => '课程ID不能为空',
            'integer' => '课程ID必须为整数'
        ],
        'first_initial' => [
            'string' => '名字首字母必须为字符串',
            'max' => '名字首字母不能超过1个字符'
        ],
        'last_initial' => [
            'string' => '姓氏首字母必须为字符串',
            'max' => '姓氏首字母不能超过1个字符'
        ],
        'keyword' => [
            'string' => '关键词必须为字符串',
            'max' => '关键词不能超过50个字符'
        ],
        'role_id' => [
            'integer' => '角色ID必须为整数'
        ],
        'page' => [
            'integer' => '页码必须为整数'
        ],
        'limit' => [
            'integer' => '每页条数必须为整数'
        ]
    ],
    'unit_save' => [
        'course_id' => [
            'required' => '课程ID不能为空',
            'exists' => '课程不存在'
        ],
        'units' => [
            'required' => '单元不能为空',
            'array' => '单元必须为数组',
            'id' => [
                'nullable' => '单元ID可以为空',
                'integer' => '单元ID必须为整数'
            ]
        ],
        'title_cn' => [
            'required' => '中文标题不能为空'
        ],
        'title_en' => [
            'required' => '英文标题不能为空'
        ],
        'title' => [
            'required' => '标题不能为空'
        ],
        'resources' => [
            'required' => '资源不能为空',
            'array' => '资源必须为数组',
            'id' => [
                'nullable' => '资源ID可以为空',
                'integer' => '资源ID必须为整数'
            ]
        ],
        'resource_type' => [
            'required' => '资源类型不能为空',
            'invalid' => '资源类型无效'
        ],
        'resource_title' => [
            'required' => '资源标题不能为空'
        ],
        'resource_url' => [
            'required' => '资源URL不能为空'
        ],
        'resource_sort_order' => [
            'required' => '资源排序不能为空'
        ],
        'file_type' => [
            'required' => '文件类型不能为空'
        ],
        'file_size' => [
            'required' => '文件大小不能为空'
        ],
        'file_url' => [
            'required' => '文件URL不能为空'
        ],
        'duration' => [
            'required' => '视频时长不能为空'
        ],
        'allow_fast_forward' => [
            'required' => '允许快进选项不能为空'
        ],
        'video_url' => [
            'required' => '视频URL不能为空'
        ],
        'quiz_category_id' => [
            'required' => '测验分类ID不能为空',
            'integer' => '测验分类ID必须为整数',
            'exists' => '测验分类ID不存在'
        ],
        'question_num' => [
            'required' => '题目数量不能为空',
            'integer' => '题目数量必须为整数',
            'min' => '题目数量必须大于等于0'
        ],
        'score_type' => [
            'required' => '得分类型不能为空',
            'string' => '得分类型必须为字符串'
        ],
        'pass_score' => [
            'required' => '及格分数不能为空',
            'decimal' => '及格分数必须为小数',
            'min' => '及格分数必须大于等于0'
        ],
        'total_score' => [
            'required' => '总分数不能为空',
            'decimal' => '总分数必须为小数',
            'min' => '总分数必须大于等于0',
            'integer' => '总分数必须为整数'
        ]
    ],
    'unit_list' => [
        'course_id' => [
            'required' => '课程ID不能为空',
            'integer' => '课程ID必须为整数'
        ]
    ],
    'folder_save' => [
        'id' => [
            'integer' => '文件夹ID必须为整数',
            'exists' => '文件夹不存在'
        ],
        'unit_id' => [
            'required' => '单元ID不能为空',
            'exists' => '单元不存在'
        ],
        'title' => [
            'required' => '标题不能为空',
            'string' => '标题必须为字符串',
            'max' => '标题不能超过200个字符'
        ],
        'description' => [
            'string' => '描述必须为字符串'
        ],
        'sort_order' => [
            'integer' => '排序必须为整数',
            'min' => '排序必须大于等于0'
        ],
        'children' => [
            'array' => '子项必须为数组',
            'id' => [
                'integer' => '子文件夹ID必须为整数',
                'exists' => '子文件夹不存在'
            ],
            'title' => [
                'required' => '子文件夹标题不能为空',
                'string' => '子文件夹标题必须为字符串',
                'max' => '子文件夹标题不能超过200个字符'
            ],
            'description' => [
                'string' => '子文件夹描述必须为字符串'
            ],
            'sort_order' => [
                'integer' => '子文件夹排序必须为整数',
                'min' => '子文件夹排序必须大于等于0'
            ],
            'resource_type' => [
                'required' => '子文件夹资源类型不能为空',
                'in' => '子文件夹资源类型无效'
            ],
            'file_url' => [
                'required_if' => '当资源类型为文件时，文件URL不能为空',
                'string' => '文件URL必须为字符串',
                'max' => '文件URL不能超过500个字符'
            ],
            'file_type' => [
                'required_if' => '当资源类型为文件时，文件类型不能为空',
                'string' => '文件类型必须为字符串',
                'max' => '文件类型不能超过50个字符'
            ],
            'children' => [
                'array' => '子文件夹子项必须为数组',
                'required_if' => '当资源类型为文件夹时，子项不能为空'
            ]
        ]
    ],
    'folder_tree' => [
        'root_directory_id' => [
            'required' => '根目录ID不能为空',
            'integer' => '根目录ID必须为整数'
        ]
    ],
    'resource_upload' => [
        'file' => [
            'required' => '请选择要上传的文件',
            'file' => '上传的文件无效',
            'max' => '文件大小不能超过 :size KB'
        ]
    ],
    'resource_save_video_progress' => [
        'student_id' => [
            'required' => '学生ID不能为空',
            'integer' => '学生ID必须为整数',
            'exists' => '学生不存在'
        ],
        'position' => [
            'required' => '播放位置不能为空',
            'integer' => '播放位置必须为整数'
        ],
        'duration' => [
            'required' => '观看时长不能为空',
            'integer' => '观看时长必须为整数'
        ],
        'course_id' => [
            'required' => '课程ID不能为空',
            'integer' => '课程ID必须为整数',
            'exists' => '课程不存在'
        ],
        'start_time' => [
            'required' => '开始时间不能为空',
            'date' => '开始时间格式不正确'
        ]
    ],
    'resource_video_info' => [
        'with_progress' => [
            'boolean' => '是否包含进度必须为布尔值'
        ],
        'student_id' => [
            'required_if' => '当需要包含进度时，学生ID不能为空',
            'integer' => '学生ID必须为整数',
            'exists' => '学生不存在'
        ],
        'unit_id' => [
            'required_if' => '当需要包含进度时，单元ID不能为空',
            'integer' => '单元ID必须为整数',
            'exists' => '单元不存在'
        ]
    ],
    'folder_root' => [
        'page' => [
            'integer' => '页码必须为整数'
        ],
        'limit' => [
            'integer' => '每页条数必须为整数'
        ],
        'unit_id' => [
            'integer' => '单元ID必须为整数'
        ]
    ],
    'reflection_report' => [
        'understanding' => '理解能力',
        'cognition' => '认知能力',
        'discussion' => '讨论能力',
        'writing' => '写作能力',
        'understanding_level_1' => '对相关理论和题目不甚理解/误解',
        'understanding_level_2' => '对相关理论和题目展示足够的理解',
        'cognition_level_1' => '未能展示任何分析、批判性思维及综合能力',
        'cognition_level_2' => '展示合理的分析、批判性思维及综合能力',
        'discussion_level_1' => '讨论散乱、欠缺逻辑及理据',
        'discussion_level_2' => '讨论清晰、具合理思路及理据',
        'writing_level_1' => '文不达意，难于理解',
        'writing_level_2' => '结构清晰、表达准确、文笔流畅'
    ],
    'resource_update_video' => [
        'title' => [
            'string' => '标题必须为字符串'
        ],
        'description' => [
            'string' => '描述必须为字符串'
        ],
        'video_url' => [
            'string' => '视频URL必须为字符串'
        ],
        'duration' => [
            'integer' => '时长必须为整数'
        ],
        'allow_fast_forward' => [
            'boolean' => '允许快进必须为布尔值'
        ]
    ],
    'resource_update_file' => [
        'title' => [
            'string' => '标题必须为字符串',
            'max' => '标题不能超过200个字符'
        ],
        'description' => [
            'string' => '描述必须为字符串'
        ],
        'file_url' => [
            'string' => '文件URL必须为字符串',
            'max' => '文件URL不能超过500个字符'
        ],
        'file_type' => [
            'string' => '文件类型必须为字符串',
            'max' => '文件类型不能超过50个字符'
        ],
        'is_required' => [
            'boolean' => '是否必填必须为布尔值'
        ],
        'sort_order' => [
            'integer' => '排序必须为整数',
            'min' => '排序必须大于等于0'
        ]
    ],
    'resource_update_folder' => [
        'title' => [
            'string' => '标题必须为字符串',
            'max' => '标题不能超过200个字符'
        ],
        'description' => [
            'string' => '描述必须为字符串'
        ],
        'order_sort' => [
            'integer' => '排序必须为整数',
            'min' => '排序必须大于等于0'
        ]
    ],
    'resource_update_question' => [
        'title' => [
            'string' => '标题必须为字符串',
            'max' => '标题不能超过200个字符'
        ],
        'description' => [
            'string' => '描述必须为字符串'
        ],
        'question_category_id' => [
            'integer' => '题目分类ID必须为整数',
            'exists' => '题目分类不存在'
        ],
        'question_num' => [
            'integer' => '题目数量必须为整数',
            'min' => '题目数量必须大于等于1'
        ],
        'type' => [
            'string' => '类型必须为字符串',
            'max' => '类型不能超过200个字符'
        ],
        'pass_score' => [
            'numeric' => '及格分数必须为数字',
            'min' => '及格分数必须大于等于0'
        ],
        'total_score' => [
            'numeric' => '总分必须为数字',
            'min' => '总分必须大于等于0'
        ]
    ],
    'resource_update_reflection' => [
        'title' => [
            'string' => '标题必须为字符串',
            'max' => '标题不能超过200个字符'
        ],
        'description' => [
            'string' => '描述必须为字符串'
        ],
        'is_hidden' => [
            'boolean' => '是否隐藏必须为布尔值'
        ],
        'start_date' => [
            'date' => '开始日期必须是有效的日期'
        ],
        'end_date' => [
            'date' => '结束日期必须是有效的日期',
            'after_or_equal' => '结束日期必须大于等于开始日期'
        ],
        'total_score' => [
            'numeric' => '总分必须为数字',
            'min' => '总分必须大于等于0',
            'max' => '总分不能超过100'
        ],
        'sort_order' => [
            'integer' => '排序必须为整数',
            'min' => '排序必须大于等于0'
        ]
    ],
    'resource_del' => [
        'type' => [
            'required' => '请选择要删除的资源类型',
            'string' => '资源类型必须是字符串',
            'in' => '无效的资源类型',
        ],
    ],
    'unit_sort' => [
        'array' => [
            'required' => '请提供排序数据',
            'array' => '排序数据必须是数组格式',
        ],
        'id' => [
            'required' => '单元ID不能为空',
            'integer' => '单元ID必须为整数',
            'exists' => '单元不存在',
        ],
        'sort_order' => [
            'required' => '排序值不能为空',
            'integer' => '排序值必须为整数',
            'min' => '排序值必须大于等于0',
        ],
    ],
];
