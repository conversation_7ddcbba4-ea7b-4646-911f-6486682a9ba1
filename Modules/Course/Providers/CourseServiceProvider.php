<?php

namespace Modules\Course\Providers;

use Bingo\Providers\BingoModuleServiceProvider;
use Modules\Course\Providers\MailServiceProvider;

class CourseServiceProvider extends BingoModuleServiceProvider
{
    public function register(): void
    {
        parent::register();

        // 合并模块配置
        $this->mergeConfigFrom(
            __DIR__.'/../config/mail.php', 'mail'
        );

        // 注册邮件服务提供者
        $this->app->register(MailServiceProvider::class);
    }


    public function boot(): void
    {
        $path = __DIR__.'/../Lang';
        $this->loadTranslationsFrom($path, 'Course');

        // 发布配置文件
        $this->publishes([
            __DIR__.'/../config/mail.php' => config_path('mail.php'),
        ], 'course-config');

        $this->registerNavigation();
    }



    /**
     * route path
     *
     * @return string
     */
    public function moduleName(): string
    {
        return 'Course';
    }

    protected function navigation(): array
    {
        return [
            [
                "key" => "course_management",
                "parent" => "application",
                "nav_name" => T("Course::nav.course_management"),
                "path" => "/course/courseList",
                "icon" => "Calendar",
                "order" => 1
            ]
        ];
    }

    /**
     * 注册配置
     * @return array
     */
    public function registerSettings(): array
    {
        return [];
    }


}
