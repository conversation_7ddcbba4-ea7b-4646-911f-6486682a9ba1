<?php

declare(strict_types=1);

namespace Modules\Course\Providers;

use Illuminate\Support\ServiceProvider;
use Modules\Course\Domain\Mail\MailConfig;
use Modules\Course\Domain\Mail\MailSender;
use Modules\Course\Domain\Mail\MailLogger;
use Modules\Course\Domain\Mail\LoggingMailLogger;

/**
 * 邮件服务提供者
 */
class MailServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register(): void
    {
        // 注册邮件日志记录器
        $this->app->bind(MailLogger::class, function ($app) {
            return new LoggingMailLogger();
        });

        // 注册邮件配置
        $this->app->bind(MailConfig::class, function ($app) {
            // 从环境变量获取邮件配置
            return MailConfig::fromEnv();
        });

        // 注册邮件发送器
        $this->app->bind(MailSender::class, function ($app) {
            return new MailSender(
                $app->make(MailConfig::class),
                $app->make(MailLogger::class)
            );
        });
    }

    /**
     * 启动服务
     *
     * @return void
     */
    public function boot(): void
    {
        // 不需要启动操作
    }
} 