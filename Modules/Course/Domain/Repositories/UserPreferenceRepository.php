<?php

namespace Modules\Course\Domain\Repositories;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Modules\Course\Models\UserPreference;

/**
 * 用户偏好仓库
 * 
 * 负责用户偏好设置的数据库操作逻辑
 */
class UserPreferenceRepository
{
    /**
     * 根据用户ID获取所有偏好设置
     *
     * @param int $userId 用户ID
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByUserId(int $userId)
    {
        return UserPreference::where('user_id', $userId)->get();
    }

    /**
     * 根据用户ID和键名获取偏好设置
     *
     * @param int $userId 用户ID
     * @param string $key 偏好键名
     * @return UserPreference|null
     */
    public function getPreference(int $userId, string $key)
    {
        $preference = UserPreference::where('user_id', $userId)
            ->where('key', $key)
            ->first();
        
        return $preference;
    }

    /**
     * 获取所有用户某个键名的偏好设置
     * 
     * @param string $key 偏好键名
     * @param array $userIds 用户ID数组，为空则查询所有用户
     * @return Collection 用户ID为键，偏好设置为值的集合
     */
    public function getPreferencesByKey(string $key, array $userIds = [])
    {
        $query = UserPreference::where('key', $key);
        
        if (!empty($userIds)) {
            $query->whereIn('user_id', $userIds);
        }
        
        return $query->get()->mapWithKeys(function ($item) {
            return [$item->user_id => $item->value];
        });
    }

    /**
     * 批量更新用户偏好设置
     * 
     * @param string $key 偏好键名
     * @param mixed $value 偏好值
     * @param array $userIds 用户ID数组
     * @param string $description 偏好描述
     * @return int 更新的记录数
     */
    public function batchUpdatePreference(string $key, $value, array $userIds, $description = null)
    {
        $count = 0;
        
        foreach ($userIds as $userId) {
            $this->updatePreference($userId, $key, $value, $description);
            $count++;
        }
        
        return $count;
    }

    /**
     * 初始化用户偏好设置，如果不存在则创建
     *
     * @param int $userId 用户ID
     * @param string $key 偏好键名
     * @param mixed $value 偏好值
     * @param string $description 偏好描述
     * @return UserPreference
     */
    private function initPreference(int $userId, string $key, $value, string $description = '')
    {
        $creatorId = Auth::user()->id ?? 0;

        return UserPreference::updateOrCreate(
            ['user_id' => $userId, 'key' => $key],
            [
                'value' => $value,
                'description' => $description,
                'creator_id' => $creatorId
            ]
        );
    }

    /**
     * 更新用户偏好设置（仅更新，不创建）
     * 
     * @param int $userId 用户ID
     * @param string $key 偏好键名
     * @param mixed $value 偏好值
     * @param string $description 偏好描述
     * @return UserPreference|null 更新后的偏好设置对象，如果不存在则返回null
     */
    public function updatePreference(int $userId, string $key, $value, $description = null)
    {
        // 查找现有的偏好设置
        $preference = UserPreference::where('user_id', $userId)
            ->where('key', $key)
            ->first();

        // 如果偏好设置不存在，直接返回null
        if (!$preference) {
            return null;
        }

        $preference->value = $value;
        !is_null($description) && $preference->description = $description;
        $preference->updated_at = now()->timestamp;
        $preference->save();

        return $preference;
    }


    /**
     * 初始化用户所有偏好设置
     * 
     * @param int $userId 用户ID
     * @return void
     */
    public function initUserPreferences(int $userId)
    {
        // 初始化语言偏好
        $this->initPreference(
            $userId, 
            UserPreference::KEY_LANGUAGE, 
            UserPreference::LANG_ZH_CN, 
            '中文', 
        );
        
        // 初始化讨论区偏好
        $this->initPreference(
            $userId, 
            UserPreference::KEY_DISCUSSION, 
            UserPreference::DEFAULT_DISCUSSION_PREFERENCES, 
            '讨论区个性化设置', 
        );
        
        // 初始化编辑器偏好
        $this->initPreference(
            $userId, 
            UserPreference::KEY_EDITOR, 
            UserPreference::EDITOR_DEFAULT, 
            '編輯器偏好', 
        );
        
        // 初始化行事历偏好
        $this->initPreference(
            $userId, 
            UserPreference::KEY_CALENDAR, 
            UserPreference::DEFAULT_CALENDAR_PREFERENCES, 
            '行事曆个性化设置', 
        );
        
        // 初始化教材库偏好
        $this->initPreference(
            $userId, 
            UserPreference::KEY_RESOURCE, 
            UserPreference::DEFAULT_RESOURCE_PREFERENCES, 
            '教材库个性化设置', 
        );
        
        // 初始化讯息偏好
        $this->initPreference(
            $userId, 
            UserPreference::KEY_MESSAGE, 
            UserPreference::DEFAULT_MESSAGE_PREFERENCES, 
            '訊息偏好', 
        );
    }

    /**
     * 批量初始化多个用户的所有偏好设置
     * 
     * @param array $userIds 用户ID数组
     * @return int 初始化的用户数
     */
    public function batchInitUserPreferences(array $userIds)
    {
        $count = 0;
        
        foreach ($userIds as $userId) {
            $this->initUserPreferences($userId);
            $count++;
        }
        
        return $count;
    }
}
