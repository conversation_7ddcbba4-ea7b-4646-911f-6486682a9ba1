<?php

namespace Modules\Course\Domain\Repositories;

use Modules\Course\Models\CourseUnit;
use Illuminate\Support\Facades\DB;

class UnitRepository
{
    protected $model;

    public function __construct(CourseUnit $model)
    {
        $this->model = $model;
    }

    /**
     * 获取单元列表
     * 
     * @param array $params
     * @param bool $withResources 是否包含资源信息
     * @return array
     */
    public function list(array $params, bool $withResources = false)
    {
        $query = $this->model->query();
        
        if (isset($params['course_id'])) {
            $query->where('course_id', $params['course_id']);
        }

        // 如果需要加载资源信息，则预加载相关关联
        if ($withResources) {
            $query->with(['files', 'videos', 'quizzes','reports', 'folders' => function($query) {
                $query->whereNull('parent_id');
            }]);
        }

        $units = $query->orderBy('sort_order')->get()->toArray();
        
        // 如果需要加载资源信息，将不同类型的资源合并为统一的resources数组
        if ($withResources) {
            foreach ($units as &$unit) {
                $unit = $this->mergeResources($unit);
            }
        }
        
        return $units;
    }

    /**
     * 获取单元信息
     * 
     * @param int $id
     * @param bool $withResources 是否包含资源信息
     * @return array
     */
    public function info(int $id, bool $withResources = false)
    {
        $query = $this->model->where('id', $id);
        
        // 如果需要加载资源信息，则预加载相关关联，只获取parent_id=null的文件夹
        if ($withResources) {
            $query->with(['files', 'videos', 'quizzes', 'reports', 'folders' => function($query) {
                $query->whereNull('parent_id');
            }]);
        }
        
        $unit = $query->firstOrFail()->toArray();
        
        // 如果需要加载资源信息，将不同类型的资源合并为统一的resources数组
        if ($withResources) {
            $unit = $this->mergeResources($unit);
        }
        
        return $unit;
    }

    /**
     * 保存单元
     * 
     * @param array $data
     * @return array
     */
    public function save(array $data)
    {
        if (isset($data['id'])) {
            $unit = $this->model->findOrFail($data['id']);
            $unit->update($data);
        } else {
            // 如果没有指定排序值，获取当前最大值+1
            if (!isset($data['sort_order']) && isset($data['course_id'])) {
                $maxOrder = $this->model->where('course_id', $data['course_id'])->max('sort_order') ?: 0;
                $data['sort_order'] = $maxOrder + 1;
            }
            $unit = $this->model->create($data);
        }
        return $unit->toArray();
    }

    /**
     * 删除单元
     * 
     * @param int $id
     * @return array
     */
    public function delete(int $id)
    {
        $unit = $this->model->findOrFail($id);
        $unit->delete();
        return ['message' => '删除成功'];
    }

    /**
     * 批量插入单元数据
     * 
     * @param array $units
     * @return bool
     */
    public function batchInsert(array $units)
    {
        return $this->model->insert($units);
    }

    /**
     * 批量保存单元数据并获取ID列表
     * 支持创建、更新和删除操作
     * 
     * @param array $units 要保存的单元数据数组
     * @param int $courseId 课程ID，用于删除不存在的单元
     * @return array 返回所有操作后的ID列表
     */
    public function batchSaveAndGetIds(array $units, int $courseId)
    {
        if (empty($units)) {
            return [];
        }

        // 开始事务
        DB::beginTransaction();
        
        try {
            // 获取当前课程下的所有单元ID
            $existingIds = $this->model->where('course_id', $courseId)
                ->pluck('id')
                ->toArray();
            
            // 分离需要更新和插入的数据
            $updates = [];
            $inserts = [];
            foreach ($units as $unit) {
                if (isset($unit['id']) && in_array($unit['id'], $existingIds)) {
                    $updates[] = $unit;
                } else {
                    $inserts[] = $unit;
                }
            }
            
            // 执行批量更新
            foreach ($updates as $update) {
                $this->model->where('id', $update['id'])->update($update);
            }
            
            // 执行批量插入
            if (!empty($inserts)) {
                $this->model->insert($inserts);
            }
            
            // 获取所有操作后的ID列表
            $allIds = $this->model->where('course_id', $courseId)
                ->pluck('id')
                ->toArray();
            
            DB::commit();
            return $allIds;
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('批量保存单元失败：' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * 根据ID数组获取单元详情（包含资源）
     * 
     * @param array $ids
     * @return array
     */
    public function getDetailsByIds(array $ids)
    {
        $units = $this->model->with(['files', 'videos', 'quizzes', 'reports', 'folders'])
            ->whereIn('id', $ids)
            ->orderBy('sort_order')
            ->get()
            ->toArray();
        
        // 将不同类型的资源合并为统一的resources数组
        foreach ($units as &$unit) {
            $unit = $this->mergeResources($unit);
        }
        
        return $units;
    }

    /**
     * 将不同类型的资源合并为统一的resources数组
     * 
     * @param array $unit 单元数据
     * @return array 处理后的单元数据
     */
    private function mergeResources(array $unit)
    {
        $resources = [];
        
        // 添加文件类型资源
        if (isset($unit['files']) && is_array($unit['files'])) {
            foreach ($unit['files'] as $file) {
                $resources[] = [
                    'id' => $file['id'],
                    'type' => 'file',
                    'title' => $file['title'],
                    'url' => $file['file_url'] ?? '',
                    'file_type' => $file['file_type'] ?? '',
                    'is_required' => $file['is_required'] ?? false,
                    'sort_order' => $file['sort_order'] ?? 0,
                ];
            }
        }
        
        // 添加视频类型资源
        if (isset($unit['videos']) && is_array($unit['videos'])) {
            foreach ($unit['videos'] as $video) {
                $resources[] = [
                    'id' => $video['id'],
                    'type' => 'video',
                    'title' => $video['title'],
                    'url' => $video['video_url'] ?? '',
                    'duration' => $video['duration'] ?? 0,
                    'allow_fast_forward' => $video['allow_fast_forward'] ?? false,
                    'sort_order' => $video['sort_order'] ?? 0,
                ];
            }
        }
        
        // 添加测验类型资源
        if (isset($unit['quizzes']) && is_array($unit['quizzes'])) {
            foreach ($unit['quizzes'] as $quiz) {
                $resources[] = [
                    'id' => $quiz['id'],
                    'type' => 'quiz',
                    'title' => $quiz['title'],
                    'quiz_category_id' => $quiz['question_category_id'],
                    'sort_order' => $quiz['sort_order'] ?? 0,
                ];
            }
        }
        
        // 添加文件夹类型资源
        if (isset($unit['folders']) && is_array($unit['folders'])) {
            foreach ($unit['folders'] as $folder) {
                $resources[] = [
                    'id' => $folder['id'],
                    'type' => 'folder',
                    'title' => $folder['title'],
                    'sort_order' => $folder['sort_order'] ?? 0,
                ];
            }
        }

        // 添加范式报告类型资源
        if (isset($unit['reports']) && is_array($unit['reports'])) {
            foreach ($unit['reports'] as $report) {
                $resources[] = [
                    'id' => $report['id'],
                    'type' => 'report',
                    'title' => $report['title'],
                    'description' => $report['description'] ?? '',
                    'is_hidden' => $report['is_hidden'] ?? false,
                    'start_date' => $report['start_date'] ?? null,
                    'end_date' => $report['end_date'] ?? null,
                    'total_score' => $report['total_score'] ?? 0,
                    'sort_order' => $report['sort_order'] ?? 0,
                ];
            }
        }
        
        // 按类型和排序号排序资源
        usort($resources, function($a, $b) {
            // 先按照类型排序：视频、文件、文件夹、试题
            $typeOrder = [
                'video' => 1,
                'file' => 2,
                'folder' => 3,
                'quiz' => 4
            ];
            
            $typeA = $typeOrder[$a['type']] ?? 999;
            $typeB = $typeOrder[$b['type']] ?? 999;
            
            // 如果类型不同，按类型优先级排序
            if ($typeA !== $typeB) {
                return $typeA <=> $typeB;
            }
            
            // 如果类型相同，则按排序号排序
            return $a['sort_order'] <=> $b['sort_order'];
        });
        
        // 将合并后的资源添加到单元数据中
        $unit['resources'] = $resources;
        
        // 确保移除原始资源数组
        unset($unit['files'], $unit['videos'], $unit['quizzes'], $unit['folders'], $unit['reports']);
        
        return $unit;
    }
} 