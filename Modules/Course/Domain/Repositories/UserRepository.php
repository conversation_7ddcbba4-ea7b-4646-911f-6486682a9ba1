<?php

namespace Modules\Course\Domain\Repositories;

use Modules\Course\Models\Role;
use Modules\Course\Models\User;
use Bingo\Exceptions\BizException;
use Modules\Course\Enums\ErrorCode;
use Modules\Course\Models\CourseUser;


class UserRepository{

    /**
     * 获取用户信息
     * 
     * @param int $user_id
     * @return array
     */
    public function info($user_id)
    {
        $result = User::findOrFail($user_id);
        return $result->toArray();
    }

    /**
     * 获取用户列表
     * 
     * @param array $params
     * @return array
     */
    public function list(array $params)
    {
        $query = User::query();
        
        // 处理筛选条件
        if (!empty($params['account'])) {
            $query->where('account', 'like', '%' . $params['account'] . '%');
        }
        
        if (!empty($params['email'])) {
            $query->where('email', 'like', '%' . $params['email'] . '%');
        }
        
        if (!empty($params['name'])) {
            $query->where(function($q) use ($params) {
                $q->where('first_name', 'like', '%' . $params['name'] . '%')
                  ->orWhere('last_name', 'like', '%' . $params['name'] . '%');
            });
        }
        
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        if (isset($params['role_id'])) {
            $query->where('role_id', $params['role_id']);
        }
        
        // 排序
        $sortField = $params['sort_field'] ?? 'id';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);
        
        // 分页
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        
        $total = $query->count();
        $items = $query->forPage($page, $limit)->get();

        return [
            'total' => $total,
            'items' => $items->toArray(),
            'page' => (int)$page,
            'limit' => (int)$limit
        ];
    }

    /**
     * 根据账号查询用户
     * 
     * @param string $account
     * @return User|null
     */
    public function getByAccount($account)
    {
        return User::where('account', $account)->first();
    }

    /**
     * 根据邮箱查询用户
     * 
     * @param string $email
     * @return User|null
     */
    public function getByEmail($email)
    {
        return User::where('email', $email)->first();
    }   

    /**
     * 根据ID查询用户
     * 
     * @param int $id
     * @return User|null
     */
    public function getById($id)
    {
        return User::find($id);
    }

    /**
     * 更新用户信息
     * 
     * @param int $id 用户ID
     * @param array $data 更新数据
     * @return bool 更新结果
     */
    public function update(int $id, array $data): bool
    {
        return User::where('id', $id)->update($data);
    }

    /**
     * 获取课程成员列表
     * 
     * @param array $params
     * @return array
     */
    public function getCourseMembers(array $params): array
    {
        $page = intval($params['page'] ?? 1);
        $limit = intval($params['limit'] ?? 10);
        $offset = ($page - 1) * $limit;
      
        $result = CourseUser::with([
            'user' => function($query) use ($params) {
                if(isset($params['first_initial'])){
                    $query->where('first_name', $params['first_initial'] );
                }
                if(isset($params['last_initial'])){
                    $query->where('last_name', $params['last_initial']);
                }
                if(isset($params['role_id'])){
                    $query->where('role_id', $params['role_id']);
                }
                if(isset($params['keyword'])){
                    $query->where(function($q) use ($params) {
                        $q->where('first_name', 'like', '%' . $params['keyword'] . '%')
                          ->orWhere('last_name', 'like', '%' . $params['keyword'] . '%');
                    });
                }
                $query->select([
                    'id',
                    'first_name',
                    'last_name',
                    'email',
                    'code',
                    'role_id',
                    'avatar_url'
                ]);
            },
            'user.role' => function($query) {
                $query->select(['id', 'name']);
            }
        ])
        ->where('course_id', $params['course_id'])
        ->whereHas('user', function($query) {
            $query->where('status', 1); // 只返回状态正常的用户
        })
        ->offset($offset)
        ->limit($limit)
        ->orderBy('created_at', 'desc') // 按加入课程时间倒序排列
        ->get()
        ->map(function($courseUser) {
            // 检查用户是否存在，避免空指针异常
            if (!$courseUser->user) {
                return null; // 返回null而不是空数组，便于后续过滤
            }
            
            $user = $courseUser->user->toArray();
            // 直接获取关联的角色名称，使用空合并运算符避免空指针异常
            $user['role'] = $courseUser->user->role?->name ?? '';
            return $user;
        })
        ->filter() // 过滤掉null值
        ->all();

        $total = CourseUser::where('course_id', $params['course_id'])
            ->whereHas('user', function($query) use ($params) {
                $query->where('status', 1); // 只返回状态正常的用户
                if(isset($params['first_initial'])){
                    $query->where('first_name', $params['first_initial']);
                }
                if(isset($params['last_initial'])){
                    $query->where('last_name', 'like', $params['last_initial'] . '%');
                }
                if(isset($params['keyword'])){
                    $query->where(function($q) use ($params) {
                        $q->where('first_name', 'like', '%' . $params['keyword'] . '%')
                          ->orWhere('last_name', 'like', '%' . $params['keyword'] . '%');
                    });
                }
                if(isset($params['role_id'])){
                    $query->where('role_id', $params['role_id']);
                }
            })
            ->count();

        return [
            'total' => $total,
            'items' => array_values($result),
            'page' => (int)$page,
            'limit' => (int)$limit
        ];
    }

    /**
     * 导入课程用户
     * 
     * @param array $params 包含课程ID和用户数据的参数
     * @return array 导入结果
     */
    public function importCourseMembers(array $params): array
    {
        $courseId = $params['course_id'] ?? 0;
        $userData = $params['users'] ?? [];
        
        // 使用导入文件服务处理导入
        $importService = app(\Modules\Course\Services\Utils\ImportFileService::class);
        return $importService->importCourseUsers($courseId, $userData);
    }
}
