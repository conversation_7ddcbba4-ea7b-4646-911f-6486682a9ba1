<?php

namespace Modules\Course\Domain\Repositories;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Course\Models\CourseUser;
use Modules\Course\Models\ReflectionReport;
use Modules\Course\Models\ReflectionGrading;
use Modules\Course\Models\ReflectionSubmissionRecord;

/**
 * 反思报告仓库类
 */
class ReflectionRepository
{
    /**
     * 获取课程反思报告
     *
     * @param int $courseId 课程ID
     * @return array
     */
    public function getCourseReflection(int $courseId): array
    {
        // 获取课程反思报告基本信息
        $reflection = ReflectionReport::where('course_id', $courseId)
            ->where('deleted_at', 0)
            ->first();

        if (empty($reflection)) {
            return [];
        }

        // 参与者
        $participants = CourseUser::where('course_id', $courseId)
            // ->whereHas('user.role', function($query) {
            //     $query->where('type', Role::TYPE_STUDENT);
            // })
            ->count();

        // 获取反思报告提交状态统计
        $submissionStats = ReflectionSubmissionRecord::where('reflection_id', $reflection->id)
            ->select(
                DB::raw('SUM(CASE WHEN submission_status = 1 THEN 1 ELSE 0 END) as drafts'),
                DB::raw('SUM(CASE WHEN submission_status = 2 THEN 1 ELSE 0 END) as submitted'),
                DB::raw('SUM(CASE WHEN overall_status = 1 THEN 1 ELSE 0 END) as need_grade')
            )
            ->first();

        // 反思报告基础数据
        $reflectionData = $reflection->toArray();

        // 构建返回数据
        $reflectionData['stats'] = [
            'participants' => $participants,
            'drafts' => $submissionStats->drafts ?? 0,
            'submitted' => $submissionStats->submitted ?? 0,
            'need_grade' => $submissionStats->need_grade ?? 0,
        ];

        return $reflectionData;
    }

    /**
     * 获取学生在指定课程的反思报告
     *
     * @param int $studentId 学生ID
     * @param int $courseId 课程ID
     * @return array
     */
    public function getStudentCourseReflection(int $studentId, int $courseId): array
    {
        // 获取课程反思报告信息
        $reflection = ReflectionReport::where('course_id', $courseId)
            ->first();

        if (empty($reflection)) {
            return [];
        }

        // 获取学生的提交记录
        $submissionRecord = ReflectionSubmissionRecord::where('reflection_id', $reflection->id)
            ->where('student_id', $studentId)
            ->first();

        // 构建返回数据
        $result = $reflection->toArray();

        $result['submission'] = !empty($submissionRecord) ? $submissionRecord->toArray() : "";

        $scoringCriteria = ReflectionReport::getScoringCriteria();
        $scoringCriteriaTitle = ReflectionReport::getScoringCriteriaTitle();

        if (!empty($result['submission']) && !empty($result['submission']['score_result'])) {
            $scoreResult = $result['submission']['score_result'] ?? [];
            // 转换评分结果为新的格式
            $formattedScoreResult = [];
            foreach ($scoringCriteria as $criterion => $criteria) {
                $formattedScoreResult[] = [
                    'name' => $criterion,
                    'title' => $scoringCriteriaTitle[$criterion],
                    'positive' => $criteria[1],
                    'negative' => $criteria[0],
                    'is_passed' => $scoreResult[$criterion]['is_passed'] ?? 0,
                    'comment' => $scoreResult[$criterion]['comment'] ?? ''
                ];
            }
            $result['submission']['score_result'] = $formattedScoreResult;
        }

        $result['criteria'] = [];
        foreach ($scoringCriteria as $criterion => $criteria) {
            $result['criteria'][] = [
                'name' => $criterion,
                'title' => $scoringCriteriaTitle[$criterion],
                'positive' => $criteria[1],
                'negative' => $criteria[0]
            ];
        }

        $result['criteria'] = array_values($result['criteria']);

        return $result;
    }

    /**
     * 获取课程反思报告列表
     *
     * @param int $courseId 课程ID
     * @param array $params 查询参数
     * @return array
     */
    /**
     * 获取课程反思报告列表
     *
     * @param int $courseId 课程ID
     * @param array $params 查询参数
     * @return array
     */
    public function getCourseReflectionList(int $courseId, array $params): array
    {
        // 构建基础查询
        $query = ReflectionSubmissionRecord::with(['student', 'reflection'])
            ->whereHas('reflection', function ($q) use ($courseId) {
                $q->where('course_id', $courseId)
                    ->where('deleted_at', 0);
            });

        // 处理筛选条件
        if (!empty($params['name'])) {
            $query->whereHas('student', function ($q) use ($params) {
                $q->where('first_initial',  $params['first_initial'])
                    ->Where('last_initial', $params['last_initial']);
            });
        }

        if (!empty($params['status'])) {
            switch ($params['status']) {
                case 1: // 草稿
                    $query->where('submission_status', ReflectionSubmissionRecord::STATUS_DRAFT);
                    break;
                case 2: // 已提交
                    $query->where('submission_status', ReflectionSubmissionRecord::STATUS_SUBMITTED);
                    break;
                case 3: // 需要评分
                    $query->where('overall_status', ReflectionSubmissionRecord::OVERALL_STATUS_NEED_GRADE);
                    break;
            }
        }

        // 统计总数
        $total = $query->count();

        // 分页
        $page = $params['page'] ?? 1;
        $pageSize = $params['limit'] ?? 20;

        // 查询数据
        $data = $query->orderBy($params['sort_field'] ?? 'id', $params['sort_order'] ?? 'desc')
            ->skip(($page - 1) * $pageSize)
            ->take($pageSize)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'first_name' => $item->student->first_name,
                    'last_name' => $item->student->last_name,
                    'email' => $item->student->email,
                    'code' => $item->student->code,
                    'avatar_url' => $item->student->avatar_url,
                    'score' => $item->score,
                    'submission_status' => $item->submission_status,
                    'overall_status' => $item->overall_status,
                    'updated_at' => $item->updated_at->format('Y-m-d H:i:s'),
                    'file_url' => $item->file_url,
                    'similarity_percentage' => $item->similarity_percentage,
                    'similarity_status' => $item->similarity_status,
                    'similarity_report_url' => $item->similarity_report_url,
                    'submission_time' => $item->submission_time,
                    'late_date' => $item->late_date,
                    'score' => $item->score,
                    'teacher_comment' => $item->teacher_comment,
                    'graded_by' => $item->graded_by,
                    'graded_time' => $item->graded_time,
                    'is_locked' => $item->is_locked,
                    'student_id' => $item->student_id,
                ];
            })
            ->toArray();

        return [
            'list' => $data,
            'total' => $total,
            'page' => (int)$page,
            'limit' => (int)$pageSize
        ];
    }

    /**
     * 批量切换反思报告锁定状态
     *
     * @param array $ids 反思报告ID列表
     * @return array
     */
    /**
     * 批量切换反思报告锁定状态
     *
     * @param array $ids 反思报告ID列表
     * @return array
     * @throws BizException
     */
    public function batchToggleLock(array $ids): array
    {
        // 批量更新反思报告的锁定状态
        $result = ReflectionSubmissionRecord::whereIn('reflection_id', $ids)
            ->where('deleted_at', 0)
            ->update([
                'is_locked' => DB::raw('NOT is_locked'), // 切换锁定状态
                'updated_at' => time()
            ]);


        return [
            'success' => true,
            'message' => '批量切换锁定状态成功',
            'affected_rows' => $result
        ];
    }

    /**
     * 根据ID列表获取反思报告
     *
     * @param array $ids 反思报告ID列表
     * @return Collection    
     */
    public function getUploadedReflectionsByIds(array $ids): Collection
    {
        return ReflectionSubmissionRecord::whereIn('id', $ids)
            ->whereNotNull('file_url')
            ->get();
    }

    /**
     * 批量退回草稿状态
     *
     * @param array $ids 反思报告ID列表
     * @return array
     */
    public function batchBackDraft(array $ids): array
    {
        $result = ReflectionSubmissionRecord::whereIn('id', $ids)
            ->update([
                'submission_status' => ReflectionSubmissionRecord::STATUS_DRAFT,
                'updated_at' => time()
            ]);

        return [
            'success' => true,
            'message' => '批量退回草稿状态成功',
            'affected_rows' => $result
        ];
    }

    /**
     * 批量延期提交
     *
     * @param array $ids 反思报告ID列表
     * @param string $delayDays 延期截止时间
     * @return array
     */
    public function batchDelaySubmit(array $ids, string $delayDays): array
    {
        $result = ReflectionSubmissionRecord::whereIn('id', $ids)
            ->where('deleted_at', 0)
            ->update([
                'late_date' => $delayDays,
                'updated_at' => time()
            ]);

        return [
            'success' => true,
            'message' => '批量延期提交成功',
            'affected_rows' => $result
        ];
    }

    /**
     * 根据ID获取反思报告
     *
     * @param int $id 反思报告ID
     * @return object|null
     */
    public function getReflectionById(int $id)
    {
        return ReflectionReport::where('id', $id)
            ->where('deleted_at', 0)
            ->first();
    }

    /**
     * 添加反思报告提交记录
     *
     * @param array $params 提交参数
     * @return array
     */
    public function addReflection(array $params): array
    {
        // 创建新的提交记录
        $record = new ReflectionSubmissionRecord();

        // 设置字段值
        $record->reflection_id = $params['reflection_id'];
        $record->student_id = $params['student_id'];
        $record->file_url = $params['file_url'];
        $record->upload_time = $params['upload_time'];
        $record->submission_time = $params['submission_time'];
        $record->submission_status = $params['submission_status'];
        $record->overall_status = ReflectionSubmissionRecord::OVERALL_STATUS_NEED_GRADE; // 待评分状态
        $record->similarity_status = ReflectionSubmissionRecord::SIMILARITY_STATUS_PENDING; // 待检测状态
        $record->creator_id = $params['creator_id'];
        $record->created_at = $params['created_at'];
        $record->updated_at = $params['created_at'];
        $record->deleted_at = 0; // 未删除

        // 保存记录
        $record->save();

        // 返回结果
        return [
            'success' => true,
            'message' => '反思报告提交成功',
            'data' => $record
        ];
    }

    /**
     * 对反思报告进行评分
     *
     * @param array $params 评分参数
     * @return array
     */
    public function score(array $params): array
    {
        // 获取反思报告提交记录
        $submission = ReflectionSubmissionRecord::find($params['id']);

        if (empty($submission)) {
            throw new \Exception('反思报告不存在');
        }

        // 更新教师评语
        $submission->teacher_comment = $params['teacher_comment'] ?? '';

        $socre = 0;
        // 获得总分值
        $reflection = ReflectionReport::find($submission->reflection_id);
        $totalScore = $reflection->total_score;
        // 计算评分标准数量
        $criteriaCount = count($params['score_result']);
        // 计算每个标准的权重百分比(向下取整)
        $weightPerCriterion = bcdiv('100', $criteriaCount, 0);
        // 计算单个题目的分值(四舍五入到2位小数)
        $scorePerCriterion = bcdiv(bcmul($totalScore, $weightPerCriterion, 2), '100', 0);


        // 保存各项得分和评语
        $scoreData = [];
        $passedCount = 0;
        foreach ($params['score_result'] as $criterion => $result) {
            $scoreData[$criterion] = [
                'is_passed' => $result['is_passed'],
                'comment' => $result['comment'] ?? ''
            ];

            // 累加分数, 如果通过则加25分, 否则加0分
            $socre += $result['is_passed'] ? $scorePerCriterion : 0;
            $passedCount += $result['is_passed'] ? 1 : 0;
        }

        //判断是否通过，通过则满分
        $isPassed = $totalScore == count($params['score_result']);
        $isPassed and $socre = $totalScore;

        // 更新得分数据
        $submission->score = $socre;
        $submission->score_result = $scoreData;
        $submission->overall_status = $isPassed ? ReflectionSubmissionRecord::OVERALL_STATUS_PASSED : ReflectionSubmissionRecord::OVERALL_STATUS_FAILED;
        $submission->updated_at = time();
        $submission->save();

        return $submission->toArray();
    }

    /**
     * 更新相似度检测状态
     *
     * @param int $id 提交记录ID
     * @param int $status 相似度状态
     * @param string|null $referenceId 查重报告ID
     * @return bool
     */
    public function updateSimilarityStatus(int $id, int $status, ?string $referenceId = null): bool
    {
        $data = [
            'similarity_status' => $status,
            'updated_at' => time()
        ];

        if ($referenceId) {
            $data['similarity_report_url'] = $referenceId;
        }

        return ReflectionSubmissionRecord::where('id', $id)
            ->update($data);
    }
}
