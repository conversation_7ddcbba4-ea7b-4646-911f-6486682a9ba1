<?php

namespace Modules\Course\Domain\Repositories;

use Modules\Course\Models\Quiz;
use Modules\Course\Models\UnitFile;
use Modules\Course\Models\VideoResource;
use Modules\Course\Models\FolderResource;
use Modules\Course\Models\ReflectionReport;

/**
 * 课程资源仓库类
 * 
 * 负责处理课程单元相关的资源，包括文件、视频、文件夹和测验
 */
class ResourceRepository
{
    protected $fileModel;
    protected $videoModel;
    protected $quizModel;
    protected $folderModel;
    protected $reportModel;

    const TYPE_FILE = 'file';
    const TYPE_VIDEO = 'video';
    const TYPE_QUIZ = 'quiz';
    const TYPE_FOLDER = 'folder';
    const TYPE_REPORT = 'report';

    /**
     * 构造函数
     * 
     * @param UnitFile $fileModel
     * @param VideoResource $videoModel
     * @param Quiz $quizModel
     * @param FolderResource $folderModel
     * @param ReflectionReport $reportModel
     */
    public function __construct(
        UnitFile $fileModel,
        VideoResource $videoModel,
        Quiz $quizModel,
        FolderResource $folderModel,
        ReflectionReport $reportModel
    ) {
        $this->fileModel = $fileModel;
        $this->videoModel = $videoModel;
        $this->quizModel = $quizModel;
        $this->folderModel = $folderModel;
        $this->reportModel = $reportModel;
    }

    /**
     * 批量保存资源数据
     * 
     * @param string $type 资源类型(file, video, quiz, folder)
     * @param array $resources 资源数据数组
     * @param int $courseId 课程ID
     * @return bool
     */
    public function batchSave(string $type, array $resources, int $courseId)
    {
        if (empty($resources)) {
            return true;
        }

        try {
            switch ($type) {
                case self::TYPE_FILE:
                    return $this->batchSaveFiles($resources);
                case self::TYPE_VIDEO:
                    return $this->batchSaveVideos($resources, $courseId);
                case self::TYPE_QUIZ:
                    return $this->batchSaveQuizzes($resources);
                case self::TYPE_FOLDER:
                    return $this->batchSaveFolders($resources);
                case self::TYPE_REPORT:
                    return $this->batchSaveReports($resources, $courseId);
                default:
                    throw new \InvalidArgumentException("不支持的资源类型: {$type}");
            }
        } catch (\Exception $e) {
            \Log::error("批量保存{$type}资源失败: " . $e->getMessage(), ['resources' => $resources]);
            throw $e;
        }
    }

    /**
     * 批量保存文件资源
     * 
     * @param array $files 文件资源数组
     * @return bool
     */
    private function batchSaveFiles(array $files)
    {
        // 分离需要更新和插入的数据
        $toUpdate = [];
        $toInsert = [];
        $now = time();

        foreach ($files as $file) {
            if (empty($file['unit_id'])) {
                throw new \InvalidArgumentException("文件资源缺少unit_id");
            }

            $fileData = [
                'unit_id' => $file['unit_id'],
                'title' => $file['title'],
                'description' => $file['description'] ?? '',
                'file_url' => $file['url'] ?? '',
                'file_type' => $file['file_type'] ?? 'pdf',
                'is_required' => $file['is_required'] ?? false,
                'sort_order' => $file['sort_order'] ?? 0,
                'creator_id' => $file['creator_id'] ?? 0,
                'updated_at' => $now,
            ];

            if (isset($file['id'])) {
                $fileData['id'] = $file['id'];
                $toUpdate[] = $fileData;
            } else {
                $fileData['created_at'] = $now;
                $toInsert[] = $fileData;
            }
        }

        // 批量更新
        if (!empty($toUpdate)) {
            $this->fileModel->upsert(
                $toUpdate,
                ['id'],
                ['title', 'description', 'file_url', 'file_type', 'is_required', 'sort_order', 'creator_id', 'updated_at']
            );
        }

        // 批量插入
        if (!empty($toInsert)) {
            $this->fileModel->insert($toInsert);
        }

        return true;
    }

    /**
     * 批量保存视频资源
     * 
     * @param array $videos 视频资源数组
     * @return bool
     */
    private function batchSaveVideos(array $videos, int $courseId)
    {
        // 分离需要更新和插入的数据
        $toUpdate = [];
        $toInsert = [];
        $now = time();

        foreach ($videos as $video) {
            if (empty($video['unit_id'])) {
                throw new \InvalidArgumentException("视频资源缺少unit_id");
            }

            $videoData = [
                'unit_id' => $video['unit_id'],
                'title' => $video['title'],
                'description' => $video['description'] ?? '',
                'video_url' => $video['url'] ?? '',
                'duration' => $video['duration'] ?? 0,
                'allow_fast_forward' => $video['allow_fast_forward'] ?? false,
                'sort_order' => $video['sort_order'] ?? 0,
                'creator_id' => $video['creator_id'] ?? 0,
                'updated_at' => $now,
                'course_id' => $courseId,
            ];

            if (isset($video['id'])) {
                $videoData['id'] = $video['id'];
                $toUpdate[] = $videoData;
            } else {
                $videoData['created_at'] = $now;
                $toInsert[] = $videoData;
            }
        }

        // 批量更新
        if (!empty($toUpdate)) {
            $this->videoModel->upsert(
                $toUpdate,
                ['id'],
                ['title', 'description', 'video_url', 'duration', 'allow_fast_forward', 'sort_order', 'creator_id', 'updated_at', 'course_id']
            );
        }

        // 批量插入
        if (!empty($toInsert)) {
            $this->videoModel->insert($toInsert);
        }

        return true;
    }

    /**
     * 批量保存测验资源
     * 
     * @param array $quizzes 测验资源数组
     * @return bool
     */
    private function batchSaveQuizzes(array $quizzes)
    {
        // 分离需要更新和插入的数据
        $toUpdate = [];
        $toInsert = [];
        $now = time();

        foreach ($quizzes as $quiz) {
            if (empty($quiz['unit_id'])) {
                throw new \InvalidArgumentException("测验资源缺少unit_id");
            }

            $quizData = [
                'unit_id' => $quiz['unit_id'],
                'title' => $quiz['title'],
                'description' => $quiz['description'] ?? '',
                'question_category_id' => $quiz['quiz_category_id'] ?? 0,
                'question_num' => $quiz['question_num'] ?? 10,
                'type' => $quiz['score_type'] ?? 'single',
                'pass_score' => $quiz['pass_score'] ?? 0,
                'total_score' => $quiz['total_score'] ?? 0,
                'sort_order' => $quiz['sort_order'] ?? 0,
                'creator_id' => $quiz['creator_id'] ?? 0,
                'updated_at' => $now,
            ];

            if (isset($quiz['id'])) {
                $quizData['id'] = $quiz['id'];
                $toUpdate[] = $quizData;
            } else {
                $quizData['created_at'] = $now;
                $toInsert[] = $quizData;
            }
        }

        // 批量更新
        if (!empty($toUpdate)) {
            $this->quizModel->upsert(
                $toUpdate,
                ['id'],
                ['unit_id', 'title', 'description', 'question_category_id', 'question_num', 'type', 'pass_score', 'total_score', 'sort_order', 'creator_id', 'updated_at']
            );
        }

        // 批量插入
        if (!empty($toInsert)) {
            $this->quizModel->insert($toInsert);
        }

        return true;
    }

    /**
     * 批量保存文件夹资源
     * 
     * @param array $folders 文件夹资源数组
     * @return bool
     */
    private function batchSaveFolders(array $folders)
    {
        // 分离需要更新和插入的数据
        $toUpdate = [];
        $toInsert = [];
        $now = time();

        foreach ($folders as $folder) {
            if (empty($folder['unit_id'])) {
                throw new \InvalidArgumentException("文件夹资源缺少unit_id");
            }

            $folderData = [
                'unit_id' => $folder['unit_id'],
                'parent_id' => null,
                'title' => $folder['title'],
                'description' => $folder['description'] ?? '',
                'resource_type' => $folder['resource_type'] ?? FolderResource::TYPE_FOLDER,
                'file_url' => null,
                'file_type' => null,
                'sort_order' => $folder['sort_order'] ?? 0,
                'creator_id' => $folder['creator_id'] ?? 0,
                'updated_at' => $now,
            ];

            if (isset($folder['id'])) {
                $folderData['id'] = $folder['id'];
                $toUpdate[] = $folderData;
            } else {
                $folderData['created_at'] = $now;
                $toInsert[] = $folderData;
            }
        }

        // 批量更新
        if (!empty($toUpdate)) {
            $this->folderModel->upsert(
                $toUpdate,
                ['id'],
                ['unit_id', 'parent_id', 'title', 'description', 'resource_type', 'file_url', 'file_type', 'sort_order', 'creator_id', 'updated_at']
            );
        }

        // 批量插入
        if (!empty($toInsert)) {
            $this->folderModel->insert($toInsert);
        }

        return true;
    }

    /**
     * 批量保存范式报告资源
     * 
     * @param array $reports 报告资源数组
     * @return bool
     */
    private function batchSaveReports(array $reports)
    {
        // 分离需要更新和插入的数据
        $toUpdate = [];
        $toInsert = [];
        $now = time();

        foreach ($reports as $report) {
            if (empty($report['unit_id'])) {
                throw new \InvalidArgumentException("范式报告资源缺少unit_id");
            }

            $reportData = [
                'unit_id' => $report['unit_id'],
                'course_id' => $report['course_id'] ?? 0,
                'title' => $report['title'],
                'description' => $report['description'] ?? '',
                'is_hidden' => $report['is_hidden'] ?? false,
                'start_date' => $report['start_date'] ?? null,
                'end_date' => $report['end_date'] ?? null,
                'total_score' => $report['total_score'] ?? 0,
                'sort_order' => $report['sort_order'] ?? 0,
                'creator_id' => $report['creator_id'] ?? 0,
                'updated_at' => $now,
            ];

            if (isset($report['id'])) {
                $reportData['id'] = $report['id'];
                $toUpdate[] = $reportData;
            } else {
                $reportData['created_at'] = $now;
                $toInsert[] = $reportData;
            }
        }

        // 批量更新
        if (!empty($toUpdate)) {
            $this->reportModel->upsert(
                $toUpdate,
                ['id'],
                ['unit_id', 'title', 'description', 'is_hidden', 'start_date', 'end_date', 'total_score', 'sort_order', 'creator_id', 'updated_at']
            );
        }

        // 批量插入
        if (!empty($toInsert)) {
            $this->reportModel->insert($toInsert);
        }

        return true;
    }

    /**
     * 获取视频资源信息
     * 
     * @param int $id
     * @return array
     */
    public function videoInfo(int $id): array
    {
        $result = $this->videoModel->find($id);
        if (!$result) {
            return [];
        }
        return $result->toArray();
    }

    /**
     * 获取文件资源信息
     * 
     * @param int $id
     * @return array
     */
    public function fileInfo(int $id): array
    {
        $result = $this->fileModel->find($id);
        return $result->toArray();
    }

    /**
     * 保存学生视频观看进度
     * 
     * @param int $videoId 视频ID
     * @param array $data 进度数据
     * @return array
     */
    public function saveVideoProgress(int $videoId, array $data): array
    {
        // 获取或创建观看记录
        $watchRecord = \Modules\Course\Models\StudentVideoWatching::updateOrCreate(
            [
                'student_id' => $data['student_id'],
                'video_id' => $videoId,
                'unit_id' => $data['unit_id'] ?? 0,
            ],
            [
                'course_id' => $data['course_id'] ?? 0,
                'last_position' => $data['position'] ?? 0,
                'watch_duration' => $data['duration'] ?? 0,
                'completion_percentage' => $data['progress_percent'] ?? 0,
                'is_completed' => $data['is_completed'] ?? 0,
                'completed_time' => ($data['is_completed'] ?? 0) ? date('Y-m-d H:i:s') : null,
                'start_time' => $data['start_time'] ?? date('Y-m-d H:i:s'),
                'creator_id' => $data['creator_id'] ?? 0,
            ]
        );

        return $watchRecord->toArray();
    }

    /**
     * 获取视频进度
     * 
     * @param int $videoId 视频ID
     * @param int $studentId 学生ID
     * @param int $unitId 单元ID
     * @return array
     */
    public function videoProgress(int $videoId, int $studentId, int $unitId): array
    {
        $result = \Modules\Course\Models\StudentVideoWatching::where('video_id', $videoId)
            ->where('student_id', $studentId)
            ->where('unit_id', $unitId)
            ->first();

        if(empty($result)){
            return [];
        }
        return $result->toArray();
    }
}