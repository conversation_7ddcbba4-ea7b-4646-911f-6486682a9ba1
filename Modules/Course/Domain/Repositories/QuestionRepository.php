<?php

namespace Modules\Course\Domain\Repositories;

use Modules\Course\Models\QuestionBank;
use Illuminate\Support\Facades\DB;

class QuestionRepository
{
    /**
     * 获取题目列表
     *
     * @param array $params 查询参数
     * @return array 题目列表
     */
    public function getQuestions(array $params = [])
    {
        $query = QuestionBank::query();

        // 只获取未删除的题目
        $query->where('deleted_at', 0);

        // 按分类ID筛选
        if (isset($params['category_id'])) {
            $query->where('category_id', $params['category_id']);
        }

        // 按题目类型筛选
        if (isset($params['question_type'])) {
            $query->where('question_type', $params['question_type']);
        }

        // 按题目内容搜索
        if (!empty($params['keyword'])) {
            $query->where(function($q) use ($params) {
                $q->where('question_point', 'like', '%' . $params['keyword'] . '%')
                  ->orWhere('question_detail', 'like', '%' . $params['keyword'] . '%');
            });
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'id';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);

        // 分页
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 100;

        $total = $query->count();
        $items = $query->forPage($page, $limit)->get();

        // 计算最后一页
        $lastPage = ceil($total / $limit);

        return [
            'items' => $items,
            'total' => $total,
            'current_page' => (int)$page,
            'per_page' => (int)$limit,
            'last_page' => $lastPage
        ];
    }

    /**
     * 获取题目详情
     *
     * @param int $id 题目ID
     * @return QuestionBank|null 题目详情
     */
    public function getQuestionById(int $id)
    {
        return QuestionBank::where('deleted_at', 0)->find($id);
    }

    /**
     * 根据编号获取题目
     *
     * @param string $questionNo 题目编号
     * @return QuestionBank|null 题目详情
     */
    public function getQuestionByNo(string $questionNo)
    {
        return QuestionBank::where('deleted_at', 0)->where('question_no', $questionNo)->first();
    }

    /**
     * 创建题目
     *
     * @param array $data 题目数据
     * @return QuestionBank 创建的题目
     */
    public function createQuestion(array $data)
    {
        return QuestionBank::create($data);
    }

    /**
     * 更新题目
     *
     * @param int $id 题目ID
     * @param array $data 题目数据
     * @return bool 是否更新成功
     */
    public function updateQuestion(int $id, array $data)
    {
        $question = QuestionBank::find($id);
        if (!$question) {
            return false;
        }
        return $question->update($data);
    }

    /**
     * 删除题目（软删除）
     *
     * @param int $id 题目ID
     * @return bool 是否删除成功
     */
    public function deleteQuestion(int $id)
    {
        $question = QuestionBank::find($id);
        if (!$question) {
            return false;
        }
        $question->deleted_at = time();
        return $question->save();
    }
}
