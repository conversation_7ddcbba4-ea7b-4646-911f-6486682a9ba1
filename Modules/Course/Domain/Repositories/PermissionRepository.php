<?php

namespace Modules\Course\Domain\Repositories;

use Modules\Course\Models\User;
use Modules\Course\Models\Permission;
use Modules\Course\Models\RolePermission;

class PermissionRepository
{
    /**
     * 获取权限树
     * 
     * @return array
     */
    public function getAll(): array
    {
        $result =  Permission::query()->get();
        return $result->toArray();
    }

    /** 
     * 获取角色权限树
     * 
     * @param int $roleId 角色ID
     * @return array
     */
    public function getAllByRoleId(int $roleId): array
    {
        // 获取角色权限关联记录
        $rolePermissions = RolePermission::query()
            ->where('role_id', $roleId)
            ->get();

        // 获取所有关联的权限ID
        $permissionIds = $rolePermissions->pluck('permission_id')->toArray();

        // 获取所有权限记录
        $permissions = Permission::query()
            ->whereIn('id', $permissionIds)
            ->get();

        return $permissions->toArray();
    }

    /** 
     * 获取用户权限树
     * 
     * @param int $userId 用户ID
     * @return array
     */
    public function getUserPermissions(int $userId): array
    {
        // 获取用户角色
        $user = User::query()
            ->where('id', $userId)
            ->first();    
        
        return $this->getAllByRoleId($user->role_id);
    }

    /**
     * 获取权限详情
     * 
     * @param int $id 权限ID
     * @return Permission|null
     */
    public function getInfo(int $id): ?Permission
    {
        return Permission::find($id);
    }
} 