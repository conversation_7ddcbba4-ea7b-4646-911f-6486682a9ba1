<?php

namespace Modules\Course\Domain\Repositories;

use Modules\Course\Models\SystemLog;

class SystemLogRepository
{
    /**
     * 获取系统日志列表
     * 
     * @param array $params 查询参数
     * @return array
     */
    public function list(array $params): array
    {
        $query = SystemLog::query();

        // 按用户ID筛选
        if (!empty($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        // 按操作类型筛选
        if (!empty($params['action'])) {
            $query->where('action', $params['action']);
        }

        // 按目标类型筛选
        if (!empty($params['target_type'])) {
            $query->where('target_type', $params['target_type']);
        }

        // 按目标ID筛选
        if (!empty($params['target_id'])) {
            $query->where('target_id', $params['target_id']);
        }

        // 按时间范围筛选
        if (!empty($params['start_time'])) {
            $query->where('created_at', '>=', $params['start_time']);
        }
        if (!empty($params['end_time'])) {
            $query->where('created_at', '<=', $params['end_time']);
        }

        // 排序
        $sortField = $params['sort_field'] ?? 'created_at';
        $sortOrder = $params['sort_order'] ?? 'desc';
        $query->orderBy($sortField, $sortOrder);

        // 分页
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;

        $total = $query->count();
        $items = $query->forPage($page, $limit)->get();

        return [
            'total' => $total,
            'items' => $items->toArray(),
            'page' => (int)$page,
            'limit' => (int)$limit
        ];
    }

    

    /**
     * 获取日志详情
     * 
     * @param int $id 日志ID
     * @return SystemLog|null
     */
    public function getInfo(int $id): ?SystemLog
    {
        return SystemLog::find($id);
    }
}
