<?php

namespace Modules\Course\Domain\Repositories;

use Modules\Course\Models\Role;
use Illuminate\Support\Facades\Auth;
use Modules\Course\Models\RolePermission;

class RoleRepository
{
    /**
     * 获取角色列表
     * 
     * @param array $params
     * @return array
     */
    public function list(array $params): array
    {
        $query = Role::query();
        
        // 处理筛选条件
        if (!empty($params['name'])) {
            $query->where('name', 'like', '%' . $params['name'] . '%');
        }
        
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }
        
        // 分页
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 10;
        
        $total = $query->count();
        $items = $query->forPage($page, $limit)->get();
        
        return [
            'total' => $total,
            'items' => $items,
            'page' => (int)$page,
            'limit' => (int)$limit
        ];
    }
    
    /**
     * 获取角色信息
     * 
     * @param int $id
     * @return Role
     */
    public function info(int $id): Role
    {
        return Role::find($id);
    }
    
    /**
     * 删除角色
     * 
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $role = Role::findOrFail($id);
        return $role->delete() > 0;
    }

    /**
     * 更新角色状态
     *
     * @param int $id
     * @return bool
     */
    public function toggle(int $id): bool
    {
        $role = Role::findOrFail($id);
        $role->status = $role->status == Role::STATUS_ENABLED ? Role::STATUS_DISABLED : Role::STATUS_ENABLED;
        return $role->save() > 0;
    }
    
    /**
     * 保存角色(创建或更新)
     * 
     * @param array $params
     * @return array
     */
    public function save(array $params): array
    {
        // 判断是创建还是更新
        if (empty($params['id'])) {
            // 创建角色
            $role = new Role();
        } else {
            // 更新角色
            $role = Role::findOrFail($params['id']);
        }
        
        // 设置角色属性
        $role->name = $params['name'] ?? $role->name;
        $role->description = $params['description'] ?? $role->description ?? '';
        $role->status = $params['status'] ?? $role->status ?? Role::STATUS_ENABLED;
        
        // 保存角色
        $role->save();
        
        return $role->toArray();
    }

    /**
     * 删除角色关联的权限
     * 
     * @param int $roleId 角色ID
     * @return bool
     */
    public function deleteRolePermissions(int $roleId): bool
    {
        return RolePermission::where('role_id', $roleId)->delete() >= 0;
    }
    
    /**
     * 保存角色权限关联
     * 
     * @param int $roleId 角色ID
     * @param array $permissionIds 权限ID数组
     * @return bool
     */
    public function saveRolePermissions(int $roleId, array $permissionIds): bool
    {
        if (empty($permissionIds)) {
            return true;
        }
        
        $data = [];
        $currentTime = time();
        
        foreach ($permissionIds as $permissionId) {
            $data[] = [
                'role_id' => $roleId,
                'permission_id' => $permissionId,
                'creator_id' => 0, // 默认使用0作为系统用户
                'created_at' => $currentTime,
                'updated_at' => $currentTime,
                'deleted_at' => 0,
            ];
        }
        
        return RolePermission::insert($data);
    }

    /**
     * 更新角色权限关联（按需更新，提高性能）
     * 
     * @param int $roleId 角色ID
     * @param array $permissionIds 权限ID数组
     * @return bool
     */
    public function updateRolePermissions(int $roleId, array $permissionIds): bool
    {
        $creatorId = Auth::user()->id ?? 0;

        // 获取当前角色已有的权限ID列表
        $existingPermissions = RolePermission::where('role_id', $roleId)
            ->where('deleted_at', 0)
            ->pluck('permission_id')
            ->toArray();
        
        // 计算需要添加的权限
        $permissionsToAdd = array_diff($permissionIds, $existingPermissions);
        
        // 计算需要删除的权限
        $permissionsToDelete = array_diff($existingPermissions, $permissionIds);
        
        $result = true;
        
        // 添加新权限
        if (!empty($permissionsToAdd)) {
            $dataToInsert = [];
            foreach ($permissionsToAdd as $permissionId) {
                $dataToInsert[] = [
                    'role_id' => $roleId,
                    'permission_id' => $permissionId,
                    'creator_id' => $creatorId,
                ];
            }
            
            $result = $result && RolePermission::insert($dataToInsert);
        }
        
        // 删除不需要的权限（软删除或硬删除）
        if (!empty($permissionsToDelete)) {
            // 软删除
            $result = $result && RolePermission::where('role_id', $roleId)
                ->whereIn('permission_id', $permissionsToDelete)
                ->delete() >= 0;
        }
        
        return $result;
    }
} 