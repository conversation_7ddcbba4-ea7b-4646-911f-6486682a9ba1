<?php

declare(strict_types=1);

namespace Modules\Course\Domain\Mail;

/**
 * 邮件配置类
 */
class MailConfig
{
    /**
     * SMTP主机
     * @var string
     */
    private string $host;

    /**
     * SMTP端口
     * @var int
     */
    private int $port;

    /**
     * SMTP用户名
     * @var string
     */
    private string $username;

    /**
     * SMTP密码
     * @var string
     */
    private string $password;

    /**
     * 加密类型
     * @var string
     */
    private string $encryption;

    /**
     * 发件人邮箱
     * @var string
     */
    private string $fromAddress;

    /**
     * 发件人名称
     * @var string
     */
    private string $fromName;

    /**
     * 构造函数
     */
    public function __construct(
        string $host, 
        int $port, 
        string $username, 
        string $password, 
        string $encryption,
        string $fromAddress,
        string $fromName
    ) {
        $this->host = $host;
        $this->port = $port;
        $this->username = $username;
        $this->password = $password;
        $this->encryption = $encryption;
        $this->fromAddress = $fromAddress;
        $this->fromName = $fromName;
    }

    /**
     * 从环境变量创建邮件配置
     * 
     * @return self
     */
    public static function fromEnv(): self
    {
        return new self(
            env('MAIL_HOST', 'smtp.mailgun.org'),
            (int)env('MAIL_PORT', 587),
            env('MAIL_USERNAME', ''),
            env('MAIL_PASSWORD', ''),
            env('MAIL_ENCRYPTION', 'tls'),
            env('MAIL_FROM_ADDRESS', '<EMAIL>'),
            env('MAIL_FROM_NAME', config('app.name', 'Laravel'))
        );
    }

    /**
     * 从配置创建邮件配置
     * 
     * @param string $serviceKey 配置服务键名
     * @return self
     */
    public static function fromConfig(string $serviceKey = 'default'): self
    {
        $config = config("mail.smtp.services.{$serviceKey}");
        
        if (!$config) {
            // 如果找不到配置，则从环境变量获取
            return self::fromEnv();
        }

        return new self(
            $config['host'] ?? '',
            intval($config['port'] ?? 587),
            $config['username'] ?? '',
            $config['password'] ?? '',
            $config['encryption'] ?? 'tls',
            $config['from_address'] ?? '',
            $config['from_name'] ?? ''
        );
    }

    /**
     * 获取SMTP主机
     * @return string
     */
    public function getHost(): string
    {
        return $this->host;
    }

    /**
     * 获取SMTP端口
     * @return int
     */
    public function getPort(): int
    {
        return $this->port;
    }

    /**
     * 获取SMTP用户名
     * @return string
     */
    public function getUsername(): string
    {
        return $this->username;
    }

    /**
     * 获取SMTP密码
     * @return string
     */
    public function getPassword(): string
    {
        return $this->password;
    }

    /**
     * 获取加密类型
     * @return string
     */
    public function getEncryption(): string
    {
        return $this->encryption;
    }

    /**
     * 获取发件人邮箱
     * @return string
     */
    public function getFromAddress(): string
    {
        return $this->fromAddress;
    }

    /**
     * 获取发件人名称
     * @return string
     */
    public function getFromName(): string
    {
        return $this->fromName;
    }
} 