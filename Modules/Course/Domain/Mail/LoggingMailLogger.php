<?php

declare(strict_types=1);

namespace Modules\Course\Domain\Mail;

use Illuminate\Support\Facades\Log;

/**
 * 使用Laravel日志实现的邮件日志记录
 */
class LoggingMailLogger implements MailLogger
{
    /**
     * 日志频道
     * @var string
     */
    private string $channel;

    /**
     * 构造函数
     * 
     * @param string $channel 日志频道
     */
    public function __construct(string $channel = 'mail')
    {
        $this->channel = $channel;
    }

    /**
     * 记录邮件发送日志
     * 
     * @param string $to 收件人
     * @param string $subject 主题
     * @param string $content 内容
     * @param string $status 状态
     * @param string|null $error 错误信息
     * @return void
     */
    public function log(
        string $to, 
        string $subject, 
        string $content, 
        string $status, 
        ?string $error = null
    ): void {
        $context = [
            'to' => $to,
            'subject' => $subject,
            'content_length' => strlen($content),
            'status' => $status
        ];

        if ($error) {
            $context['error'] = $error;
            Log::channel($this->channel)->error('邮件发送失败', $context);
        } else {
            Log::channel($this->channel)->info('邮件发送成功', $context);
        }
    }
} 