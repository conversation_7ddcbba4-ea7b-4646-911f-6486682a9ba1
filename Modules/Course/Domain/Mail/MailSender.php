<?php

declare(strict_types=1);

namespace Modules\Course\Domain\Mail;

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

/**
 * 邮件发送器实现类
 */
class MailSender
{
    /**
     * 邮件配置
     * @var MailConfig
     */
    private MailConfig $config;

    /**
     * 邮件日志记录器
     * @var MailLogger
     */
    private MailLogger $logger;

    /**
     * 构造函数
     * 
     * @param MailConfig $config 邮件配置
     * @param MailLogger $logger 邮件日志记录器
     */
    public function __construct(MailConfig $config, MailLogger $logger)
    {
        $this->config = $config;
        $this->logger = $logger;
    }

    /**
     * 发送邮件
     * 
     * @param string $to 收件人
     * @param string $subject 主题
     * @param string $content 内容
     * @param array $attachments 附件 [['path' => '文件路径', 'name' => '文件名']]
     * @return bool 是否发送成功
     */
    public function send(string $to, string $subject, string $content, array $attachments = []): bool
    {
        try {
            $mailer = $this->createMailer();
            
            // 设置收件人
            $mailer->addAddress($to);
            
            // 设置邮件主题和内容
            $mailer->Subject = $subject;
            $mailer->Body = $content;
            $mailer->isHTML(true);
            
            // 添加附件
            foreach ($attachments as $attachment) {
                if (isset($attachment['path']) && file_exists($attachment['path'])) {
                    $name = $attachment['name'] ?? basename($attachment['path']);
                    $mailer->addAttachment($attachment['path'], $name);
                }
            }
            
            // 发送邮件
            $result = $mailer->send();
            
            // 记录日志
            $this->logger->log($to, $subject, $content, 'success');
            
            return $result;
        } catch (Exception $e) {
            // 记录失败日志
            $this->logger->log($to, $subject, $content, 'failed', $e->getMessage());
            return false;
        }
    }

    /**
     * 创建PHPMailer实例
     * 
     * @return PHPMailer
     */
    private function createMailer(): PHPMailer
    {
        $mailer = new PHPMailer(true);
        
        // 设置字符集为UTF-8
        $mailer->CharSet = 'UTF-8';
        
        // 设置SMTP
        $mailer->isSMTP();
        $mailer->Host = $this->config->getHost();
        $mailer->SMTPAuth = true;
        $mailer->Username = $this->config->getUsername();
        $mailer->Password = $this->config->getPassword();
        $mailer->Port = $this->config->getPort();
        
        // 设置加密类型
        $encryption = $this->config->getEncryption();
        if ($encryption === 'tls') {
            $mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        } elseif ($encryption === 'ssl') {
            $mailer->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        }
        
        // 设置发件人
        $mailer->setFrom(
            $this->config->getFromAddress(),
            $this->config->getFromName()
        );
        
        return $mailer;
    }
} 