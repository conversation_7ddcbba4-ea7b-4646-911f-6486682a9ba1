<?php

declare(strict_types=1);

namespace Modules\Course\Domain\Mail\Mailable;

use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

/**
 * 测试邮件模板
 * 用于发送测试邮件的模板
 */
class TestEmail extends Mailable
{
    use SerializesModels;

    /**
     * 邮件内容
     * @var string
     */
    private string $emailContent;

    /**
     * 附件列表
     * @var array
     */
    private array $emailAttachments;

    /**
     * 创建一个新的消息实例
     *
     * @param string $subject 邮件主题
     * @param string $content 邮件内容
     * @param array $attachments 附件列表 [['path' => '路径', 'name' => '名称']]
     */
    public function __construct(string $subject, string $content, array $attachments = [])
    {
        $this->subject = $subject;
        $this->emailContent = $content;
        $this->emailAttachments = $attachments;
    }

    /**
     * 构建邮件消息
     *
     * @return $this
     */
    public function build()
    {
        $mail = $this->html($this->emailContent);
        
        // 添加附件
        foreach ($this->emailAttachments as $attachment) {
            if (isset($attachment['path']) && file_exists($attachment['path'])) {
                $name = $attachment['name'] ?? basename($attachment['path']);
                $mail->attach($attachment['path'], ['as' => $name]);
            }
        }
        
        return $mail;
    }
} 