<?php

declare(strict_types=1);

namespace Modules\Course\Domain\Mail;

/**
 * 邮件日志记录接口
 */
interface MailLogger
{
    /**
     * 记录邮件发送日志
     * 
     * @param string $to 收件人
     * @param string $subject 主题
     * @param string $content 内容
     * @param string $status 状态
     * @param string|null $error 错误信息
     * @return void
     */
    public function log(
        string $to, 
        string $subject, 
        string $content, 
        string $status, 
        ?string $error = null
    ): void;
} 