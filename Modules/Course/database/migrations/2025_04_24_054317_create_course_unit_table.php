<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_unit', function (Blueprint $table) {
            $table->comment('课程单元表');
            $table->bigInteger('id', true)->comment('单元ID');
            $table->bigInteger('course_id')->comment('所属课程ID');
            $table->unsignedTinyInteger('unit_number')->comment('单元编号（1-11）');
            $table->string('title_cn', 200)->comment('单元中文标题');
            $table->string('title_en', 200)->comment('单元英文标题');
            $table->string('title', 200)->comment('单元标题');
            $table->text('description')->nullable()->comment('单元描述');
            $table->string('icon_url', 500)->nullable()->comment('单元图标URL');
            $table->integer('sort_order')->default(0)->comment('排序序号');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['course_id', 'unit_number', 'deleted_at'], 'uniq_course_unit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_unit');
    }
};
