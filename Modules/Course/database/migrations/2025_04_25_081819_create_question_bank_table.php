<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('question_bank', function (Blueprint $table) {
            $table->comment('题库表');
            $table->bigInteger('id', true)->comment('题目ID');
            $table->bigInteger('category_id')->index('idx_category')->comment('题目分类ID');
            $table->string('question_point', 300)->default('')->comment('概念/指标能力');
            $table->text('question_detail')->nullable()->comment('试题文字');
            $table->decimal('score', 5)->default(1)->comment('题目分值');
            $table->text('question_feedback')->comment('试题回馈');
            $table->string('question_no', 50)->default('')->comment('试题编号');
            $table->tinyInteger('question_type')->default(1)->comment('题目类型 1单选、2多选');
            $table->tinyInteger('is_range')->default(1)->comment('是否随机排序 1否、2是');
            $table->json('answer')->comment('答案');
            $table->json('answer_feedback')->nullable()->comment('反馈信息');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('question_bank');
    }
};
