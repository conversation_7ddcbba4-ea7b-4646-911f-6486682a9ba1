<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_unit_progress', function (Blueprint $table) {
            $table->comment('学生单元学习进度表');
            $table->bigInteger('id', true)->comment('进度记录ID');
            $table->bigInteger('student_id')->comment('学生ID');
            $table->bigInteger('course_id')->comment('课程ID');
            $table->bigInteger('unit_id')->comment('单元ID');
            $table->dateTime('start_time')->nullable()->comment('开始学习时间');
            $table->dateTime('complete_time')->nullable()->comment('完成时间');
            $table->integer('status')->default(1)->comment('状态： 1未开始、2进行中、3已完成');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['student_id', 'course_id', 'unit_id', 'deleted_at'], 'uniq_student_course_unit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_unit_progress');
    }
};
