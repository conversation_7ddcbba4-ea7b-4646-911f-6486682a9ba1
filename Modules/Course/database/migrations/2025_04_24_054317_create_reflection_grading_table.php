<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reflection_grading', function (Blueprint $table) {
            $table->comment('反思报告评分详情表（包含评分标准关联）');
            $table->bigInteger('id', true)->comment('评分记录ID');
            $table->bigInteger('reflection_id')->comment('反思报告ID');
            $table->bigInteger('criteria_id')->comment('评分标准ID');
            $table->tinyInteger('is_passed')->default(0)->comment('是否通过 1通过、0未通过');
            $table->decimal('score_percentage', 5)->nullable()->comment('标准占用分数');
            $table->text('comment')->nullable()->comment('评语');
            $table->integer('sort_order')->comment('排序');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['reflection_id', 'criteria_id', 'deleted_at'], 'uniq_reflection_criteria');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reflection_grading');
    }
};
