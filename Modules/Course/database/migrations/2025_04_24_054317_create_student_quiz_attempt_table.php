<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_quiz_attempt', function (Blueprint $table) {
            $table->comment('学生测验参与记录表');
            $table->bigInteger('id', true)->comment('测验参与记录ID');
            $table->bigInteger('student_id')->comment('学生ID');
            $table->bigInteger('course_id')->comment('课程ID');
            $table->bigInteger('unit_id')->comment('单元ID');
            $table->unsignedSmallInteger('attempt_number')->comment('尝试次数');
            $table->dateTime('start_time')->comment('开始时间');
            $table->dateTime('end_time')->nullable()->comment('完成时间');
            $table->unsignedTinyInteger('score')->default(0)->comment('得分');
            $table->unsignedTinyInteger('total_questions')->default(10)->comment('题目总数');
            $table->text('quiz_content')->nullable()->comment('试卷内容');
            $table->integer('status')->default(1)->comment('状态： 1进行中、2已提交');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->index(['student_id', 'unit_id'], 'idx_student_quiz');
            $table->unique(['student_id', 'unit_id', 'deleted_at'], 'uniq_student_unit_attempt');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_quiz_attempt');
    }
};
