<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('questions_answer', function (Blueprint $table) {
            $table->bigInteger('course_id')->default(0)->comment('课程ID')->after('student_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('questions_answer', function (Blueprint $table) {
            $table->dropColumn('course_id');
        });
    }
};
