<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('info', function (Blueprint $table) {
            $table->comment('站点公告信息表');
            $table->bigInteger('id', true)->comment('自增ID');
            $table->string('title', 200)->default('')->comment('标题');
            $table->text('description')->nullable()->comment('描述');
            $table->tinyInteger('type')->default(1)->comment('类型：1 FAQ 2公告 3 联系我们');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('info');
    }
};
