<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reflection_report', function (Blueprint $table) {
            $table->comment('反思报告表');
            $table->bigInteger('id', true)->comment('反思报告ID');
            $table->bigInteger('student_id')->comment('学生ID');
            $table->bigInteger('course_id')->comment('课程ID');
            $table->string('title', 200)->comment('反思报告标题');
            $table->text('content')->comment('反思报告内容');
            $table->string('file_url', 500)->nullable()->comment('报告文件URL');
            $table->dateTime('upload_time')->nullable()->comment('上传时间');
            $table->decimal('similarity_percentage', 5)->nullable()->comment('相似度百分比（查重结果）');
            $table->integer('similarity_status')->default(1)->comment('相似度检测状态： 1待检测、2已完成、3失败');
            $table->string('similarity_report_url', 500)->nullable()->comment('相似度报告URL');
            $table->integer('submission_status')->default(1)->comment('提交状态： 1草稿、2已提交、3已退回、4已重新提交');
            $table->dateTime('submission_time')->nullable()->comment('提交时间');
            $table->integer('overall_status')->default(1)->comment('整体状态： 1待评分、2通过、3未通过');
            $table->text('teacher_comment')->nullable()->comment('教师整体评语');
            $table->decimal('score', 5)->nullable()->comment('得分');
            $table->bigInteger('graded_by')->nullable()->comment('评分教师ID');
            $table->dateTime('graded_time')->nullable()->comment('评分时间');
            $table->dateTime('late_date')->nullable()->comment('延期日期（到指定日期）');
            $table->dateTime('start_date')->nullable()->comment('开始日期');
            $table->dateTime('end_date')->nullable()->comment('截止日期');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['student_id', 'course_id', 'deleted_at'], 'uniq_student_course');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reflection_report');
    }
};
