<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user', function (Blueprint $table) {
            $table->comment('系统用户表');
            $table->bigInteger('id', true)->comment('用户ID');
            $table->string('account', 50)->index('idx_account')->comment('登录账号');
            $table->string('password', 100)->comment('加密后的密码');
            $table->bigInteger('role_id')->index('idx_role_id')->comment('用户角色,关联edu_role表');
            $table->string('first_name', 50)->comment('姓氏');
            $table->string('last_name', 50)->comment('名字');
            $table->string('first_initial', 50)->comment('名字首字母');
            $table->string('last_initial', 50)->comment('姓氏首字母');
            $table->string('email', 100)->index('idx_email')->comment('电子邮箱');
            $table->integer('show_email_type')->default(1)->comment('显示邮箱类型： 1所有、2不显示、3仅管理员');
            $table->string('moodlenet_account', 50)->nullable()->comment('Moodle账号');
            $table->string('city_address', 200)->nullable()->comment('城市地址: 省/市');
            $table->string('country', 50)->nullable()->comment('国家');
            $table->string('timezone', 50)->nullable()->comment('时区');
            $table->text('introduction')->nullable()->comment('简介');
            $table->string('code', 50)->comment('编号');
            $table->string('avatar_url', 500)->nullable()->comment('头像URL');
            $table->text('avatar_description')->nullable()->comment('头像说明');
            $table->text('interests')->nullable()->comment('兴趣');
            $table->string('phone_number', 50)->nullable()->comment('座机号码');
            $table->string('mobile_number', 50)->nullable()->comment('手机号码');
            $table->text('detailed_address')->nullable()->comment('详细地址');
            $table->string('institution', 50)->nullable()->comment('机构');
            $table->string('department', 50)->nullable()->comment('科系');
            $table->string('additional_last_name', 50)->nullable()->comment('附加名字');
            $table->string('additional_first_name', 50)->nullable()->comment('附加姓氏');
            $table->string('additional_first_initial', 50)->nullable()->comment('附加名字首字母');
            $table->string('additional_last_initial', 50)->nullable()->comment('附加姓氏首字母');
            $table->string('additional_middle_name', 50)->nullable()->comment('附加中间名');
            $table->string('additional_alias', 50)->nullable()->comment('附加别名');
            $table->integer('status')->default(1)->index('idx_status')->comment('用户状态： 1正常、0禁用');
            $table->timestamp('last_login_time')->nullable()->comment('上次登录时间');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['account', 'deleted_at'], 'uniq_account');
            $table->unique(['email', 'deleted_at'], 'uniq_email');
            $table->unique(['code', 'deleted_at'], 'uniq_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user');
    }
};
