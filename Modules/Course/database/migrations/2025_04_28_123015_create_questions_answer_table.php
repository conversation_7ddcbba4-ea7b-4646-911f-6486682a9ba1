<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions_answer', function (Blueprint $table) {
            $table->comment('测测你题目及答卷表');
            $table->bigInteger('id', true);
            $table->bigInteger('student_id')->comment('学生ID');
            $table->decimal('total_score', 6)->default(0)->comment('总分');
            $table->decimal('actual_score', 6)->default(0)->comment('实际得分');
            $table->tinyInteger('status')->default(1)->comment('状态：1-进行中，2-已完成，3-已提交');
            $table->dateTime('start_time')->nullable()->comment('开始时间');
            $table->dateTime('end_time')->nullable()->comment('结束时间');
            $table->integer('duration')->nullable()->comment('答题时长(秒)');
            $table->json('question')->comment('题目JSON');
            $table->json('user_answer')->nullable()->comment('用户答案JSON');
            $table->json('grade_answer')->nullable()->comment('判卷JSON');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->index(['student_id', 'deleted_at'], '用户');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions_answer');
    }
};
