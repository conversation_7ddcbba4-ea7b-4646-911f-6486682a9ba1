<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('role', function (Blueprint $table) {
            $table->comment('角色表');
            $table->bigInteger('id', true)->comment('角色ID');
            $table->string('name', 50)->comment('角色名称');
            $table->string('code', 50)->index('idx_code')->comment('角色编码');
            $table->text('description')->nullable()->comment('角色描述');
            $table->tinyInteger('status')->default(1)->index('idx_status')->comment('状态 1启用、0禁用');
            $table->tinyInteger('type')->default(1)->comment('类型 1学生、2教师、3管理员');
            $table->bigInteger('creator_id')->default(0)->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['name', 'deleted_at'], 'uniq_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('role');
    }
};
