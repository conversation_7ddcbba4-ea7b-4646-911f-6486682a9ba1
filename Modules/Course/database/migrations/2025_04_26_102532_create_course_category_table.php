<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course_category', function (Blueprint $table) {
            $table->comment('课程分类表');
            $table->bigInteger('id', true)->comment('分类ID');
            $table->bigInteger('parent_id')->default(0)->index('idx_parent')->comment('父分类ID');
            $table->string('name', 100)->comment('分类名称');
            $table->text('description')->comment('描述');
            $table->string('cate_id_num', 300)->default('')->comment('分类ID号');
            $table->integer('sort_order')->default(0)->comment('排序序号');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course_category');
    }
};
