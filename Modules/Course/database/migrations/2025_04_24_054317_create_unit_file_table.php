<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unit_file', function (Blueprint $table) {
            $table->comment('单元文件表');
            $table->bigInteger('id', true)->comment('文件ID');
            $table->bigInteger('unit_id')->index('idx_unit')->comment('所属单元ID');
            $table->string('title', 200)->comment('文件标题');
            $table->text('description')->nullable()->comment('描述说明');
            $table->string('file_url', 500)->comment('文件URL');
            $table->string('file_type', 50)->default('pdf')->comment('文件类型 默认pdf,其他类型：word,excel,ppt');
            $table->tinyInteger('is_required')->default(1)->comment('是否必读 1必读、0非必读');
            $table->unsignedInteger('sort_order')->default(0)->comment('顺序');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_file');
    }
};
