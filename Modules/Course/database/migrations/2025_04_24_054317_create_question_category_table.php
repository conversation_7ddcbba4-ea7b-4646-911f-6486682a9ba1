<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('question_category', function (Blueprint $table) {
            $table->comment('题库分类表');
            $table->bigInteger('id', true)->comment('分类ID');
            $table->string('name', 100)->comment('分类名称');
            $table->text('description')->nullable()->comment('分类描述');
            $table->bigInteger('parent_id')->nullable()->index('idx_parent')->comment('父分类ID，NULL表示顶级分类');
            $table->tinyInteger('level')->default(1)->index('idx_level')->comment('分类层级，1为顶级');
            $table->integer('sort_order')->default(0)->index('idx_sort')->comment('排序序号');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('question_category');
    }
};
