<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_log', function (Blueprint $table) {
            $table->comment('系统操作日志表');
            $table->bigInteger('id', true)->comment('日志ID');
            $table->bigInteger('user_id')->index('idx_user')->comment('操作用户ID');
            $table->string('action', 50)->index('idx_action')->comment('操作类型 （如：登录、登出、新增、修改、删除）');
            $table->string('target_type', 50)->comment('目标类型 （如：用户、课程、班级、单元、视频、测验、反思报告、评分标准、评估记录）');
            $table->bigInteger('target_id')->nullable()->comment('目标ID');
            $table->text('description')->nullable()->comment('操作描述 （如：新增用户、修改课程、删除班级、新增单元、修改视频、删除测验、新增评分标准、删除评估记录）');
            $table->string('ip_address', 50)->nullable()->comment('IP地址');
            $table->string('user_agent', 500)->nullable()->comment('用户代理');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->index(['target_type', 'target_id'], 'idx_target');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_log');
    }
};
