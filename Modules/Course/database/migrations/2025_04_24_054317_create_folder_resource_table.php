<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('folder_resource', function (Blueprint $table) {
            $table->comment('文件夹类型资源表');
            $table->bigInteger('id', true)->comment('资源ID');
            $table->bigInteger('unit_id')->index('idx_unit')->comment('所属单元ID');
            $table->bigInteger('parent_id')->nullable()->index('idx_parent')->comment('父文件夹ID，NULL表示顶级');
            $table->string('title', 200)->comment('资源名称');
            $table->text('description')->nullable()->comment('资源描述');
            $table->string('resource_type', 20)->default('folder')->comment('资源类型：folder或file');
            $table->string('file_url', 500)->nullable()->comment('文件URL，仅文件类型有效');
            $table->string('file_type', 50)->nullable()->comment('文件类型，仅文件类型有效');
            $table->unsignedInteger('sort_order')->default(0)->comment('排序顺序');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('folder_resource');
    }
}; 