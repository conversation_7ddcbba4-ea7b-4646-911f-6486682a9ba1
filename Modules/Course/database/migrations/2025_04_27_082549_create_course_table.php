<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course', function (Blueprint $table) {
            $table->comment('课程表');
            $table->bigInteger('id', true)->comment('课程ID');
            $table->string('name', 200)->default('')->comment('课程全名');
            $table->string('short_name', 200)->default('')->comment('课程名简称');
            $table->bigInteger('category_id')->index('idx_category')->comment('课程分类ID');
            $table->integer('status')->default(1)->index('idx_status')->comment('课程状态： 1准备中 2 已发布 3已完成');
            $table->integer('is_show')->default(1)->comment('是否可见： 1 可见 2 不可见');
            $table->date('start_date')->comment('开课日期');
            $table->date('end_date')->comment('结课日期');
            $table->string('course_id', 50)->default('')->index('idx_code')->comment('课程ID 号码');
            $table->integer('is_user')->nullable()->default(1)->comment('是否包含用户  1 包含 2 不包含');
            $table->string('roles', 200)->nullable()->default('')->comment('包含 用户角色 格式：1,2,3');
            $table->string('image_url', 500)->nullable()->default('')->comment('课程图片URL');
            $table->text('description')->nullable()->comment('课程描述');
            $table->date('report_deadline')->nullable()->comment('反思报告提交截止日期');
            $table->unsignedSmallInteger('max_students')->default(30)->comment('最大学生数');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['course_id', 'deleted_at'], 'uniq_course_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course');
    }
};
