<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz', function (Blueprint $table) {
            $table->comment('试卷表');
            $table->bigInteger('id', true)->comment('试卷ID');
            $table->integer('course_id')->nullable()->comment('课程ID');
            $table->bigInteger('unit_id')->comment('所属单元ID');
            $table->string('title', 200)->default('')->comment('试卷名称,比如：测一测');
            $table->text('description')->nullable()->comment('试卷描述');
            $table->bigInteger('question_category_id')->comment('题目分类ID');
            $table->integer('question_num')->nullable()->default(10)->comment('题目数量');
            $table->unsignedInteger('sort_order')->default(0)->comment('顺序');
            $table->string('type', 200)->default('')->comment('评分方式');
            $table->decimal('pass_score', 6)->nullable()->default(0)->comment('及格分数');
            $table->decimal('total_score', 6)->nullable()->default(0)->comment('总分数');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz');
    }
};
