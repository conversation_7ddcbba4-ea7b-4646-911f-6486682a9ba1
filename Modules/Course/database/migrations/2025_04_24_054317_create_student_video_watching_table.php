<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_video_watching', function (Blueprint $table) {
            $table->comment('学生视频观看记录表');
            $table->bigInteger('id', true)->comment('观看记录ID');
            $table->bigInteger('student_id')->comment('学生ID');
            $table->bigInteger('video_id')->comment('视频ID');
            $table->bigInteger('course_id')->comment('课程ID');
            $table->dateTime('start_time')->nullable()->comment('开始观看时间');
            $table->unsignedInteger('last_position')->default(0)->comment('上次观看位置（秒）');
            $table->unsignedInteger('watch_duration')->default(0)->comment('累计观看时长（秒）');
            $table->decimal('completion_percentage', 5)->default(0)->comment('完成百分比');
            $table->tinyInteger('is_completed')->default(0)->comment('是否完成观看 1完成、0未完成');
            $table->dateTime('completed_time')->nullable()->comment('完成观看时间');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['student_id', 'video_id', 'course_id', 'deleted_at'], 'uniq_student_video_course');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_video_watching');
    }
};
