<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reflection_criteria', function (Blueprint $table) {
            $table->comment('反思报告评分标准表');
            $table->bigInteger('id', true)->comment('评分标准ID');
            $table->string('criteria_name', 200)->comment('评分项名称');
            $table->text('description')->nullable()->comment('评分项描述');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['criteria_name', 'deleted_at'], 'uniq_criteria_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reflection_criteria');
    }
};
