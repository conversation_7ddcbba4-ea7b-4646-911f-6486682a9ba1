<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('video_resource', function (Blueprint $table) {
            $table->comment('视频资源表');
            $table->bigInteger('id', true)->comment('视频资源ID');
            $table->bigInteger('unit_id')->comment('所属单元ID');
            $table->string('title', 200)->comment('视频标题');
            $table->text('description')->nullable()->comment('视频描述');
            $table->string('video_url', 500)->comment('视频URL');
            $table->unsignedInteger('duration')->comment('视频时长（秒）');
            $table->boolean('allow_fast_forward')->default(false)->comment('是否允许快进');
            $table->unsignedInteger('sort_order')->default(0)->comment('顺序');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->index(['unit_id', 'sort_order'], 'idx_unit_sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('video_resource');
    }
};
