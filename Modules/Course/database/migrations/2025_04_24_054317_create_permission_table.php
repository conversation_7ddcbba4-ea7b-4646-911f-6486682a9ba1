<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('permission', function (Blueprint $table) {
            $table->comment('权限表');
            $table->bigInteger('id', true)->comment('权限ID');
            $table->string('name', 100)->comment('权限名称');
            $table->string('code', 100)->comment('权限编码');
            $table->text('description')->nullable()->comment('权限描述');
            $table->string('module', 50)->index('idx_module')->comment('所属模块');
            $table->string('type', 20)->default('operation')->index('idx_type')->comment('权限类型：menu菜单、operation操作');
            $table->bigInteger('parent_id')->nullable()->index('idx_parent')->comment('父权限ID');
            $table->integer('sort_order')->default(0)->comment('排序序号');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['code', 'deleted_at'], 'uniq_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('permission');
    }
};
