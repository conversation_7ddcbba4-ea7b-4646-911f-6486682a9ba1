<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz_questions', function (Blueprint $table) {
            $table->comment('测验题目关联表');
            $table->bigInteger('id', true)->comment('关联ID');
            $table->bigInteger('unit_id')->comment('所属单元ID');
            $table->bigInteger('student_id')->comment('学生ID');
            $table->bigInteger('question_id')->index('idx_question')->comment('题目ID');
            $table->unsignedInteger('sequence')->comment('题目顺序');
            $table->decimal('score', 5)->default(1)->comment('题目分值');
            $table->tinyInteger('is_required')->default(1)->comment('是否必答 1必答、0非必答');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->bigInteger('quiz_record_id')->index('idx_quiz')->comment('测验记录ID');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['quiz_record_id', 'question_id', 'deleted_at'], 'uniq_quiz_question');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz_questions');
    }
};
