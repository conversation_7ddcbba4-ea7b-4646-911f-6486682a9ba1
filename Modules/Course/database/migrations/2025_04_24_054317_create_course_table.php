<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('course', function (Blueprint $table) {
            $table->comment('课程表');
            $table->bigInteger('id', true)->comment('课程ID');
            $table->string('course_code', 50)->index('idx_code')->comment('课程代码');
            $table->string('title_cn', 200)->comment('课程中文标题');
            $table->string('title_en', 200)->comment('课程英文标题');
            $table->text('description_cn')->nullable()->comment('课程中文描述');
            $table->text('description_en')->nullable()->comment('课程英文描述');
            $table->string('image_url', 500)->nullable()->comment('课程图片URL');
            $table->bigInteger('category_id')->index('idx_category')->comment('课程分类ID');
            $table->boolean('is_template')->default(false)->comment('是否为模板课程');
            $table->date('start_date')->nullable()->comment('开课日期');
            $table->date('end_date')->nullable()->comment('结课日期');
            $table->date('report_deadline')->nullable()->comment('反思报告提交截止日期');
            $table->unsignedSmallInteger('max_students')->default(30)->comment('最大学生数');
            $table->integer('status')->default(1)->index('idx_status')->comment('课程状态： 1准备中、2进行中、3已完成、4已归档');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['course_code', 'deleted_at'], 'uniq_course_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('course');
    }
};
