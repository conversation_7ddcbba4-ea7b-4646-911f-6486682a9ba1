<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reflection_submission_settings', function (Blueprint $table) {
            $table->comment('反思报告提交设置表');
            $table->bigInteger('id', true)->comment('设置ID');
            $table->bigInteger('course_id')->index('idx_course')->comment('课程ID');
            $table->dateTime('start_date')->comment('开始提交日期');
            $table->dateTime('due_date')->comment('截止日期');
            $table->tinyInteger('late_submission_allowed')->default(0)->comment('是否允许迟交 1允许、0不允许');
            $table->dateTime('late_submission_deadline')->nullable()->comment('迟交截止日期');
            $table->unsignedInteger('max_file_size')->default(10240)->comment('最大文件大小（KB）');
            $table->tinyInteger('similarity_check_required')->default(1)->comment('是否需要相似度检查 1需要、0不需要');
            $table->decimal('similarity_threshold', 5)->default(30)->comment('相似度阈值百分比');
            $table->string('file_name_format', 100)->nullable()->comment('文件命名格式');
            $table->unsignedInteger('word_count_min')->nullable()->comment('最小字数要求');
            $table->unsignedInteger('word_count_max')->nullable()->comment('最大字数限制');
            $table->text('instructions')->nullable()->comment('提交说明');
            $table->text('declaration_text')->nullable()->comment('声明文本');
            $table->bigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['course_id'], 'uniq_course');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reflection_submission_settings');
    }
};
