<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->comment('无限分类表');
            $table->bigIncrements('id')->comment('分类ID');
            $table->unsignedBigInteger('parent_id')->nullable()->index('idx_parent_id')->comment('父分类ID，顶级分类为NULL');
            $table->string('name')->comment('分类名称');
            $table->text('description')->nullable()->comment('分类描述');
            $table->unsignedInteger('sort_order')->default(0)->comment('同级别分类排序');
            $table->unsignedBigInteger('creator_id')->comment('创建人ID');
            $table->unsignedInteger('created_at')->default(0)->comment('创建时间');
            $table->unsignedInteger('updated_at')->default(0)->comment('更新时间');
            $table->unsignedInteger('deleted_at')->default(0)->index('idx_deleted_at')->comment('删除时间 (用于软删除)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
