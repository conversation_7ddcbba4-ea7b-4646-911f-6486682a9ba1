<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('config', function (Blueprint $table) {
            $table->comment('配置中心');
            $table->bigIncrements('id')->unique('bingo_config_id_unique')->comment('ID  自增');
            $table->string('key', 100)->comment('配置key');
            $table->text('value')->nullable()->comment('配置value');
            $table->string('type', 10)->nullable()->comment('配置类型');
            $table->string('desc', 50)->nullable()->comment('描述');
            $table->string('lang', 50)->nullable()->comment('语言标识');
            $table->unsignedInteger('creator_id')->default(0)->comment('creator id');
            $table->unsignedInteger('created_at')->default(0)->comment('created time');
            $table->unsignedInteger('updated_at')->default(0)->comment('updated time');
            $table->unsignedInteger('deleted_at')->default(0)->comment('delete time');

            $table->unique(['key', 'lang'], 'bingo_config_key_unique');
            $table->primary(['id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('config');
    }
};
