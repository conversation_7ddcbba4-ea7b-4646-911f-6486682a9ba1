
use lms;
-- 反思报告评分标准数据
INSERT INTO `edu_reflection_criteria` (`id`, `criteria_name`, `description`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, '学习内容理解', '对课程核心概念和理论的理解程度', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, '学习过程反思', '对个人学习方法和过程的反思深度', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(3, '学习收获总结', '对学习成果和收获的总结完整性', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(4, '改进计划制定', '对后续学习计划的制定合理性', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);

-- 反思报告提交设置数据
INSERT INTO `edu_reflection_submission_settings` (`id`, `course_id`, `start_date`, `due_date`, `late_submission_allowed`, `late_submission_deadline`, `max_file_size`, `similarity_check_required`, `similarity_threshold`, `file_name_format`, `word_count_min`, `word_count_max`, `instructions`, `declaration_text`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 201, '2023-11-01 08:00:00', '2023-11-15 23:59:59', 1, '2023-11-20 23:59:59', 10240, 1, 30.00, '期中反思报告_{student_name}_{date}', 1000, 5000, '请认真完成期中反思报告，内容包括：1. 对课程内容的理解；2. 学习过程中的收获与困难；3. 后续学习计划。', '本人承诺本反思报告为原创，如有抄袭愿承担相应责任。', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);

-- 反思报告提交记录数据
INSERT INTO `edu_reflection_submission_record` (`id`, `reflection_id`, `student_id`, `file_url`, `upload_time`, `similarity_percentage`, `similarity_status`, `similarity_report_url`, `submission_status`, `submission_time`, `overall_status`, `teacher_comment`, `score`, `graded_by`, `graded_time`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 101, 'uploads/reflections/report_101_20231110.pdf', '2023-11-10 15:30:00', 5.20, 2, 'uploads/similarity/report_101_20231110.pdf', 2, '2023-11-10 15:30:00', 2, '报告内容详实，反思深入，建议继续保持。', 95.00, 201, '2023-11-12 10:00:00', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 1, 102, 'uploads/reflections/report_102_20231112.pdf', '2023-11-12 14:20:00', 8.50, 2, 'uploads/similarity/report_102_20231112.pdf', 2, '2023-11-12 14:20:00', 1, NULL, NULL, NULL, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(3, 1, 103, 'uploads/reflections/report_103_20231113.pdf', '2023-11-13 16:45:00', 15.30, 2, 'uploads/similarity/report_103_20231113.pdf', 2, '2023-11-13 16:45:00', 3, '报告内容过于简单，建议重新提交。', 60.00, 201, '2023-11-14 09:30:00', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(4, 1, 104, NULL, NULL, NULL, 1, NULL, 1, NULL, 1, NULL, NULL, NULL, NULL, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0);

-- 反思报告评分详情数据
INSERT INTO `edu_reflection_grading` (`id`, `reflection_id`, `criteria_id`, `is_passed`, `score_percentage`, `comment`, `sort_order`, `creator_id`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 1, 1, 25.00, '对课程核心概念理解透彻', 1, 201, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(2, 1, 2, 1, 25.00, '学习过程反思深入', 2, 201, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(3, 1, 3, 1, 25.00, '学习收获总结全面', 3, 201, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(4, 1, 4, 1, 25.00, '改进计划制定合理', 4, 201, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(5, 3, 1, 0, 15.00, '对课程内容理解不够深入', 1, 201, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(6, 3, 2, 0, 15.00, '学习过程反思不够具体', 2, 201, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(7, 3, 3, 0, 15.00, '学习收获总结过于简单', 3, 201, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0),
(8, 3, 4, 0, 15.00, '改进计划不够具体', 4, 201, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0); 