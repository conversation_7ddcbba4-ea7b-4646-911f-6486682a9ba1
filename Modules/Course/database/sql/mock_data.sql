-- 插入角色数据
INSERT INTO `lms`.`edu_role` (`name`, `description`, `status`, `creator_id`) VALUES
('系统管理员', '系统最高权限管理员', 1, 1),
('教师', '课程教师', 1, 1),
('学生', '普通学生', 1, 1),
('助教', '课程助教', 1, 1);

-- 插入权限数据
INSERT INTO `lms`.`edu_permission` (`name`, `code`, `description`, `risk_ids`, `module`, `type`, `parent_id`, `sort_order`, `creator_id`) VALUES
('用户管理', 'user:manage', '用户管理权限', '[1,2,3]', 'system', 'menu', NULL, 1, 1),
('角色管理', 'role:manage', '角色管理权限', '[1,2,3]', 'system', 'menu', NULL, 2, 1),
('课程管理', 'course:manage', '课程管理权限', '[1,2,3]', 'course', 'menu', NULL, 3, 1),
('查看用户', 'user:view', '查看用户信息', '[1,2,3]', 'system', 'operation', 1, 1, 1),
('编辑用户', 'user:edit', '编辑用户信息', '[1,2,3]', 'system', 'operation', 1, 2, 1),
('删除用户', 'user:delete', '删除用户', '[1,2,3]', 'system', 'operation', 1, 3, 1),
('查看角色', 'role:view', '查看角色信息', null,'system', 'operation', 2, 1, 1),
('编辑角色', 'role:edit', '编辑角色信息', null,'system', 'operation', 2, 2, 1),
('删除角色', 'role:delete', '删除角色', null,'system', 'operation', 2, 3, 1),
('查看课程', 'course:view', '查看课程信息', null,'course', 'operation', 3, 1, 1),
('编辑课程', 'course:edit', '编辑课程信息',null, 'course', 'operation', 3, 2, 1),
('删除课程', 'course:delete', '删除课程', null,'course', 'operation', 3, 3, 1);

-- 插入角色权限关联数据
INSERT INTO `lms`.`edu_role_permission` (`role_id`, `permission_id`, `creator_id`) VALUES
-- 系统管理员拥有所有权限
(1, 1, 1), (1, 2, 1), (1, 3, 1), (1, 4, 1), (1, 5, 1), (1, 6, 1),
(1, 7, 1), (1, 8, 1), (1, 9, 1), (1, 10, 1), (1, 11, 1), (1, 12, 1),
-- 教师拥有课程相关权限
(2, 3, 1), (2, 10, 1), (2, 11, 1),
-- 助教拥有课程查看权限
(3, 3, 1), (3, 10, 1),
-- 学生只有课程查看权限
(4, 3, 1), (4, 10, 1);

-- 插入用户数据（包含所有字段）
INSERT INTO `lms`.`edu_user` (
    `account`, `password`, `role_id`, `first_name`, `last_name`, 
    `email`, `show_email_type`, `moodlenet_account`, `city_address`, 
    `country`, `timezone`, `introduction`, `code`, `avatar_url`, 
    `avatar_description`, `interests`, `phone_number`, `mobile_number`, 
    `detailed_address`, `department`, `additional_last_name`, 
    `additional_first_name`, `additional_middle_name`, `additional_alias`, 
    `status`, `last_login_time`, `creator_id`
) VALUES
-- 系统管理员
('admin', '$2y$10$BH3FGCsKMd0n2l54S0iOgOe89FAG6fwvpbOWsd0GOrDabJcKd8Yym', 1, '张', '三', 
'<EMAIL>', 1, 'admin_moodle', '北京市朝阳区', 
'中国', 'Asia/Shanghai', '系统管理员，负责系统维护和用户管理', 'ADMIN001', 'https://example.com/avatars/admin.jpg', 
'系统管理员头像', '系统管理、网络安全', '010-********', '***********', 
'北京市朝阳区建国路88号', '信息技术部', '张', 
'三', '系统', 'admin', 
1, NOW(), 1),

-- 教师1
('teacher1', '$2y$10$BH3FGCsKMd0n2l54S0iOgOe89FAG6fwvpbOWsd0GOrDabJcKd8Yym', 2, '李', '四', 
'<EMAIL>', 1, 'teacher1_moodle', '北京市海淀区', 
'中国', 'Asia/Shanghai', '计算机科学教师，专注于人工智能和机器学习', 'TEA001', 'https://example.com/avatars/teacher1.jpg', 
'李老师头像', '人工智能、机器学习、教育技术', '010-87654321', '13900139000', 
'北京市海淀区中关村大街1号', '计算机科学系', '李', 
'四', '教授', 'teacher1', 
1, NOW(), 1),

-- 教师2
('teacher2', '$2y$10$BH3FGCsKMd0n2l54S0iOgOe89FAG6fwvpbOWsd0GOrDabJcKd8Yym', 2, '王', '五', 
'<EMAIL>', 1, 'teacher2_moodle', '北京市海淀区', 
'中国', 'Asia/Shanghai', '软件工程教师，专注于Web开发和数据库', 'TEA002', 'https://example.com/avatars/teacher2.jpg', 
'王老师头像', 'Web开发、数据库、软件工程', '010-98765432', '13700137000', 
'北京市海淀区学院路2号', '计算机科学系', '王', 
'五', '副教授', 'teacher2', 
1, NOW(), 1),

-- 助教1
('assistant1', '$2y$10$BH3FGCsKMd0n2l54S0iOgOe89FAG6fwvpbOWsd0GOrDabJcKd8Yym', 3, '赵', '六', 
'<EMAIL>', 1, 'assistant1_moodle', '北京市海淀区', 
'中国', 'Asia/Shanghai', '计算机科学助教，协助教师进行课程管理', 'ASS001', 'https://example.com/avatars/assistant1.jpg', 
'赵助教头像', '编程、教学辅助', '010-76543210', '13600136000', 
'北京市海淀区学院路3号', '计算机科学系', '赵', 
'六', '助教', 'assistant1', 
1, NOW(), 1),

-- 助教2
('assistant2', '$2y$10$BH3FGCsKMd0n2l54S0iOgOe89FAG6fwvpbOWsd0GOrDabJcKd8Yym', 3, '钱', '七', 
'<EMAIL>', 1, 'assistant2_moodle', '北京市海淀区', 
'中国', 'Asia/Shanghai', '软件工程助教，负责实验课程指导', 'ASS002', 'https://example.com/avatars/assistant2.jpg', 
'钱助教头像', '软件测试、实验指导', '010-65432109', '13500135000', 
'北京市海淀区学院路4号', '计算机科学系', '钱', 
'七', '助教', 'assistant2', 
1, NOW(), 1),

-- 学生1
('student1', '$2y$10$BH3FGCsKMd0n2l54S0iOgOe89FAG6fwvpbOWsd0GOrDabJcKd8Yym', 4, '孙', '八', 
'<EMAIL>', 1, 'student1_moodle', '北京市海淀区', 
'中国', 'Asia/Shanghai', '计算机科学专业大三学生', 'STU001', 'https://example.com/avatars/student1.jpg', 
'孙同学头像', '编程、人工智能', '010-54321098', '13400134000', 
'北京市海淀区学院路5号', '计算机科学系', '孙', 
'八', '学生', 'student1', 
1, NOW(), 1),

-- 学生2
('student2', '$2y$10$BH3FGCsKMd0n2l54S0iOgOe89FAG6fwvpbOWsd0GOrDabJcKd8Yym', 4, '周', '九', 
'<EMAIL>', 1, 'student2_moodle', '北京市海淀区', 
'中国', 'Asia/Shanghai', '软件工程专业大二学生', 'STU002', 'https://example.com/avatars/student2.jpg', 
'周同学头像', 'Web开发、移动应用', '010-43210987', '13300133000', 
'北京市海淀区学院路6号', '计算机科学系', '周', 
'九', '学生', 'student2', 
1, NOW(), 1),

-- 学生3
('student3', '$2y$10$BH3FGCsKMd0n2l54S0iOgOe89FAG6fwvpbOWsd0GOrDabJcKd8Yym', 4, '吴', '十', 
'<EMAIL>', 1, 'student3_moodle', '北京市海淀区', 
'中国', 'Asia/Shanghai', '人工智能专业大一学生', 'STU003', 'https://example.com/avatars/student3.jpg', 
'吴同学头像', '机器学习、数据分析', '010-32109876', '13200132000', 
'北京市海淀区学院路7号', '计算机科学系', '吴', 
'十', '学生', 'student3', 
1, NOW(), 1);


-- 插入偏好数据
INSERT INTO `lms`.`edu_user_preference` (`user_id`, `key`, `value`, `description`, `creator_id`) VALUES
-- 语言偏好
(1, 'language', 'zh_CN', '中文', 1),
-- 讨论区偏好
-- {
--     "auto_subscribe": 1, -- 1: 自动订阅, 0: 不自动订阅
--     "email_digest_type": 1, -- 1: 无, 2: 完整, 3: 主题
--     "use_nested_discussions": 0, -- 1: 使用嵌套讨论视图, 0: 不使用
--     "track_forums": 1, -- 1: 跟踪讨论区, 0: 不跟踪
--     "notify_on_post": 1, -- 1: 标记为已读, 0: 不标记
-- }
(1, 'discussion_preferences', '{
    "auto_subscribe": 1,
    "email_digest_type": 1, 
    "use_nested_discussions": 0,
    "track_forums": 1, 
    "notify_on_post": 1,
}', '讨论区个性化设置', 1),
-- 編輯器偏好,1: 预设编辑器, 2: atto html, 3: tinymce html, 4: 纯文本·
(1, 'editor_preferences', '1', '編輯器偏好', 1),
-- 行事曆偏好
-- {
--     "time_format" 1: 预设, 2: 24小時制, 3: 12小時制(上午/下午)
--     "week_start": 1, -- 0：星期日, 1：星期一，2：星期二，3：星期三，4：星期四，5：星期五，6：星期六
--     "max_events": 10, -- 最多顯示幾件即將來臨的事件 : 1-20次  
--     "show_days": 1, -- 顯示幾日內即將來臨的事件 : 单位：天
--     "remember_filters": 0, -- 記住過濾器的設定
-- }
(1, 'calendar_preferences', '{
    "time_format": 1,
    "week_start": 1,
    "max_events": 10,
    "show_days": 1, 
    "remember_filters": 0,
}', '行事曆个性化设置', 1),
-- 教材庫設定偏好
-- {
--     "default_content_visibility": 1, -- 1: public, 0: 不显示
-- }
(1, 'resource_preferences', '{
   "default_content_visibility": 1, -- 1: public, 0: unlisted
}', '教材库个性化设置', 1),
-- 訊息偏好
-- {
--     "default_content_visibility": 1, -- 1: public, 0: unlisted
-- }
(1, 'message_preferences', '{
    "who_can_send_message": 2, -- 1: 限通訊錄內, 2: 我的通訊錄和我的課程中所有人
    "notify_on_message": [1], -- 1: Email, 2: 行動裝置
    "use_input_key": 0, -- 1: 使用輸入鍵發送, 0: 不使用
}', '訊息偏好', 1);

-- 插入课程分类数据
INSERT INTO `lms`.`edu_course_category` (`name`, `parent_id`, `sort_order`, `creator_id`) VALUES
-- 顶级分类
('计算机科学', NULL, 1, 1),
('软件工程', NULL, 2, 1),
('人工智能', NULL, 3, 1),
-- 计算机科学子分类
('编程基础', 1, 1, 1),
('数据结构与算法', 1, 2, 1),
('计算机网络', 1, 3, 1),
-- 软件工程子分类
('软件设计', 2, 1, 1),
('软件测试', 2, 2, 1),
('项目管理', 2, 3, 1),
-- 人工智能子分类
('机器学习', 3, 1, 1),
('深度学习', 3, 2, 1),
('自然语言处理', 3, 3, 1);

-- 插入课程数据
INSERT INTO `lms`.`edu_course` (
    `course_code`, `title_cn`, `title_en`, `description_cn`, `description_en`, 
    `image_url`, `category_id`, `is_template`, `start_date`, `end_date`, 
    `report_deadline`, `max_students`, `status`, `is_active`, `creator_id`
) VALUES
-- 编程基础课程
('CS101', 'Python编程基础', 'Python Programming Basics', 
'本课程介绍Python编程语言的基础知识，包括语法、数据类型、控制结构等。', 
'This course introduces the basics of Python programming language, including syntax, data types, control structures, etc.',
'https://example.com/images/python-basic.jpg', 4, 0, '2024-03-01', '2024-06-30',
'2024-06-15', 30, 1, 1, 1),

-- 数据结构课程
('CS201', '数据结构与算法', 'Data Structures and Algorithms',
'本课程深入讲解常见数据结构和算法的实现与应用。',
'This course provides an in-depth explanation of common data structures and algorithms implementation and application.',
'https://example.com/images/dsa.jpg', 5, 0, '2024-03-01', '2024-06-30',
'2024-06-15', 30, 1, 1, 1),

-- 软件设计课程
('SE301', '软件设计模式', 'Software Design Patterns',
'本课程介绍常见软件设计模式及其在实际项目中的应用。',
'This course introduces common software design patterns and their applications in real projects.',
'https://example.com/images/design-patterns.jpg', 7, 0, '2024-03-01', '2024-06-30',
'2024-06-15', 30, 1, 1, 1),

-- 机器学习课程
('AI401', '机器学习基础', 'Machine Learning Fundamentals',
'本课程介绍机器学习的基本概念、算法和应用。',
'This course introduces the basic concepts, algorithms, and applications of machine learning.',
'https://example.com/images/ml.jpg', 9, 0, '2024-03-01', '2024-06-30',
'2024-06-15', 30, 1, 1, 1);

-- 插入课程用户管理数据
INSERT INTO `lms`.`edu_course_user` (`user_id`, `course_id`, `creator_id`) VALUES
-- 教师1教授Python编程基础
(2, 1, 1),
-- 教师2教授数据结构与算法
(3, 2, 1),
-- 教师1教授软件设计模式
(2, 3, 1),
-- 教师2教授机器学习基础
(3, 4, 1),
-- 助教1协助Python编程基础
(4, 1, 1),
-- 助教2协助数据结构与算法
(5, 2, 1),
-- 学生1选修Python编程基础
(6, 1, 1),
-- 学生2选修数据结构与算法
(7, 2, 1),
-- 学生3选修机器学习基础
(8, 4, 1);

-- 插入课程单元数据
INSERT INTO `lms`.`edu_course_unit` (
    `course_id`, `title_cn`, `title_en`, `title`, 
    `description`, `icon_url`, `sort_order`, `creator_id`
) VALUES
-- Python编程基础课程单元
(1, 'Python入门', 'Python Introduction', 'Python Introduction',
'本单元介绍Python的基本概念、安装和开发环境设置。',
'https://example.com/icons/python-intro.png', 1, 1),

(1, 'Python基础语法', 'Python Basic Syntax', 'Python Basic Syntax',
'学习Python的基本语法，包括变量、数据类型和运算符。',
'https://example.com/icons/python-syntax.png', 2, 1),

(1, 'Python控制结构', 'Python Control Structures', 'Python Control Structures',
'掌握Python的条件语句和循环结构。',
'https://example.com/icons/python-control.png', 3, 1),

(1, 'Python函数', 'Python Functions', 'Python Functions',
'学习如何定义和使用Python函数。',
'https://example.com/icons/python-functions.png', 4, 1),

-- 数据结构与算法课程单元
(2, '算法基础', 'Algorithm Basics', 'Algorithm Basics',
'介绍算法的基本概念和复杂度分析。',
'https://example.com/icons/algo-basics.png', 1, 1),

(2, '线性数据结构', 'Linear Data Structures', 'Linear Data Structures',
'学习数组、链表、栈和队列等线性数据结构。',
'https://example.com/icons/linear-ds.png', 2, 1),

(2, '树形结构', 'Tree Structures', 'Tree Structures',
'掌握二叉树、二叉搜索树和平衡树等树形结构。',
'https://example.com/icons/tree-structures.png', 3, 1),

(2, '图论基础', 'Graph Theory Basics', 'Graph Theory Basics',
'学习图的基本概念和常见算法。',
'https://example.com/icons/graph-theory.png', 4, 1),

-- 软件设计模式课程单元
(3, '设计模式概述', 'Design Patterns Overview', 'Design Patterns Overview',
'介绍设计模式的基本概念和分类。',
'https://example.com/icons/patterns-overview.png', 1, 1),

(3, '创建型模式', 'Creational Patterns', 'Creational Patterns',
'学习单例、工厂、建造者等创建型模式。',
'https://example.com/icons/creational-patterns.png', 2, 1),

(3, '结构型模式', 'Structural Patterns', 'Structural Patterns',
'掌握适配器、装饰器、代理等结构型模式。',
'https://example.com/icons/structural-patterns.png', 3, 1),

(3, '行为型模式', 'Behavioral Patterns', 'Behavioral Patterns',
'学习观察者、策略、命令等行为型模式。',
'https://example.com/icons/behavioral-patterns.png', 4, 1),

-- 机器学习基础课程单元
(4, '机器学习概述', 'Machine Learning Overview', 'Machine Learning Overview',
'介绍机器学习的基本概念和应用领域。',
'https://example.com/icons/ml-overview.png', 1, 1),

(4, '监督学习', 'Supervised Learning', 'Supervised Learning',
'学习线性回归、逻辑回归、决策树等监督学习算法。',
'https://example.com/icons/supervised-learning.png', 2, 1),

(4, '无监督学习', 'Unsupervised Learning', 'Unsupervised Learning',
'掌握聚类、降维等无监督学习算法。',
'https://example.com/icons/unsupervised-learning.png', 3, 1),

(4, '模型评估与优化', 'Model Evaluation and Optimization', 'Model Evaluation and Optimization',
'学习模型评估方法和优化技巧。',
'https://example.com/icons/model-evaluation.png', 4, 1);

-- 视频资源数据
INSERT INTO `lms`.`edu_video_resource` (
    `unit_id`, `title`, `description`, `video_url`, `duration`, 
    `allow_fast_forward`, `sort_order`, `creator_id`
) VALUES
-- Python入门视频资源
(1, 'Python介绍与环境配置', '本视频介绍Python编程语言的背景和如何搭建开发环境', 
    'https://example.com/videos/python-intro-env.mp4', 1800, 1, 1, 1),
(1, 'Python开发工具使用教程', '如何使用PyCharm、VSCode等IDE进行Python开发', 
    'https://example.com/videos/python-ide-tutorial.mp4', 1500, 1, 2, 1),
    
-- Python基础语法视频资源
(2, 'Python变量与数据类型', '详细讲解Python中的变量定义和各种数据类型', 
    'https://example.com/videos/python-vars-types.mp4', 2400, 0, 1, 1),
(2, 'Python运算符与表达式', '学习Python中的各种运算符和表达式用法', 
    'https://example.com/videos/python-operators.mp4', 1800, 0, 2, 1),
    
-- Python控制结构视频资源
(3, 'Python条件语句详解', '详细讲解if-else和嵌套条件语句的使用方法', 
    'https://example.com/videos/python-if-else.mp4', 2100, 0, 1, 1),
(3, 'Python循环结构教程', '学习for循环和while循环的使用方法和应用场景', 
    'https://example.com/videos/python-loops.mp4', 2400, 0, 2, 1),
    
-- Python函数视频资源
(4, 'Python函数定义与调用', '如何定义和调用Python函数，参数传递方式详解', 
    'https://example.com/videos/python-function-basics.mp4', 2700, 0, 1, 1),
(4, 'Python高级函数特性', '学习Lambda表达式、装饰器等高级函数特性', 
    'https://example.com/videos/python-advanced-functions.mp4', 3000, 0, 2, 1),
    
-- 算法基础视频资源
(5, '算法概念与复杂度分析', '介绍什么是算法以及如何分析算法复杂度', 
    'https://example.com/videos/algo-intro-complexity.mp4', 2700, 1, 1, 1),
(5, '常见算法效率对比', '通过实例比较不同算法的效率差异', 
    'https://example.com/videos/algo-efficiency-comparison.mp4', 2400, 1, 2, 1),
    
-- 线性数据结构视频资源
(6, '数组与链表详解', '深入讲解数组和链表的实现原理和应用场景', 
    'https://example.com/videos/arrays-linked-lists.mp4', 3000, 0, 1, 1),
(6, '栈与队列应用实例', '学习栈和队列的实现方法及其在实际问题中的应用', 
    'https://example.com/videos/stacks-queues.mp4', 2700, 0, 2, 1),
    
-- 树形结构视频资源
(7, '二叉树原理与实现', '详细讲解二叉树的基本概念和实现方法', 
    'https://example.com/videos/binary-trees.mp4', 3300, 0, 1, 1),
(7, '高级树结构与应用', '学习AVL树、红黑树等平衡树结构及其应用', 
    'https://example.com/videos/advanced-trees.mp4', 3600, 0, 2, 1),
    
-- 软件设计模式视频资源
(9, '设计模式基础概念', '介绍设计模式的起源、分类和基本原则', 
    'https://example.com/videos/design-patterns-basics.mp4', 2400, 1, 1, 1),
(9, '设计模式在项目中的应用', '如何在实际项目中合理应用设计模式', 
    'https://example.com/videos/design-patterns-in-practice.mp4', 2700, 1, 2, 1),
    
-- 机器学习概述视频资源
(13, '机器学习导论', '介绍机器学习的基本概念、应用领域和发展历程', 
    'https://example.com/videos/ml-introduction.mp4', 2700, 1, 1, 1),
(13, '机器学习工作流程', '详解机器学习项目的完整工作流程和注意事项', 
    'https://example.com/videos/ml-workflow.mp4', 2400, 1, 2, 1);

-- 单元文件数据
INSERT INTO `lms`.`edu_unit_file` (
    `unit_id`, `title`, `description`, `file_url`, 
    `file_type`, `is_required`, `sort_order`, `creator_id`
) VALUES
-- Python入门文件资源
(1, 'Python环境配置指南', 'Windows、Mac和Linux系统下Python环境的安装与配置教程', 
    'https://example.com/files/python-env-setup-guide.pdf', 'pdf', 1, 1, 1),
(1, 'Python学习路线图', '从新手到专家的Python学习路线和资源推荐', 
    'https://example.com/files/python-learning-roadmap.pdf', 'pdf', 0, 2, 1),
    
-- Python基础语法文件资源
(2, 'Python数据类型总结', '详细总结Python中的各种数据类型及其使用方法', 
    'https://example.com/files/python-data-types-summary.pdf', 'pdf', 1, 1, 1),
(2, 'Python编码规范', 'PEP 8 Python代码风格指南', 
    'https://example.com/files/python-pep8-guide.pdf', 'pdf', 1, 2, 1),
(2, 'Python基础语法练习题', '包含变量、数据类型和运算符的练习题及答案', 
    'https://example.com/files/python-basics-exercises.docx', 'word', 0, 3, 1),
    
-- Python控制结构文件资源
(3, 'Python控制流程详解', '条件语句和循环结构的详细讲解和示例代码', 
    'https://example.com/files/python-control-flow.pdf', 'pdf', 1, 1, 1),
(3, 'Python控制结构练习题', '条件语句和循环结构的练习题与参考答案', 
    'https://example.com/files/python-control-exercises.docx', 'word', 0, 2, 1),
    
-- Python函数文件资源
(4, 'Python函数编程指南', '函数定义、调用、参数传递和返回值的详细指南', 
    'https://example.com/files/python-function-guide.pdf', 'pdf', 1, 1, 1),
(4, 'Python函数高级特性讲解', '闭包、装饰器和生成器等高级函数特性详解', 
    'https://example.com/files/python-advanced-func-features.pdf', 'pdf', 0, 2, 1),
    
-- 算法基础文件资源
(5, '算法复杂度分析指南', '如何分析和计算算法的时间复杂度和空间复杂度', 
    'https://example.com/files/algorithm-complexity-guide.pdf', 'pdf', 1, 1, 1),
(5, '常见算法伪代码示例', '常见算法的伪代码表示和实现思路', 
    'https://example.com/files/algorithm-pseudocode-examples.pdf', 'pdf', 1, 2, 1),
    
-- 线性数据结构文件资源
(6, '线性数据结构实现代码', '数组、链表、栈和队列的Python实现代码', 
    'https://example.com/files/linear-ds-implementations.pdf', 'pdf', 1, 1, 1),
(6, '线性数据结构应用案例', '线性数据结构在实际问题中的应用案例分析', 
    'https://example.com/files/linear-ds-applications.pdf', 'pdf', 0, 2, 1),
    
-- 树形结构文件资源
(7, '树形数据结构教程', '二叉树、二叉搜索树和平衡树的详细教程', 
    'https://example.com/files/tree-structures-tutorial.pdf', 'pdf', 1, 1, 1),
(7, '树形结构实现代码', '各种树形数据结构的Python实现代码', 
    'https://example.com/files/tree-implementations.pdf', 'pdf', 1, 2, 1),
    
-- 设计模式概述文件资源
(9, '设计模式入门指南', '设计模式的基本概念、原则和分类介绍', 
    'https://example.com/files/design-patterns-intro.pdf', 'pdf', 1, 1, 1),
(9, '设计模式对比分析', '不同设计模式的特点和适用场景对比', 
    'https://example.com/files/design-patterns-comparison.pdf', 'pdf', 0, 2, 1),
    
-- 机器学习概述文件资源
(13, '机器学习基础概念', '机器学习的核心概念和基本术语解释', 
    'https://example.com/files/ml-basic-concepts.pdf', 'pdf', 1, 1, 1),
(13, '机器学习应用领域概览', '机器学习在各个领域的应用案例分析', 
    'https://example.com/files/ml-application-domains.pdf', 'pdf', 0, 2, 1),
(13, '机器学习环境配置指南', 'Python机器学习库的安装和环境配置教程', 
    'https://example.com/files/ml-env-setup.pdf', 'pdf', 1, 3, 1);

-- 插入题库分类数据
INSERT INTO `lms`.`edu_question_category` (
    `name`, `description`, `parent_id`, `level`, `sort_order`, `creator_id`
) VALUES
-- 顶级分类
('编程基础', '基础编程知识和技能', NULL, 1, 1, 1),
('数据结构与算法', '数据结构与算法相关知识', NULL, 1, 2, 1),
('软件设计', '软件设计原则和模式', NULL, 1, 3, 1),
('机器学习', '机器学习理论和应用', NULL, 1, 4, 1),

-- 编程基础子分类
('Python基础', 'Python编程语言基础知识', 1, 2, 1, 1),
('Python数据类型', 'Python数据类型和操作', 1, 2, 2, 1),
('Python控制结构', 'Python条件和循环结构', 1, 2, 3, 1),
('Python函数', 'Python函数定义和使用', 1, 2, 4, 1),

-- 数据结构与算法子分类
('算法基础', '算法基本概念和分析方法', 2, 2, 1, 1),
('线性数据结构', '数组、链表、栈、队列等线性结构', 2, 2, 2, 1),
('树形结构', '二叉树、平衡树等树形结构', 2, 2, 3, 1),
('图结构', '图的表示和算法', 2, 2, 4, 1),

-- 软件设计子分类
('设计原则', '软件设计的基本原则', 3, 2, 1, 1),
('创建型模式', '创建型设计模式', 3, 2, 2, 1),
('结构型模式', '结构型设计模式', 3, 2, 3, 1),
('行为型模式', '行为型设计模式', 3, 2, 4, 1),

-- 机器学习子分类
('机器学习基础', '机器学习基本概念和理论', 4, 2, 1, 1),
('监督学习', '监督学习算法和应用', 4, 2, 2, 1),
('无监督学习', '无监督学习算法和应用', 4, 2, 3, 1),
('模型评估', '机器学习模型评估方法', 4, 2, 4, 1);

-- 插入题库数据
INSERT INTO `lms`.`edu_question_bank` (
    `category_id`, `question_point`, `question_detail`, `score`, 
    `question_feedback`, `question_no`, `question_type`, 
    `is_range`, `answer`, `answer_feedback`, `creator_id`
) VALUES
-- Python基础题目
(5, 'Python解释器', '以下哪种不是常用的Python解释器？', 1.00, 
    '了解不同的Python解释器是学习Python的基础知识之一', 'PY001', 1, 
    1, '[{"id": 1, "text": "CPython", "is_correct": false}, 
          {"id": 2, "text": "PyPy", "is_correct": false}, 
          {"id": 3, "text": "Jython", "is_correct": false}, 
          {"id": 4, "text": "CythonScript", "is_correct": true}]', 
    '{"1": "CPython是最常用的Python解释器，用C语言实现", 
      "2": "PyPy是用Python实现的Python解释器，具有JIT编译", 
      "3": "Jython是用Java实现的Python解释器", 
      "4": "CythonScript不存在，Cython是Python的C扩展"}', 1),
          
(5, 'Python版本', 'Python 2和Python 3的主要区别包括哪些？', 2.00, 
    '理解Python不同版本的区别有助于避免兼容性问题', 'PY002', 2, 
    1, '[{"id": 1, "text": "打印语句的语法不同", "is_correct": true}, 
          {"id": 2, "text": "整数除法的行为不同", "is_correct": true}, 
          {"id": 3, "text": "字符串和Unicode处理方式不同", "is_correct": true}, 
          {"id": 4, "text": "Python 3不支持面向对象编程", "is_correct": false}]', 
    '{"1": "Python 2使用print语句，Python 3使用print()函数", 
      "2": "Python 2中/执行整数除法，Python 3中/执行浮点除法", 
      "3": "Python 3默认使用Unicode编码字符串", 
      "4": "两个版本都完全支持面向对象编程"}', 1),
          
-- Python数据类型题目
(6, 'Python数据类型', '下列哪个不是Python的基本数据类型？', 1.00, 
    '掌握Python基本数据类型是编程的基础', 'PY003', 1, 
    1, '[{"id": 1, "text": "int", "is_correct": false}, 
          {"id": 2, "text": "float", "is_correct": false}, 
          {"id": 3, "text": "array", "is_correct": true}, 
          {"id": 4, "text": "bool", "is_correct": false}]', 
    '{"1": "int是Python的整数类型", 
      "2": "float是Python的浮点数类型", 
      "3": "array不是Python的基本数据类型，而是需要通过模块导入的类型", 
      "4": "bool是Python的布尔类型"}', 1),
          
(6, '列表操作', '关于Python列表，下列说法正确的是：', 2.00, 
    '列表是Python中最常用的数据结构之一', 'PY004', 2, 
    1, '[{"id": 1, "text": "列表是不可变的", "is_correct": false}, 
          {"id": 2, "text": "列表可以包含不同类型的元素", "is_correct": true}, 
          {"id": 3, "text": "append()方法在列表开头添加元素", "is_correct": false}, 
          {"id": 4, "text": "列表支持切片操作", "is_correct": true}]', 
    '{"1": "列表是可变的，可以修改其内容", 
      "2": "列表可以包含任意类型的元素，包括混合类型", 
      "3": "append()在列表末尾添加元素，insert()可在指定位置添加", 
      "4": "列表支持切片操作，可以获取子列表"}', 1),
          
-- Python控制结构题目
(7, '条件语句', '在Python中，以下哪个不是有效的条件语句？', 1.00, 
    '掌握条件语句的正确语法是编程基础', 'PY005', 1, 
    1, '[{"id": 1, "text": "if x > 0: print(x)", "is_correct": false}, 
          {"id": 2, "text": "if (x > 0) print(x)", "is_correct": true}, 
          {"id": 3, "text": "if x > 0:\\n    print(x)", "is_correct": false}, 
          {"id": 4, "text": "if x > 0: print(x)\\nelse: print(-x)", "is_correct": false}]', 
    '{"1": "这是有效的单行if语句", 
      "2": "Python不使用这种无冒号的语法，正确语法需要冒号", 
      "3": "这是标准的缩进形式的if语句", 
      "4": "这是有效的单行if-else语句"}', 1),
          
(7, '循环结构', '关于Python循环，下列说法正确的是：', 2.00, 
    '理解不同循环结构的特点和用途', 'PY006', 2, 
    1, '[{"id": 1, "text": "for循环可以遍历任何可迭代对象", "is_correct": true}, 
          {"id": 2, "text": "while循环必须使用break语句退出", "is_correct": false}, 
          {"id": 3, "text": "continue语句会结束整个循环", "is_correct": false}, 
          {"id": 4, "text": "循环可以有else子句", "is_correct": true}]', 
    '{"1": "for循环可以遍历列表、元组、字符串等可迭代对象", 
      "2": "while循环可以通过条件控制退出，不一定需要break", 
      "3": "continue语句跳过当前迭代，继续下一次迭代", 
      "4": "Python的循环可以有else子句，在循环正常完成时执行"}', 1),
          
-- Python函数题目
(8, '函数定义', '关于Python函数参数，下列说法错误的是：', 1.00, 
    '理解函数参数的类型和传递方式是函数编程的基础', 'PY007', 1, 
    1, '[{"id": 1, "text": "位置参数必须在关键字参数之前", "is_correct": false}, 
          {"id": 2, "text": "可变参数用*args表示", "is_correct": false}, 
          {"id": 3, "text": "默认参数值在函数调用时计算", "is_correct": true}, 
          {"id": 4, "text": "关键字可变参数用**kwargs表示", "is_correct": false}]', 
    '{"1": "位置参数必须在关键字参数之前，这是正确的", 
      "2": "*args用于接收任意数量的位置参数，这是正确的", 
      "3": "默认参数值在函数定义时计算，而不是调用时", 
      "4": "**kwargs用于接收任意数量的关键字参数，这是正确的"}', 1),
          
(8, '函数特性', '关于Python函数，以下哪些说法是正确的？', 2.00, 
    '深入理解Python函数的特性和高级用法', 'PY008', 2, 
    1, '[{"id": 1, "text": "函数在Python中是一等公民", "is_correct": true}, 
          {"id": 2, "text": "函数可以作为参数传递给其他函数", "is_correct": true}, 
          {"id": 3, "text": "函数不能返回另一个函数", "is_correct": false}, 
          {"id": 4, "text": "装饰器是修改函数行为的一种方式", "is_correct": true}]', 
    '{"1": "Python中函数是一等公民，可以赋值给变量", 
      "2": "函数可以作为参数传递，实现回调等功能", 
      "3": "函数可以返回另一个函数，这是闭包的基础", 
      "4": "装饰器可以在不修改原函数代码的情况下扩展功能"}', 1),
          
-- 算法基础题目
(9, '算法复杂度', '时间复杂度为O(n²)的算法有：', 2.00, 
    '理解不同算法的时间复杂度是算法分析的基础', 'ALGO001', 2, 
    1, '[{"id": 1, "text": "冒泡排序", "is_correct": true}, 
          {"id": 2, "text": "二分查找", "is_correct": false}, 
          {"id": 3, "text": "选择排序", "is_correct": true}, 
          {"id": 4, "text": "快速排序（平均情况）", "is_correct": false}]', 
    '{"1": "冒泡排序平均时间复杂度为O(n²)", 
      "2": "二分查找时间复杂度为O(log n)", 
      "3": "选择排序时间复杂度为O(n²)", 
      "4": "快速排序平均时间复杂度为O(n log n)"}', 1),
          
-- 机器学习基础题目
(17, '机器学习概念', '以下哪项不是监督学习算法？', 1.00, 
    '区分监督学习和无监督学习算法是机器学习基础知识', 'ML001', 1, 
    1, '[{"id": 1, "text": "线性回归", "is_correct": false}, 
          {"id": 2, "text": "K-means聚类", "is_correct": true}, 
          {"id": 3, "text": "决策树", "is_correct": false}, 
          {"id": 4, "text": "支持向量机", "is_correct": false}]', 
    '{"1": "线性回归是典型的监督学习算法", 
      "2": "K-means聚类是无监督学习算法", 
      "3": "决策树是监督学习算法", 
      "4": "支持向量机是监督学习算法"}', 1),
          
(17, '机器学习工作流程', '机器学习项目的基本步骤包括：', 2.00, 
    '了解机器学习项目的完整工作流程是实践机器学习的基础', 'ML002', 2, 
    1, '[{"id": 1, "text": "数据收集和预处理", "is_correct": true}, 
          {"id": 2, "text": "特征工程", "is_correct": true}, 
          {"id": 3, "text": "模型训练和评估", "is_correct": true}, 
          {"id": 4, "text": "直接部署未经测试的模型", "is_correct": false}]', 
    '{"1": "数据收集和预处理是机器学习项目的第一步", 
      "2": "特征工程对模型性能有重要影响", 
      "3": "模型训练和评估是核心步骤", 
      "4": "模型应该经过充分测试和验证后再部署"}', 1); 

-- 插入目录资源数据
INSERT INTO `lms`.`edu_folder_resource` (
    `unit_id`, `parent_id`, `title`, `description`, 
    `resource_type`, `file_url`, `file_type`, `creator_id`
) VALUES
-- Python入门课程目录结构
-- 顶级文件夹
(1, NULL, 'Python入门学习资料', 'Python入门所需的各类学习资料和代码示例', 
    'folder', NULL, NULL, 1),
(1, NULL, 'Python环境配置指南', 'Python开发环境的安装和配置指南', 
    'folder', NULL, NULL, 1),
-- Python入门学习资料子文件
(1, 1, 'Python基础语法手册', 'Python基础语法速查手册', 
    'file', 'https://example.com/files/python-syntax-handbook.pdf', 'pdf', 1),
(1, 1, 'Python标准库文档', 'Python标准库函数和模块的详细文档', 
    'file', 'https://example.com/files/python-stdlib-doc.pdf', 'pdf', 1),
(1, 1, 'Python初学者入门代码', '适合初学者的Python代码示例集', 
    'file', 'https://example.com/files/python-beginner-code.zip', 'zip', 1),
-- Python环境配置指南子文件
(1, 2, 'Windows系统Python安装指南', 'Windows系统下Python环境的详细安装步骤', 
    'file', 'https://example.com/files/python-windows-install.pdf', 'pdf', 1),
(1, 2, 'Mac系统Python安装指南', 'Mac系统下Python环境的详细安装步骤', 
    'file', 'https://example.com/files/python-mac-install.pdf', 'pdf', 1),
(1, 2, 'Linux系统Python安装指南', 'Linux系统下Python环境的详细安装步骤', 
    'file', 'https://example.com/files/python-linux-install.pdf', 'pdf', 1),
(1, 2, 'VSCode配置Python开发环境', 'VSCode编辑器配置Python开发环境的教程', 
    'file', 'https://example.com/files/vscode-python-setup.pdf', 'pdf', 1),

-- Python基础语法课程目录结构
-- 顶级文件夹
(2, NULL, '代码示例', 'Python基础语法的代码示例集', 
    'folder', NULL, NULL, 1),
(2, NULL, '练习题', 'Python基础语法的练习题和答案', 
    'folder', NULL, NULL, 1),
-- 代码示例子文件
(2, 9, '变量和数据类型示例', 'Python变量和数据类型的代码示例', 
    'file', 'https://example.com/files/python-variables-examples.py', 'py', 1),
(2, 9, '运算符示例', 'Python运算符的代码示例', 
    'file', 'https://example.com/files/python-operators-examples.py', 'py', 1),
(2, 9, '字符串操作示例', 'Python字符串操作的代码示例', 
    'file', 'https://example.com/files/python-strings-examples.py', 'py', 1),
-- 练习题子文件
(2, 10, '变量和数据类型练习', 'Python变量和数据类型的练习题', 
    'file', 'https://example.com/files/python-variables-exercises.pdf', 'pdf', 1),
(2, 10, '运算符练习', 'Python运算符的练习题', 
    'file', 'https://example.com/files/python-operators-exercises.pdf', 'pdf', 1),
(2, 10, '字符串操作练习', 'Python字符串操作的练习题', 
    'file', 'https://example.com/files/python-strings-exercises.pdf', 'pdf', 1),
(2, 10, '习题答案', 'Python基础语法练习题的参考答案', 
    'file', 'https://example.com/files/python-basics-answers.pdf', 'pdf', 1),

-- 数据结构与算法课程目录结构
-- 顶级文件夹
(5, NULL, '算法分析资料', '算法复杂度分析的相关资料', 
    'folder', NULL, NULL, 1),
(5, NULL, '算法实现代码', '常见算法的Python实现代码', 
    'folder', NULL, NULL, 1),
-- 算法分析资料子文件
(5, 18, '时间复杂度分析方法', '算法时间复杂度的分析方法和示例', 
    'file', 'https://example.com/files/time-complexity-analysis.pdf', 'pdf', 1),
(5, 18, '空间复杂度分析方法', '算法空间复杂度的分析方法和示例', 
    'file', 'https://example.com/files/space-complexity-analysis.pdf', 'pdf', 1),
(5, 18, '常见算法复杂度对比', '常见算法的时间和空间复杂度对比', 
    'file', 'https://example.com/files/algorithm-complexity-comparison.pdf', 'pdf', 1),
-- 算法实现代码子文件
(5, 19, '排序算法实现', '常见排序算法的Python实现', 
    'file', 'https://example.com/files/sorting-algorithms.py', 'py', 1),
(5, 19, '搜索算法实现', '常见搜索算法的Python实现', 
    'file', 'https://example.com/files/searching-algorithms.py', 'py', 1),
(5, 19, '递归算法实现', '经典递归算法的Python实现', 
    'file', 'https://example.com/files/recursion-algorithms.py', 'py', 1),

-- 软件设计模式课程目录结构
-- 顶级文件夹
(9, NULL, '设计模式分类', '不同类型设计模式的详细介绍', 
    'folder', NULL, NULL, 1),
(9, NULL, '设计模式代码实现', '各种设计模式的代码实现示例', 
    'folder', NULL, NULL, 1),
-- 设计模式分类子文件
(9, 26, '创建型模式详解', '创建型设计模式的详细介绍和应用场景', 
    'file', 'https://example.com/files/creational-patterns.pdf', 'pdf', 1),
(9, 26, '结构型模式详解', '结构型设计模式的详细介绍和应用场景', 
    'file', 'https://example.com/files/structural-patterns.pdf', 'pdf', 1),
(9, 26, '行为型模式详解', '行为型设计模式的详细介绍和应用场景', 
    'file', 'https://example.com/files/behavioral-patterns.pdf', 'pdf', 1),
-- 设计模式代码实现子文件
(9, 27, '创建型模式代码示例', '创建型设计模式的Python代码实现', 
    'file', 'https://example.com/files/creational-patterns-code.py', 'py', 1),
(9, 27, '结构型模式代码示例', '结构型设计模式的Python代码实现', 
    'file', 'https://example.com/files/structural-patterns-code.py', 'py', 1),
(9, 27, '行为型模式代码示例', '行为型设计模式的Python代码实现', 
    'file', 'https://example.com/files/behavioral-patterns-code.py', 'py', 1),
(9, 27, '设计模式实际应用案例', '设计模式在实际项目中的应用案例代码', 
    'file', 'https://example.com/files/design-patterns-examples.zip', 'zip', 1),

-- 机器学习课程目录结构
-- 顶级文件夹
(13, NULL, '机器学习基础资料', '机器学习基础理论和概念的学习资料', 
    'folder', NULL, NULL, 1),
(13, NULL, '机器学习实践代码', '机器学习算法的实践代码和数据集', 
    'folder', NULL, NULL, 1),
-- 机器学习基础资料子文件
(13, 34, '机器学习概念导论', '机器学习的基本概念和术语解释', 
    'file', 'https://example.com/files/ml-concepts-intro.pdf', 'pdf', 1),
(13, 34, '常见机器学习算法概述', '常见机器学习算法的原理和适用场景', 
    'file', 'https://example.com/files/ml-algorithms-overview.pdf', 'pdf', 1),
(13, 34, '机器学习数学基础', '机器学习所需的数学基础知识', 
    'file', 'https://example.com/files/ml-math-foundation.pdf', 'pdf', 1),
-- 机器学习实践代码子文件
(13, 35, '监督学习代码示例', '监督学习算法的Python实现代码', 
    'file', 'https://example.com/files/supervised-learning-code.py', 'py', 1),
(13, 35, '无监督学习代码示例', '无监督学习算法的Python实现代码', 
    'file', 'https://example.com/files/unsupervised-learning-code.py', 'py', 1),
(13, 35, '机器学习实践数据集', '机器学习算法练习用的数据集', 
    'file', 'https://example.com/files/ml-practice-datasets.zip', 'zip', 1),
(13, 35, '模型评估与调优示例', '机器学习模型评估与超参数调优的代码示例', 
    'file', 'https://example.com/files/model-evaluation-tuning.py', 'py', 1);



