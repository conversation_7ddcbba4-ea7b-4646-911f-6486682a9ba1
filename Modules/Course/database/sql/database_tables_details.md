# Bingo LMS数据库表详细说明

本文档详细说明了Bingo LMS系统中各个数据表的结构、字段含义及业务用途。开发人员可参考此文档了解系统数据结构。

## 用户权限管理模块

### bingo_user (用户表)

**功能说明**：存储系统所有用户信息，包括学生、教师和管理员的基本信息。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 用户ID，自增主键 |
| account | VARCHAR(50) | 是 | 登录账号，唯一 |
| password | VARCHAR(100) | 是 | 加密后的密码 |
| role_id | int | 是 | 用户角色，关联bingo_role表 |
| first_name | VARCHAR(50) | 是 | 姓氏 |
| last_name | VARCHAR(50) | 是 | 名字 |
| email | VARCHAR(100) | 是 | 电子邮箱 |
| show_email_type | int | 是 | 显示邮箱类型：1所有、2不显示、3仅管理员 |
| moodlenet_account | VARCHAR(50) | 否 | Moodle账号 |
| city_address | VARCHAR(200) | 否 | 城市地址: 省/市 |
| country | VARCHAR(50) | 否 | 国家 |
| timezone | VARCHAR(50) | 否 | 时区 |
| introduction | TEXT | 否 | 简介 |
| code | VARCHAR(50) | 是 | 编号，唯一 |
| avatar_url | VARCHAR(500) | 否 | 头像URL |
| avatar_description | TEXT | 否 | 头像说明 |
| interests | TEXT | 否 | 兴趣 |
| phone_number | VARCHAR(50) | 否 | 座机号码 |
| mobile_number | VARCHAR(50) | 否 | 手机号码 |
| detailed_address | TEXT | 否 | 详细地址 |
| department | VARCHAR(50) | 否 | 科系 |
| additional_last_name | VARCHAR(50) | 否 | 附加名字 |
| additional_first_name | VARCHAR(50) | 否 | 附加姓氏 |
| additional_middle_name | VARCHAR(50) | 否 | 附加中间名 |
| additional_alias | VARCHAR(50) | 否 | 附加别名 |
| status | int | 是 | 用户状态：1正常、0禁用 |
| last_login_time | DATETIME | 否 | 上次登录时间 |
| token | VARCHAR(255) | 否 | 登录令牌 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：account、code、account+deleted_at
- 普通索引：role_id、email、status

### bingo_role (角色表)

**功能说明**：定义系统中的各种角色，如学生、教师、管理员等。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 角色ID，自增主键 |
| name | VARCHAR(50) | 是 | 角色名称 |
| code | VARCHAR(50) | 是 | 角色编码，唯一 |
| description | TEXT | 否 | 角色描述 |
| status | tinyint | 是 | 状态：1启用、0禁用 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：code、name+deleted_at
- 普通索引：status

### bingo_permission (权限表)

**功能说明**：存储系统中的各种权限项，按模块和操作类型组织。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 权限ID，自增主键 |
| name | VARCHAR(100) | 是 | 权限名称 |
| code | VARCHAR(100) | 是 | 权限编码，唯一 |
| description | TEXT | 否 | 权限描述 |
| module | VARCHAR(50) | 是 | 所属模块 |
| type | VARCHAR(20) | 是 | 权限类型：menu菜单、operation操作 |
| parent_id | int | 否 | 父权限ID |
| sort_order | INT | 是 | 排序序号 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：code+deleted_at
- 普通索引：module、parent_id、type

### bingo_role_permission (角色权限关联表)

**功能说明**：建立角色与权限之间的多对多关系，定义每个角色拥有的权限。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 关联ID，自增主键 |
| role_id | int | 是 | 角色ID |
| permission_id | int | 是 | 权限ID |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：role_id+permission_id+deleted_at
- 普通索引：role_id、permission_id

## 课程管理模块

### bingo_course_category (课程分类表)

**功能说明**：定义课程分类体系，支持多级分类结构。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 分类ID，自增主键 |
| name | VARCHAR(100) | 是 | 分类名称 |
| parent_id | int | 否 | 父分类ID |
| sort_order | INT | 是 | 排序序号 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 普通索引：parent_id

### bingo_course (课程表)

**功能说明**：存储课程的基本信息，包含中英文标题、描述、开课日期、结课日期等。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 课程ID，自增主键 |
| course_code | VARCHAR(50) | 是 | 课程代码，唯一 |
| title_cn | VARCHAR(200) | 是 | 课程中文标题 |
| title_en | VARCHAR(200) | 是 | 课程英文标题 |
| description_cn | TEXT | 否 | 课程中文描述 |
| description_en | TEXT | 否 | 课程英文描述 |
| image_url | VARCHAR(500) | 否 | 课程图片URL |
| category_id | int | 是 | 课程分类ID |
| is_template | BOOLEAN | 是 | 是否为模板课程 |
| start_date | DATE | 否 | 开课日期 |
| end_date | DATE | 否 | 结课日期 |
| report_deadline | DATE | 否 | 反思报告提交截止日期 |
| max_students | SMALLINT UNSIGNED | 是 | 最大学生数，默认30 |
| status | int | 是 | 课程状态：1准备中、2进行中、3已完成、4已归档 |
| is_active | BOOLEAN | 是 | 是否激活 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：course_code
- 普通索引：category_id、status

### bingo_course_user (课程用户管理表)

**功能说明**：建立用户与课程之间的多对多关系，记录哪些用户（学生/教师）参与了哪些课程。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 记录ID，自增主键 |
| user_id | int | 是 | 用户ID |
| course_id | int | 是 | 课程ID |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：user_id+course_id+deleted_at
- 普通索引：user_id、course_id

### bingo_course_unit (课程单元表)

**功能说明**：定义课程的教学单元结构，每个课程包含多个教学单元。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 单元ID，自增主键 |
| course_id | int | 是 | 所属课程ID |
| unit_number | TINYINT UNSIGNED | 是 | 单元编号（1-11） |
| title_cn | VARCHAR(200) | 是 | 单元中文标题 |
| title_en | VARCHAR(200) | 是 | 单元英文标题 |
| title | VARCHAR(200) | 是 | 单元标题 |
| description | TEXT | 否 | 单元描述 |
| icon_url | VARCHAR(500) | 否 | 单元图标URL |
| sort_order | INT | 是 | 排序序号 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：course_id+unit_number+deleted_at

## 教学资源模块

### bingo_video_resource (视频资源表)

**功能说明**：存储课程单元中的视频资源，包含视频URL、时长、观看控制等信息。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 视频资源ID，自增主键 |
| unit_id | int | 是 | 所属单元ID |
| title | VARCHAR(200) | 是 | 视频标题 |
| description | TEXT | 否 | 视频描述 |
| video_url | VARCHAR(500) | 是 | 视频URL |
| duration | INT UNSIGNED | 是 | 视频时长（秒） |
| allow_fast_forward | BOOLEAN | 是 | 是否允许快进 |
| sequence | INT UNSIGNED | 是 | 在单元中的顺序 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 普通索引：unit_id+sequence

### bingo_unit_file (单元文件表)

**功能说明**：存储课程单元中的文件资源，如PDF、Word文档等。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 文件ID，自增主键 |
| unit_id | int | 是 | 所属单元ID |
| title | VARCHAR(200) | 是 | 文件标题 |
| description | TEXT | 否 | 描述说明 |
| file_url | VARCHAR(500) | 是 | 文件URL |
| file_type | VARCHAR(50) | 是 | 文件类型，默认pdf，其他类型：word,excel,ppt |
| is_required | tinyint | 是 | 是否必读：1必读、0非必读 |
| sequence | INT UNSIGNED | 是 | 在单元中的顺序 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 普通索引：unit_id

## 题库与测验模块

### bingo_question_category (题库分类表)

**功能说明**：定义题库分类体系，支持多级分类结构。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 分类ID，自增主键 |
| name | VARCHAR(100) | 是 | 分类名称 |
| description | TEXT | 否 | 分类描述 |
| parent_id | int | 否 | 父分类ID，NULL表示顶级分类 |
| level | TINYINT | 是 | 分类层级，1为顶级 |
| sort_order | INT | 是 | 排序序号 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 普通索引：parent_id、level、sort_order

### bingo_question_bank (题库表)

**功能说明**：存储测验题目，包含题目内容、选项、正确答案等。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 题目ID，自增主键 |
| code | VARCHAR(50) | 是 | 题目编号，唯一 |
| category_id | int | 是 | 题目分类ID |
| unit_id | int | 是 | 所属单元ID |
| question_text | TEXT | 是 | 题目内容 |
| option_a | TEXT | 是 | 选项A，JSON格式 |
| option_b | TEXT | 是 | 选项B，JSON格式 |
| option_c | TEXT | 是 | 选项C，JSON格式 |
| option_d | TEXT | 是 | 选项D，JSON格式 |
| correct_option | TINYINT UNSIGNED | 是 | 正确选项：1A、2B、3C、4D |
| question_type | TINYINT UNSIGNED | 是 | 题目类型：1单选、2多选 |
| option_display_type | TINYINT UNSIGNED | 是 | 选项显示方式：1a、2A、3i、4I、5不显示 |
| score | DECIMAL(5,2) | 是 | 题目分值 |
| feedback | TEXT | 否 | 反馈信息 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：code
- 普通索引：unit_id、question_type、category_id

### bingo_quiz_questions (测验题目关联表)

**功能说明**：关联测验记录与题目，定义测验中题目的顺序和分值。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 关联ID，自增主键 |
| unit_id | int | 是 | 所属单元ID |
| student_id | int | 是 | 学生ID |
| question_id | int | 是 | 题目ID |
| sequence | INT UNSIGNED | 是 | 题目顺序 |
| score | DECIMAL(5,2) | 是 | 题目分值 |
| is_required | tinyint | 是 | 是否必答：1必答、0非必答 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| quiz_record_id | int | 是 | 测验记录ID |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：quiz_record_id+question_id+deleted_at
- 普通索引：quiz_record_id、question_id

### bingo_student_quiz_attempt (学生测验参与记录表)

**功能说明**：记录学生参与测验的情况，包含开始时间、完成时间、得分等信息。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 测验参与记录ID，自增主键 |
| student_id | int | 是 | 学生ID |
| course_id | int | 是 | 课程ID |
| unit_id | int | 是 | 单元ID |
| attempt_number | SMALLINT UNSIGNED | 是 | 尝试次数 |
| start_time | DATETIME | 是 | 开始时间 |
| end_time | DATETIME | 否 | 完成时间 |
| score | TINYINT UNSIGNED | 是 | 得分 |
| total_questions | TINYINT UNSIGNED | 是 | 题目总数 |
| quiz_content | TEXT | 否 | 试卷内容 |
| status | int | 是 | 状态：1进行中、2已提交 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 普通索引：student_id+unit_id
- 唯一索引：student_id+unit_id+deleted_at

## 学习进度模块

### bingo_student_unit_progress (学生单元学习进度表)

**功能说明**：跟踪学生在各单元的学习进度，记录开始学习时间、完成时间和状态。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 进度记录ID，自增主键 |
| student_id | int | 是 | 学生ID |
| course_id | int | 是 | 课程ID |
| unit_id | int | 是 | 单元ID |
| start_time | DATETIME | 否 | 开始学习时间 |
| complete_time | DATETIME | 否 | 完成时间 |
| status | int | 是 | 状态：1未开始、2进行中、3已完成 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：student_id+course_id+unit_id+deleted_at

### bingo_student_video_watching (学生视频观看记录表)

**功能说明**：详细记录学生视频观看情况，包含观看时长、进度百分比、完成状态等。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 观看记录ID，自增主键 |
| student_id | int | 是 | 学生ID |
| video_id | int | 是 | 视频ID |
| course_id | int | 是 | 课程ID |
| start_time | DATETIME | 否 | 开始观看时间 |
| last_position | INT UNSIGNED | 是 | 上次观看位置（秒） |
| watch_duration | INT UNSIGNED | 是 | 累计观看时长（秒） |
| completion_percentage | DECIMAL(5,2) | 是 | 完成百分比 |
| is_completed | tinyint | 是 | 是否完成观看：1完成、0未完成 |
| completed_time | DATETIME | 否 | 完成观看时间 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：student_id+video_id+course_id+deleted_at

## 反思报告模块

### bingo_reflection_criteria (反思报告评分标准表)

**功能说明**：定义评估反思报告的标准，包含评分项名称和描述。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 评分标准ID，自增主键 |
| criteria_name | VARCHAR(200) | 是 | 评分项名称 |
| description | TEXT | 否 | 评分项描述 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：criteria_name+deleted_at

### bingo_reflection_grading (反思报告评分详情表)

**功能说明**：存储对反思报告的具体评分，关联反思报告和评分标准。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 评分记录ID，自增主键 |
| reflection_id | int | 是 | 反思报告ID |
| criteria_id | int | 是 | 评分标准ID |
| is_passed | tinyint | 是 | 是否通过：1通过、0未通过 |
| score_percentage | DECIMAL(5,2) | 否 | 标准占用分数 |
| comment | TEXT | 否 | 评语 |
| sort_order | int | 是 | 排序 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：reflection_id+criteria_id+deleted_at

### bingo_reflection_report (反思报告表)

**功能说明**：存储学生提交的反思报告内容，包含提交状态、评分、教师评语等。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 反思报告ID，自增主键 |
| student_id | int | 是 | 学生ID |
| course_id | int | 是 | 课程ID |
| title | VARCHAR(200) | 是 | 反思报告标题 |
| content | TEXT | 是 | 反思报告内容 |
| file_url | VARCHAR(500) | 否 | 报告文件URL |
| upload_time | DATETIME | 否 | 上传时间 |
| similarity_percentage | DECIMAL(5,2) | 否 | 相似度百分比（查重结果） |
| similarity_status | int | 是 | 相似度检测状态：1待检测、2已完成、3失败 |
| similarity_report_url | VARCHAR(500) | 否 | 相似度报告URL |
| submission_status | int | 是 | 提交状态：1草稿、2已提交、3已退回、4已重新提交 |
| submission_time | DATETIME | 否 | 提交时间 |
| overall_status | int | 是 | 整体状态：1待评分、2通过、3未通过 |
| teacher_comment | TEXT | 否 | 教师整体评语 |
| score | DECIMAL(5,2) | 否 | 得分 |
| graded_by | int | 否 | 评分教师ID |
| graded_time | DATETIME | 否 | 评分时间 |
| late_date | DATETIME | 否 | 延期日期（到指定日期） |
| start_date | DATETIME | 否 | 开始日期 |
| end_date | DATETIME | 否 | 截止日期 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：student_id+course_id+deleted_at

### bingo_reflection_submission_settings (反思报告提交设置表)

**功能说明**：定义反思报告提交的规则和要求，包含截止日期、文件大小限制、相似度检查等设置。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 设置ID，自增主键 |
| course_id | int | 是 | 课程ID |
| start_date | DATETIME | 是 | 开始提交日期 |
| due_date | DATETIME | 是 | 截止日期 |
| late_submission_allowed | tinyint | 是 | 是否允许迟交：1允许、0不允许 |
| late_submission_deadline | DATETIME | 否 | 迟交截止日期 |
| max_file_size | INT UNSIGNED | 是 | 最大文件大小（KB） |
| similarity_check_required | tinyint | 是 | 是否需要相似度检查：1需要、0不需要 |
| similarity_threshold | DECIMAL(5,2) | 是 | 相似度阈值百分比 |
| file_name_format | VARCHAR(100) | 否 | 文件命名格式 |
| word_count_min | INT UNSIGNED | 否 | 最小字数要求 |
| word_count_max | INT UNSIGNED | 否 | 最大字数限制 |
| instructions | TEXT | 否 | 提交说明 |
| declaration_text | TEXT | 否 | 声明文本 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 唯一索引：course_id
- 普通索引：course_id

## 系统管理模块

### bingo_system_log (系统操作日志表)

**功能说明**：记录系统中的各种操作，用于审计和问题排查。

| 字段名 | 数据类型 | 是否必填 | 说明 |
|-------|---------|---------|------|
| id | int | 是 | 日志ID，自增主键 |
| user_id | int | 是 | 操作用户ID |
| action | VARCHAR(50) | 是 | 操作类型（如：登录、登出、新增、修改、删除） |
| target_type | VARCHAR(50) | 是 | 目标类型（如：用户、课程、班级、单元、视频、测验、反思报告、评分标准、评估记录） |
| target_id | int | 否 | 目标ID |
| description | TEXT | 否 | 操作描述 |
| ip_address | VARCHAR(50) | 否 | IP地址 |
| user_agent | VARCHAR(500) | 否 | 用户代理 |
| created_by | int | 是 | 创建人ID |
| created_at | DATETIME | 是 | 创建时间 |
| updated_at | DATETIME | 否 | 更新时间 |
| deleted_at | DATETIME | 否 | 删除时间 |

**索引**：
- 主键：id
- 普通索引：user_id、action、target_type+target_id 