# Bingo LMS系统业务流程时序图

本文档使用时序图展示Bingo LMS系统中的主要业务流程和交互逻辑。时序图采用Mermaid语法描述，可通过支持Mermaid的Markdown查看器或在线工具渲染为图形。

## 1. 用户登录流程

```mermaid
sequenceDiagram
    autonumber
    participant 用户
    participant 登录界面
    participant 认证控制器
    participant 用户表(bingo_user)
    participant 角色表(bingo_role)
    participant 权限表(bingo_permission)
    participant 角色权限表(bingo_role_permission)
    participant 日志控制器
    participant 系统日志表(bingo_system_log)

    用户->>登录界面: 输入账号密码
    登录界面->>认证控制器: 提交登录请求
    认证控制器->>用户表(bingo_user): 查询用户信息
    用户表(bingo_user)-->>认证控制器: 返回用户数据
    认证控制器->>认证控制器: 验证密码
    alt 密码正确
        认证控制器->>角色表(bingo_role): 获取用户角色
        角色表(bingo_role)-->>认证控制器: 返回角色信息
        认证控制器->>角色权限表(bingo_role_permission): 获取角色权限
        角色权限表(bingo_role_permission)->>权限表(bingo_permission): 查询权限详情
        权限表(bingo_permission)-->>角色权限表(bingo_role_permission): 返回权限信息
        角色权限表(bingo_role_permission)-->>认证控制器: 返回角色权限
        认证控制器->>认证控制器: 生成令牌
        认证控制器->>用户表(bingo_user): 更新登录时间和令牌
        认证控制器->>日志控制器: 记录登录日志
        日志控制器->>系统日志表(bingo_system_log): 保存登录日志
        认证控制器-->>登录界面: 返回登录成功和令牌
        登录界面-->>用户: 跳转到系统主页
    else 密码错误
        认证控制器->>日志控制器: 记录登录失败
        日志控制器->>系统日志表(bingo_system_log): 保存失败日志
        认证控制器-->>登录界面: 返回登录失败
        登录界面-->>用户: 显示错误信息
    end
```

## 2. 学生课程学习流程

```mermaid
sequenceDiagram
    autonumber
    participant 学生
    participant 课程界面
    participant 单元界面
    participant 视频播放器
    participant 测验界面
    participant 课程控制器
    participant 单元控制器
    participant 视频控制器
    participant 测验控制器
    participant 课程表(bingo_course)
    participant 课程单元表(bingo_course_unit)
    participant 视频资源表(bingo_video_resource)
    participant 单元文件表(bingo_unit_file)
    participant 学生单元进度表(bingo_student_unit_progress)
    participant 学生视频观看表(bingo_student_video_watching)
    participant 题库表(bingo_question_bank)
    participant 学生测验记录表(bingo_student_quiz_attempt)

    学生->>课程界面: 访问课程列表
    课程界面->>课程控制器: 获取学生课程
    课程控制器->>课程表(bingo_course): 查询学生课程
    课程表(bingo_course)-->>课程控制器: 返回课程列表
    课程控制器-->>课程界面: 显示课程
    课程界面-->>学生: 查看课程列表

    学生->>课程界面: 选择课程
    课程界面->>单元控制器: 获取课程单元
    单元控制器->>课程单元表(bingo_course_unit): 查询课程单元
    课程单元表(bingo_course_unit)-->>单元控制器: 返回单元列表
    单元控制器->>学生单元进度表(bingo_student_unit_progress): 获取学习进度
    学生单元进度表(bingo_student_unit_progress)-->>单元控制器: 返回进度信息
    单元控制器-->>课程界面: 返回单元和进度
    课程界面-->>学生: 显示课程单元

    学生->>单元界面: 选择学习单元
    单元界面->>单元控制器: 获取单元详情
    单元控制器->>视频资源表(bingo_video_resource): 获取视频资源
    视频资源表(bingo_video_resource)-->>单元控制器: 返回视频列表
    单元控制器->>单元文件表(bingo_unit_file): 获取文件资源
    单元文件表(bingo_unit_file)-->>单元控制器: 返回文件列表
    单元控制器->>学生单元进度表(bingo_student_unit_progress): 更新单元进度(开始学习)
    单元控制器-->>单元界面: 返回单元资源
    单元界面-->>学生: 显示单元学习资源

    学生->>视频播放器: 观看视频
    视频播放器->>视频控制器: 记录观看进度
    视频控制器->>学生视频观看表(bingo_student_video_watching): 更新观看记录
    视频控制器-->>视频播放器: 确认记录
    视频播放器-->>学生: 显示视频内容

    学生->>测验界面: 进入单元测验
    测验界面->>测验控制器: 获取测验题目
    测验控制器->>题库表(bingo_question_bank): 获取题库题目
    题库表(bingo_question_bank)-->>测验控制器: 返回题目
    测验控制器->>学生测验记录表(bingo_student_quiz_attempt): 创建测验记录
    测验控制器-->>测验界面: 返回测验题目
    测验界面-->>学生: 显示测验题目

    学生->>测验界面: 提交测验答案
    测验界面->>测验控制器: 提交答案评分
    测验控制器->>测验控制器: 评分计算
    测验控制器->>学生测验记录表(bingo_student_quiz_attempt): 保存测验结果
    测验控制器->>学生单元进度表(bingo_student_unit_progress): 更新单元进度(完成)
    测验控制器-->>测验界面: 返回测验结果
    测验界面-->>学生: 显示测验成绩
```

## 3. 反思报告提交与评阅流程

```mermaid
sequenceDiagram
    autonumber
    participant 学生
    participant 教师
    participant 反思报告界面
    participant 报告评阅界面
    participant 反思报告控制器
    participant 相似度检测服务
    participant 反思报告表(bingo_reflection_report)
    participant 反思报告设置表(bingo_reflection_submission_settings)
    participant 评分标准表(bingo_reflection_criteria)
    participant 评分详情表(bingo_reflection_grading)

    学生->>反思报告界面: 访问反思报告页面
    反思报告界面->>反思报告控制器: 获取报告提交设置
    反思报告控制器->>反思报告设置表(bingo_reflection_submission_settings): 查询提交设置
    反思报告设置表(bingo_reflection_submission_settings)-->>反思报告控制器: 返回设置信息
    反思报告控制器->>反思报告表(bingo_reflection_report): 查询学生报告状态
    反思报告表(bingo_reflection_report)-->>反思报告控制器: 返回报告状态
    反思报告控制器-->>反思报告界面: 返回报告相关信息
    反思报告界面-->>学生: 显示报告编辑界面

    学生->>反思报告界面: 编写/上传报告
    反思报告界面->>反思报告控制器: 保存报告草稿
    反思报告控制器->>反思报告表(bingo_reflection_report): 保存/更新报告
    反思报告表(bingo_reflection_report)-->>反思报告控制器: 确认保存
    反思报告控制器-->>反思报告界面: 返回保存结果
    反思报告界面-->>学生: 显示保存成功

    学生->>反思报告界面: 提交报告
    反思报告界面->>反思报告控制器: 提交报告请求
    反思报告控制器->>反思报告设置表(bingo_reflection_submission_settings): 验证提交时间
    反思报告控制器->>反思报告表(bingo_reflection_report): 更新报告状态为已提交
    alt 需要相似度检测
        反思报告控制器->>相似度检测服务: 发送相似度检测请求
        相似度检测服务->>相似度检测服务: 执行相似度分析
        相似度检测服务-->>反思报告控制器: 返回相似度结果
        反思报告控制器->>反思报告表(bingo_reflection_report): 更新相似度数据
    end
    反思报告控制器-->>反思报告界面: 返回提交结果
    反思报告界面-->>学生: 显示提交成功

    教师->>报告评阅界面: 访问报告评阅页面
    报告评阅界面->>反思报告控制器: 获取待评阅报告
    反思报告控制器->>反思报告表(bingo_reflection_report): 查询待评阅报告
    反思报告表(bingo_reflection_report)-->>反思报告控制器: 返回报告列表
    反思报告控制器-->>报告评阅界面: 返回待评阅报告
    报告评阅界面-->>教师: 显示报告列表

    教师->>报告评阅界面: 选择报告评阅
    报告评阅界面->>反思报告控制器: 获取报告详情
    反思报告控制器->>反思报告表(bingo_reflection_report): 查询报告内容
    反思报告表(bingo_reflection_report)-->>反思报告控制器: 返回报告数据
    反思报告控制器->>评分标准表(bingo_reflection_criteria): 获取评分标准
    评分标准表(bingo_reflection_criteria)-->>反思报告控制器: 返回评分标准
    反思报告控制器-->>报告评阅界面: 返回报告和标准
    报告评阅界面-->>教师: 显示报告和评分表单

    教师->>报告评阅界面: 提交评分和意见
    报告评阅界面->>反思报告控制器: 保存评分结果
    反思报告控制器->>评分详情表(bingo_reflection_grading): 保存各项评分
    评分详情表(bingo_reflection_grading)-->>反思报告控制器: 确认保存评分
    反思报告控制器->>反思报告表(bingo_reflection_report): 更新报告状态和总分
    反思报告表(bingo_reflection_report)-->>反思报告控制器: 确认更新
    反思报告控制器-->>报告评阅界面: 返回评分结果
    报告评阅界面-->>教师: 显示评分完成

    学生->>反思报告界面: 查看评分结果
    反思报告界面->>反思报告控制器: 获取评分详情
    反思报告控制器->>反思报告表(bingo_reflection_report): 查询报告状态
    反思报告表(bingo_reflection_report)-->>反思报告控制器: 返回报告信息
    反思报告控制器->>评分详情表(bingo_reflection_grading): 查询评分详情
    评分详情表(bingo_reflection_grading)-->>反思报告控制器: 返回评分数据
    反思报告控制器-->>反思报告界面: 返回评分结果
    反思报告界面-->>学生: 显示评分和教师意见
```

## 4. 课程管理流程

```mermaid
sequenceDiagram
    autonumber
    participant 教师
    participant 课程管理界面
    participant 单元管理界面
    participant 资源管理界面
    participant 测验管理界面
    participant 课程控制器
    participant 单元控制器
    participant 资源控制器
    participant 测验控制器
    participant 课程表(bingo_course)
    participant 课程分类表(bingo_course_category)
    participant 课程单元表(bingo_course_unit)
    participant 视频资源表(bingo_video_resource)
    participant 单元文件表(bingo_unit_file)
    participant 题库表(bingo_question_bank)
    participant 题库分类表(bingo_question_category)

    教师->>课程管理界面: 访问课程管理
    课程管理界面->>课程控制器: 获取教师课程
    课程控制器->>课程表(bingo_course): 查询教师课程
    课程表(bingo_course)-->>课程控制器: 返回课程列表
    课程控制器-->>课程管理界面: 显示课程列表
    课程管理界面-->>教师: 查看课程列表

    教师->>课程管理界面: 创建新课程
    课程管理界面->>课程控制器: 获取课程分类
    课程控制器->>课程分类表(bingo_course_category): 查询分类列表
    课程分类表(bingo_course_category)-->>课程控制器: 返回分类数据
    课程控制器-->>课程管理界面: 显示分类选项
    课程管理界面-->>教师: 填写课程信息

    教师->>课程管理界面: 提交课程信息
    课程管理界面->>课程控制器: 保存课程
    课程控制器->>课程表(bingo_course): 创建课程记录
    课程表(bingo_course)-->>课程控制器: 确认创建
    课程控制器-->>课程管理界面: 返回创建结果
    课程管理界面-->>教师: 显示创建成功

    教师->>单元管理界面: 管理课程单元
    单元管理界面->>单元控制器: 获取课程单元
    单元控制器->>课程单元表(bingo_course_unit): 查询单元列表
    课程单元表(bingo_course_unit)-->>单元控制器: 返回单元数据
    单元控制器-->>单元管理界面: 显示单元列表
    单元管理界面-->>教师: 查看/创建单元

    教师->>资源管理界面: 管理单元资源
    资源管理界面->>资源控制器: 获取单元资源
    资源控制器->>视频资源表(bingo_video_resource): 查询视频资源
    视频资源表(bingo_video_resource)-->>资源控制器: 返回视频列表
    资源控制器->>单元文件表(bingo_unit_file): 查询文件资源
    单元文件表(bingo_unit_file)-->>资源控制器: 返回文件列表
    资源控制器-->>资源管理界面: 显示资源列表
    资源管理界面-->>教师: 查看/上传资源

    教师->>测验管理界面: 管理单元测验
    测验管理界面->>测验控制器: 获取题库题目
    测验控制器->>题库分类表(bingo_question_category): 查询题目分类
    题库分类表(bingo_question_category)-->>测验控制器: 返回分类数据
    测验控制器->>题库表(bingo_question_bank): 查询题目列表
    题库表(bingo_question_bank)-->>测验控制器: 返回题目数据
    测验控制器-->>测验管理界面: 显示题目列表
    测验管理界面-->>教师: 编辑/创建题目

    教师->>测验管理界面: 提交题目信息
    测验管理界面->>测验控制器: 保存题目
    测验控制器->>题库表(bingo_question_bank): 创建/更新题目
    题库表(bingo_question_bank)-->>测验控制器: 确认保存
    测验控制器-->>测验管理界面: 返回保存结果
    测验管理界面-->>教师: 显示保存成功
```

## 5. 学习进度监控流程

```mermaid
sequenceDiagram
    autonumber
    participant 教师
    participant 学生
    participant 进度监控界面
    participant 学生进度界面
    participant 进度控制器
    participant 学生单元进度表(bingo_student_unit_progress)
    participant 学生视频观看表(bingo_student_video_watching)
    participant 学生测验记录表(bingo_student_quiz_attempt)
    participant 课程表(bingo_course)
    participant 课程单元表(bingo_course_unit)
    participant 用户表(bingo_user)

    教师->>进度监控界面: 访问进度监控
    进度监控界面->>进度控制器: 获取课程列表
    进度控制器->>课程表(bingo_course): 查询教师课程
    课程表(bingo_course)-->>进度控制器: 返回课程数据
    进度控制器-->>进度监控界面: 显示课程选项
    进度监控界面-->>教师: 选择课程

    教师->>进度监控界面: 选择课程
    进度监控界面->>进度控制器: 获取学生列表
    进度控制器->>用户表(bingo_user): 查询课程学生
    用户表(bingo_user)-->>进度控制器: 返回学生列表
    进度控制器-->>进度监控界面: 显示学生列表
    进度监控界面-->>教师: 查看学生列表

    教师->>进度监控界面: 查看课程整体进度
    进度监控界面->>进度控制器: 获取整体统计
    进度控制器->>学生单元进度表(bingo_student_unit_progress): 统计单元完成情况
    学生单元进度表(bingo_student_unit_progress)-->>进度控制器: 返回统计数据
    进度控制器->>学生测验记录表(bingo_student_quiz_attempt): 统计测验完成情况
    学生测验记录表(bingo_student_quiz_attempt)-->>进度控制器: 返回测验数据
    进度控制器-->>进度监控界面: 返回统计结果
    进度监控界面-->>教师: 显示整体进度图表

    教师->>进度监控界面: 选择特定学生
    进度监控界面->>进度控制器: 获取学生详细进度
    进度控制器->>学生单元进度表(bingo_student_unit_progress): 查询学生单元进度
    学生单元进度表(bingo_student_unit_progress)-->>进度控制器: 返回单元进度
    进度控制器->>学生视频观看表(bingo_student_video_watching): 查询视频观看记录
    学生视频观看表(bingo_student_video_watching)-->>进度控制器: 返回观看数据
    进度控制器->>学生测验记录表(bingo_student_quiz_attempt): 查询测验成绩
    学生测验记录表(bingo_student_quiz_attempt)-->>进度控制器: 返回测验数据
    进度控制器-->>进度监控界面: 返回学生详细进度
    进度监控界面-->>教师: 显示学生学习情况

    学生->>学生进度界面: 查看个人进度
    学生进度界面->>进度控制器: 获取个人进度
    进度控制器->>学生单元进度表(bingo_student_unit_progress): 查询学生进度
    学生单元进度表(bingo_student_unit_progress)-->>进度控制器: 返回进度数据
    进度控制器->>学生测验记录表(bingo_student_quiz_attempt): 查询测验成绩
    学生测验记录表(bingo_student_quiz_attempt)-->>进度控制器: 返回测验数据
    进度控制器-->>学生进度界面: 返回进度统计
    学生进度界面-->>学生: 显示学习进度和成绩
```

## 6. 系统管理流程

```mermaid
sequenceDiagram
    autonumber
    participant 管理员
    participant 用户管理界面
    participant 角色权限界面
    participant 日志查询界面
    participant 用户控制器
    participant 角色控制器
    participant 权限控制器
    participant 日志控制器
    participant 用户表(bingo_user)
    participant 角色表(bingo_role)
    participant 权限表(bingo_permission)
    participant 角色权限表(bingo_role_permission)
    participant 系统日志表(bingo_system_log)

    管理员->>用户管理界面: 访问用户管理
    用户管理界面->>用户控制器: 获取用户列表
    用户控制器->>用户表(bingo_user): 查询用户数据
    用户表(bingo_user)-->>用户控制器: 返回用户列表
    用户控制器->>角色表(bingo_role): 获取角色选项
    角色表(bingo_role)-->>用户控制器: 返回角色数据
    用户控制器-->>用户管理界面: 返回用户和角色
    用户管理界面-->>管理员: 显示用户列表

    管理员->>用户管理界面: 创建/编辑用户
    用户管理界面->>用户控制器: 保存用户信息
    用户控制器->>用户表(bingo_user): 创建/更新用户
    用户表(bingo_user)-->>用户控制器: 确认操作
    用户控制器->>日志控制器: 记录操作日志
    日志控制器->>系统日志表(bingo_system_log): 保存操作日志
    用户控制器-->>用户管理界面: 返回操作结果
    用户管理界面-->>管理员: 显示操作成功

    管理员->>角色权限界面: 访问角色管理
    角色权限界面->>角色控制器: 获取角色列表
    角色控制器->>角色表(bingo_role): 查询角色数据
    角色表(bingo_role)-->>角色控制器: 返回角色列表
    角色控制器-->>角色权限界面: 显示角色列表
    角色权限界面-->>管理员: 查看角色列表

    管理员->>角色权限界面: 配置角色权限
    角色权限界面->>权限控制器: 获取权限列表
    权限控制器->>权限表(bingo_permission): 查询权限数据
    权限表(bingo_permission)-->>权限控制器: 返回权限列表
    权限控制器->>角色权限表(bingo_role_permission): 查询角色已有权限
    角色权限表(bingo_role_permission)-->>权限控制器: 返回已有权限
    权限控制器-->>角色权限界面: 返回权限和选中状态
    角色权限界面-->>管理员: 显示权限配置界面

    管理员->>角色权限界面: 提交权限配置
    角色权限界面->>角色控制器: 保存角色权限
    角色控制器->>角色权限表(bingo_role_permission): 更新角色权限
    角色权限表(bingo_role_permission)-->>角色控制器: 确认更新
    角色控制器->>日志控制器: 记录操作日志
    日志控制器->>系统日志表(bingo_system_log): 保存操作日志
    角色控制器-->>角色权限界面: 返回操作结果
    角色权限界面-->>管理员: 显示保存成功

    管理员->>日志查询界面: 访问系统日志
    日志查询界面->>日志控制器: 查询日志记录
    日志控制器->>系统日志表(bingo_system_log): 获取日志数据
    系统日志表(bingo_system_log)-->>日志控制器: 返回日志列表
    日志控制器-->>日志查询界面: 返回日志数据
    日志查询界面-->>管理员: 显示系统日志
```

以上时序图展示了Bingo LMS系统中的主要业务流程和交互过程，包括用户登录、学习课程、提交评阅反思报告、课程管理、学习进度监控和系统管理等核心功能。时序图通过描述不同角色、界面、控制器和数据表之间的交互关系，清晰地展示了系统的业务逻辑和数据流向。 