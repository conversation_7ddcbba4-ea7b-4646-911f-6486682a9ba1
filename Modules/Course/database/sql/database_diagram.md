# Bingo LMS数据库关系图与描述

## 数据库概述

Bingo LMS系统数据库包含21个主要表，涵盖了用户管理、权限管理、课程管理、教学资源管理、学习进度跟踪和反思报告评估等功能模块。

## 数据库表关系

```
+---------------------+       +----------------+       +--------------------+
| bingo_user          |------>| bingo_role     |------>| bingo_permission   |
+---------------------+       +----------------+       +--------------------+
         |                           |                          ^
         |                           |                          |
         |                           v                          |
         |                    +----------------+                |
         |                    | bingo_role_    |----------------+
         |                    | permission     |
         |                    +----------------+
         |
         |                    +----------------+
         |                    | bingo_course_  |
         |                    | category       |
         |                    +----------------+
         |                           ^
         |                           |
         v                           |
+---------------------+       +----------------+
| bingo_course_user   |------>| bingo_course   |
+---------------------+       +----------------+
         ^                           |
         |                           |
         |                           v
         |                    +----------------+       +--------------------+
         |                    | bingo_course_  |------>| bingo_video_       |
         |                    | unit           |       | resource           |
         |                    +----------------+       +--------------------+
         |                           |                          ^
         |                           |                          |
         |                           v                          |
+---------------------+       +----------------+       +--------------------+
| bingo_student_unit_ |------>| bingo_question |------>| bingo_question_    |
| progress            |       | bank           |       | category           |
+---------------------+       +----------------+       +--------------------+
         |                           |
         |                           |
         v                           v
+---------------------+       +----------------+       +--------------------+
| bingo_student_video_|       | bingo_quiz_    |<------| bingo_student_quiz_|
| watching            |       | questions      |       | attempt            |
+---------------------+       +----------------+       +--------------------+
                                                                |
                                                                |
                                                                v
+---------------------+       +----------------+       +--------------------+
| bingo_reflection_   |<------| bingo_         |<------| bingo_reflection_  |
| criteria            |       | reflection_    |       | submission_settings|
+---------------------+       | report         |       +--------------------+
         |                    +----------------+
         |                           ^
         v                           |
+---------------------+              |
| bingo_reflection_   |--------------+
| grading             |
+---------------------+

```

## 表结构说明

### 用户权限管理模块

#### bingo_user (用户表)
- 存储系统所有用户信息，包括学生、教师和管理员
- 包含基本身份信息、联系方式和账号状态等字段
- 关联角色表，确定用户权限

#### bingo_role (角色表)
- 定义系统中各种角色（如学生、教师、管理员等）
- 包含角色名称、编码和描述等信息

#### bingo_permission (权限表)
- 存储系统中各种权限项
- 按模块和操作类型组织权限
- 支持权限层级结构（通过parent_id）

#### bingo_role_permission (角色权限关联表)
- 建立角色与权限之间的多对多关系
- 定义每个角色拥有的具体权限

### 课程管理模块

#### bingo_course_category (课程分类表)
- 定义课程分类体系
- 支持多级分类结构（通过parent_id）

#### bingo_course (课程表)
- 存储课程基本信息
- 包含中英文标题、描述、开课日期、结课日期等
- 关联课程分类表

#### bingo_course_user (课程用户管理表)
- 建立用户与课程之间的多对多关系
- 记录哪些用户参与了哪些课程

#### bingo_course_unit (课程单元表)
- 定义课程的教学单元结构
- 每个课程包含多个教学单元
- 包含单元标题、描述和排序等信息

### 教学资源模块

#### bingo_video_resource (视频资源表)
- 存储课程单元中的视频资源
- 包含视频URL、时长、观看控制等信息
- 关联课程单元表

#### bingo_unit_file (单元文件表)
- 存储课程单元中的文件资源（如PDF、Word文档等）
- 关联课程单元表

### 题库与测验模块

#### bingo_question_category (题库分类表)
- 定义题库分类体系
- 支持多级分类结构

#### bingo_question_bank (题库表)
- 存储测验题目
- 包含题目内容、选项、正确答案等
- 关联题库分类和课程单元

#### bingo_quiz_questions (测验题目关联表)
- 关联测验记录与题目
- 定义测验中题目的顺序和分值

#### bingo_student_quiz_attempt (学生测验参与记录表)
- 记录学生参与测验的情况
- 包含开始时间、完成时间、得分等信息

### 学习进度模块

#### bingo_student_unit_progress (学生单元学习进度表)
- 跟踪学生在各单元的学习进度
- 记录开始学习时间、完成时间和状态

#### bingo_student_video_watching (学生视频观看记录表)
- 详细记录学生视频观看情况
- 包含观看时长、进度百分比、完成状态等

### 反思报告模块

#### bingo_reflection_criteria (反思报告评分标准表)
- 定义评估反思报告的标准
- 包含评分项名称和描述

#### bingo_reflection_grading (反思报告评分详情表)
- 存储对反思报告的具体评分
- 关联反思报告和评分标准

#### bingo_reflection_report (反思报告表)
- 存储学生提交的反思报告内容
- 包含提交状态、评分、教师评语等

#### bingo_reflection_submission_settings (反思报告提交设置表)
- 定义反思报告提交的规则和要求
- 包含截止日期、文件大小限制、相似度检查等设置

### 系统管理模块

#### bingo_system_log (系统操作日志表)
- 记录系统中的各种操作
- 用于审计和问题排查

## 主要业务流程

1. **用户登录流程**：用户通过账号密码登录系统，系统根据用户角色分配相应权限。

2. **课程学习流程**：
   - 学生选择/被分配课程
   - 按单元顺序学习课程内容（视频、文档）
   - 完成单元测验评估
   - 提交课程反思报告
   - 教师评阅反思报告并给予评分

3. **课程管理流程**：
   - 教师/管理员创建课程和课程单元
   - 上传教学资源（视频、文档）
   - 设置测验题目
   - 设置反思报告要求
   - 查看学生学习进度和测验结果
   - 评阅学生反思报告

4. **系统管理流程**：
   - 管理员管理用户账号
   - 分配用户角色和权限
   - 管理课程分类
   - 监控系统日志 