# Bingo LMS数据库实体关系图 (ERD)

以下是Bingo LMS系统的数据库实体关系图，使用PlantUML语法描述。可以通过在线PlantUML工具（如 [PlantUML Online Server](https://www.plantuml.com/plantuml/uml/)）渲染为图形。

```plantuml
@startuml Bingo LMS Database ERD

' 使用下划线和斜体来表示主键和外键
!define primary_key(x) <u>x</u>
!define foreign_key(x) <i>x</i>

' 设置标题
title Bingo LMS 数据库实体关系图

' 表定义和关系
entity "bingo_user" as user {
  * primary_key(id): int
  --
  * account: varchar(50)
  * password: varchar(100)
  * foreign_key(role_id): int
  * first_name: varchar(50)
  * last_name: varchar(50)
  * email: varchar(100)
  * show_email_type: int
  * code: varchar(50)
  * status: int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_role" as role {
  * primary_key(id): int
  --
  * name: varchar(50)
  * code: varchar(50)
  * status: tinyint
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_permission" as permission {
  * primary_key(id): int
  --
  * name: varchar(100)
  * code: varchar(100)
  * module: varchar(50)
  * type: varchar(20)
  foreign_key(parent_id): int
  * sort_order: int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_role_permission" as role_permission {
  * primary_key(id): int
  --
  * foreign_key(role_id): int
  * foreign_key(permission_id): int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_course_category" as course_category {
  * primary_key(id): int
  --
  * name: varchar(100)
  foreign_key(parent_id): int
  * sort_order: int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_course" as course {
  * primary_key(id): int
  --
  * course_code: varchar(50)
  * title_cn: varchar(200)
  * title_en: varchar(200)
  description_cn: text
  description_en: text
  image_url: varchar(500)
  * foreign_key(category_id): int
  * is_template: boolean
  start_date: date
  end_date: date
  report_deadline: date
  * max_students: smallint
  * status: int
  * is_active: boolean
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_course_user" as course_user {
  * primary_key(id): int
  --
  * foreign_key(user_id): int
  * foreign_key(course_id): int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_course_unit" as course_unit {
  * primary_key(id): int
  --
  * foreign_key(course_id): int
  * unit_number: tinyint
  * title_cn: varchar(200)
  * title_en: varchar(200)
  * title: varchar(200)
  description: text
  icon_url: varchar(500)
  * sort_order: int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_video_resource" as video_resource {
  * primary_key(id): int
  --
  * foreign_key(unit_id): int
  * title: varchar(200)
  description: text
  * video_url: varchar(500)
  * duration: int
  * allow_fast_forward: boolean
  * sequence: int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_question_category" as question_category {
  * primary_key(id): int
  --
  * name: varchar(100)
  description: text
  foreign_key(parent_id): int
  * level: tinyint
  * sort_order: int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_question_bank" as question_bank {
  * primary_key(id): int
  --
  * code: varchar(50)
  * foreign_key(category_id): int
  * foreign_key(unit_id): int
  * question_text: text
  * option_a: text
  * option_b: text
  * option_c: text
  * option_d: text
  * correct_option: tinyint
  * question_type: tinyint
  * option_display_type: tinyint
  * score: decimal(5,2)
  feedback: text
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_quiz_questions" as quiz_questions {
  * primary_key(id): int
  --
  * foreign_key(unit_id): int
  * foreign_key(student_id): int
  * foreign_key(question_id): int
  * sequence: int
  * score: decimal(5,2)
  * is_required: tinyint
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  * foreign_key(quiz_record_id): int
  deleted_at: datetime
}

entity "bingo_unit_file" as unit_file {
  * primary_key(id): int
  --
  * foreign_key(unit_id): int
  * title: varchar(200)
  description: text
  * file_url: varchar(500)
  * file_type: varchar(50)
  * is_required: tinyint
  * sequence: int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_student_unit_progress" as student_unit_progress {
  * primary_key(id): int
  --
  * foreign_key(student_id): int
  * foreign_key(course_id): int
  * foreign_key(unit_id): int
  start_time: datetime
  complete_time: datetime
  * status: int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_student_video_watching" as student_video_watching {
  * primary_key(id): int
  --
  * foreign_key(student_id): int
  * foreign_key(video_id): int
  * foreign_key(course_id): int
  start_time: datetime
  * last_position: int
  * watch_duration: int
  * completion_percentage: decimal(5,2)
  * is_completed: tinyint
  completed_time: datetime
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_student_quiz_attempt" as student_quiz_attempt {
  * primary_key(id): int
  --
  * foreign_key(student_id): int
  * foreign_key(course_id): int
  * foreign_key(unit_id): int
  * attempt_number: smallint
  * start_time: datetime
  end_time: datetime
  * score: tinyint
  * total_questions: tinyint
  quiz_content: text
  * status: int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_reflection_criteria" as reflection_criteria {
  * primary_key(id): int
  --
  * criteria_name: varchar(200)
  description: text
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_reflection_grading" as reflection_grading {
  * primary_key(id): int
  --
  * foreign_key(reflection_id): int
  * foreign_key(criteria_id): int
  * is_passed: tinyint
  score_percentage: decimal(5,2)
  comment: text
  * sort_order: int
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_reflection_report" as reflection_report {
  * primary_key(id): int
  --
  * foreign_key(student_id): int
  * foreign_key(course_id): int
  * title: varchar(200)
  * content: text
  file_url: varchar(500)
  upload_time: datetime
  similarity_percentage: decimal(5,2)
  * similarity_status: int
  similarity_report_url: varchar(500)
  * submission_status: int
  submission_time: datetime
  * overall_status: int
  teacher_comment: text
  score: decimal(5,2)
  foreign_key(graded_by): int
  graded_time: datetime
  late_date: datetime
  start_date: datetime
  end_date: datetime
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_reflection_submission_settings" as reflection_submission_settings {
  * primary_key(id): int
  --
  * foreign_key(course_id): int
  * start_date: datetime
  * due_date: datetime
  * late_submission_allowed: tinyint
  late_submission_deadline: datetime
  * max_file_size: int
  * similarity_check_required: tinyint
  * similarity_threshold: decimal(5,2)
  file_name_format: varchar(100)
  word_count_min: int
  word_count_max: int
  instructions: text
  declaration_text: text
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

entity "bingo_system_log" as system_log {
  * primary_key(id): int
  --
  * foreign_key(user_id): int
  * action: varchar(50)
  * target_type: varchar(50)
  foreign_key(target_id): int
  description: text
  ip_address: varchar(50)
  user_agent: varchar(500)
  * created_by: int
  * created_at: datetime
  * updated_at: datetime
  deleted_at: datetime
}

' 定义关系
user "1" -- "1" role : 拥有 >
role "1" -- "n" role_permission : 包含 >
permission "1" -- "n" role_permission : 被赋予 >
permission "1" -- "n" permission : 包含 >

user "1" -- "n" course_user : 参与 >
course "1" -- "n" course_user : 被参与 >
course_category "1" -- "n" course : 分类 >
course_category "1" -- "n" course_category : 包含 >

course "1" -- "n" course_unit : 包含 >
course_unit "1" -- "n" video_resource : 包含 >
course_unit "1" -- "n" unit_file : 包含 >
course_unit "1" -- "n" question_bank : 包含 >

question_category "1" -- "n" question_bank : 分类 >
question_category "1" -- "n" question_category : 包含 >
question_bank "1" -- "n" quiz_questions : 包含 >

user "1" -- "n" student_unit_progress : 记录进度 >
course "1" -- "n" student_unit_progress : 关联 >
course_unit "1" -- "n" student_unit_progress : 跟踪 >

user "1" -- "n" student_video_watching : 观看 >
video_resource "1" -- "n" student_video_watching : 被观看 >
course "1" -- "n" student_video_watching : 关联 >

user "1" -- "n" student_quiz_attempt : 参与 >
course_unit "1" -- "n" student_quiz_attempt : 关联 >
course "1" -- "n" student_quiz_attempt : 关联 >
student_quiz_attempt "1" -- "n" quiz_questions : 包含 >

reflection_criteria "1" -- "n" reflection_grading : 评估标准 >
reflection_report "1" -- "n" reflection_grading : 被评估 >
user "1" -- "n" reflection_report : 提交 >
course "1" -- "n" reflection_report : 关联 >
user "1" -- "n" reflection_report : 评阅 >
course "1" -- "1" reflection_submission_settings : 设置 >

user "1" -- "n" system_log : 产生 >

@enduml
```

## 数据表分组与模块说明

Bingo LMS系统的数据库表可以按功能分为以下几个模块：

### 1. 用户与权限管理模块
- **bingo_user**: 用户基本信息
- **bingo_role**: 用户角色定义
- **bingo_permission**: 权限项定义
- **bingo_role_permission**: 角色-权限映射

### 2. 课程管理模块
- **bingo_course_category**: 课程分类
- **bingo_course**: 课程基本信息
- **bingo_course_user**: 用户-课程关联
- **bingo_course_unit**: 课程单元

### 3. 教学资源模块
- **bingo_video_resource**: 视频资源
- **bingo_unit_file**: 单元文件资源

### 4. 题库与测验模块
- **bingo_question_category**: 题库分类
- **bingo_question_bank**: 题目库
- **bingo_quiz_questions**: 测验题目关联
- **bingo_student_quiz_attempt**: 学生测验尝试记录

### 5. 学习进度跟踪模块
- **bingo_student_unit_progress**: 单元学习进度
- **bingo_student_video_watching**: 视频观看记录

### 6. 反思报告评估模块
- **bingo_reflection_criteria**: 评分标准
- **bingo_reflection_grading**: 评分记录
- **bingo_reflection_report**: 报告内容
- **bingo_reflection_submission_settings**: 提交设置

### 7. 系统管理模块
- **bingo_system_log**: 系统操作日志

## 核心业务流程

### 学生学习流程
1. 学生登录系统，查看所属课程
2. 按顺序学习课程单元
3. 观看视频、阅读材料
4. 完成单元测验
5. 提交课程反思报告
6. 查看成绩和反馈

### 教师教学流程
1. 教师登录系统，管理所教授课程
2. 上传和管理课程资源
3. 查看学生学习进度
4. 评阅学生测验结果
5. 评估学生反思报告
6. 提供反馈和评语

### 管理员管理流程
1. 系统设置和维护
2. 用户账号管理
3. 课程分类管理
4. 权限配置
5. 系统日志监控 