<?php

namespace Modules\Course\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Course\Models\ReflectionReport;
use Modules\Course\Middleware\LocaleMiddleware;
use App\Http\Controllers\Controller;

/**
 * 反思报告API控制器
 */
class ReflectionController extends Controller
{
    public function __construct()
    {
        // 应用多语言中间件
        $this->middleware(LocaleMiddleware::class);
    }

    /**
     * 获取评分标准
     * 
     * @return JsonResponse
     */
    public function getScoringCriteria(): JsonResponse
    {
        try {
            $criteria = ReflectionReport::getFormattedScoringCriteria();
            
            return response()->json([
                'criteria' => $criteria
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => T('Course::validation.SYSTEM_ERROR'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取课程反思报告详情
     * 
     * @param int $courseId 课程ID
     * @param int $studentId 学生ID (可选)
     * @param Request $request
     * @return JsonResponse
     */
    public function getCourseReflection(int $courseId, int $studentId = null, Request $request): JsonResponse
    {
        try {
            // 如果没有指定学生ID，从请求中获取
            if (!$studentId) {
                $studentId = $request->get('student_id');
            }

            // 这里应该添加实际的业务逻辑
            // 目前返回示例数据
            $data = [
                'course_id' => $courseId,
                'student_id' => $studentId,
                'language' => T_locale(),
                'criteria' => ReflectionReport::getFormattedScoringCriteria(),
                'message' => T('Course::validation.RESOURCE_NOT_FOUND')
            ];

            return response()->json([
                'item' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => T('Course::validation.SYSTEM_ERROR'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 提交反思报告评分
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function submitScore(Request $request): JsonResponse
    {
        try {
            // 验证请求数据
            $validated = $request->validate([
                'id' => 'required|integer',
                'teacher_comment' => 'nullable|string',
                'score_result' => 'required|array',
                'score_result.understanding.is_passed' => 'required|integer|in:0,1',
                'score_result.understanding.comment' => 'nullable|string',
                'score_result.cognition.is_passed' => 'required|integer|in:0,1',
                'score_result.cognition.comment' => 'nullable|string',
                'score_result.discussion.is_passed' => 'required|integer|in:0,1',
                'score_result.discussion.comment' => 'nullable|string',
                'score_result.writing.is_passed' => 'required|integer|in:0,1',
                'score_result.writing.comment' => 'nullable|string',
            ], [
                'id.required' => T('Course::validation.score.id.required'),
                'id.integer' => T('Course::validation.score.id.integer'),
                'teacher_comment.string' => T('Course::validation.score.teacher_comment.string'),
                'score_result.understanding.is_passed.required' => T('Course::validation.score.score_result.understanding.is_passed.required'),
                'score_result.understanding.is_passed.integer' => T('Course::validation.score.score_result.understanding.is_passed.integer'),
                'score_result.understanding.is_passed.in' => T('Course::validation.score.score_result.understanding.is_passed.in'),
                'score_result.understanding.comment.string' => T('Course::validation.score.score_result.understanding.comment.string'),
                'score_result.cognition.is_passed.required' => T('Course::validation.score.score_result.cognition.is_passed.required'),
                'score_result.cognition.is_passed.integer' => T('Course::validation.score.score_result.cognition.is_passed.integer'),
                'score_result.cognition.is_passed.in' => T('Course::validation.score.score_result.cognition.is_passed.in'),
                'score_result.cognition.comment.string' => T('Course::validation.score.score_result.cognition.comment.string'),
                'score_result.discussion.is_passed.required' => T('Course::validation.score.score_result.discussion.is_passed.required'),
                'score_result.discussion.is_passed.integer' => T('Course::validation.score.score_result.discussion.is_passed.integer'),
                'score_result.discussion.is_passed.in' => T('Course::validation.score.score_result.discussion.is_passed.in'),
                'score_result.discussion.comment.string' => T('Course::validation.score.score_result.discussion.comment.string'),
                'score_result.writing.is_passed.required' => T('Course::validation.score.score_result.writing.is_passed.required'),
                'score_result.writing.is_passed.integer' => T('Course::validation.score.score_result.writing.is_passed.integer'),
                'score_result.writing.is_passed.in' => T('Course::validation.score.score_result.writing.is_passed.in'),
                'score_result.writing.comment.string' => T('Course::validation.score.score_result.writing.comment.string'),
            ]);

            // 这里应该添加实际的保存逻辑
            // 目前返回成功响应
            return response()->json([
                'message' => 'success',
                'item' => [
                    'id' => $validated['id'],
                    'status' => 'scored',
                    'language' => T_locale()
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'message' => T('Course::validation.INVALID_PARAMS'),
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => T('Course::validation.SYSTEM_ERROR'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取课程学生反思报告
     * 支持路径参数格式: /api/course/reflection/course/{courseId}/student/{studentId}
     * 
     * @param int $courseId
     * @param int $studentId
     * @param Request $request
     * @return JsonResponse
     */
    public function getCourseStudentReflection(int $courseId, int $studentId, Request $request): JsonResponse
    {
        try {
            // 获取当前语言
            $currentLang = T_locale();
            
            // 获取评分标准
            $criteria = ReflectionReport::getFormattedScoringCriteria();
            
            // 示例数据
            $data = [
                'course_id' => $courseId,
                'student_id' => $studentId,
                'current_language' => $currentLang,
                'supported_languages' => [
                    'zh_CN' => '简体中文',
                    'en' => 'English', 
                    'zh_HK' => '繁體中文'
                ],
                'criteria' => $criteria,
                'reflection_data' => [
                    'id' => 123,
                    'title' => T('Course::validation.reflection_report.understanding'),
                    'status' => 'pending',
                    'created_at' => '2024-01-15 10:30:00',
                    'updated_at' => '2024-01-15 15:45:00'
                ]
            ];

            return response()->json([
                'message' => 'success',
                'item' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => T('Course::validation.SYSTEM_ERROR'),
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
