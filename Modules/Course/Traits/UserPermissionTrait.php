<?php

namespace Modules\Course\Traits;

use Bingo\Exceptions\BizException;
use Modules\Course\Enums\ErrorCode;
use Illuminate\Support\Facades\Auth;

/**
 * 用户权限验证 Trait
 * 
 * 提供用户权限验证的通用方法
 */
trait UserPermissionTrait
{
    /**
     * 验证当前用户是否有权限访问指定用户的数据
     * 
     * @param int $targetUserId 目标用户ID
     * @throws BizException 当权限验证失败时抛出异常
     * @return void
     */
    protected function validateUserPermission(int $targetUserId): void
    {
        // 获取当前登录用户ID
        $currentUserId = Auth::user()->id ?? 0;
        
        // 检查目标用户ID是否与当前登录用户ID一致
        if ($targetUserId != $currentUserId) {
            throw new BizException(
                ErrorCode::USER_NO_PERMISSION,
                ErrorCode::USER_NO_PERMISSION->getMessage()
            );
        }
    }
} 