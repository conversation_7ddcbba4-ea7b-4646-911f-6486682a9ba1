<?php

namespace Modules\Course\Api\Controllers;

use App\Http\Controllers\Controller;
use Modules\Course\Services\PermissionService;

class PermissionController extends Controller
{
    protected PermissionService $service;

    public function __construct(PermissionService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取权限树
     *
     * @return array
     */
    public function tree(): array
    {
        return $this->service->tree();
    }

    /**
     * 获取权限信息
     *
     * @param int $id
     * @return array
     * @throws \Exception
     */
    public function info($id)
    {
        $result = $this->service->info($id);
        return $result;
    }
} 