<?php

namespace Modules\Course\Api\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Modules\Course\Services\UnitService;
use Modules\Course\Api\Requests\UnitBatchSaveRequest;
use Bingo\Exceptions\BizException;
use Modules\Course\Enums\ErrorCode;
use Illuminate\Support\Facades\Validator;

class UnitController extends Controller
{
    protected $service;

    public function __construct(UnitService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取单元列表
     *
     * @param Request $request
     * @return array
     */
    public function list(Request $request)
    {
        $params = $request->validate([
            'course_id' => 'required|integer',
            'with_resources' => 'nullable|boolean',
        ],[
            'course_id.required' => T("Course::validation.unit_list.course_id.required"),
            'course_id.integer' => T("Course::validation.unit_list.course_id.integer"),
        ]);
        $result = $this->service->list($params);
        return $result;
    }

    /**
     * 获取单元信息
     *
     * @param int $id
     * @param Request $request
     * @return array
     */
    public function info($id, Request $request)
    {
        $params = $request->validate([
            'with_resources' => 'nullable|boolean',
        ]);

        $withResources = $params['with_resources'] ?? true;
        $result = $this->service->info($id, $withResources);
        return $result;
    }

    /**
     * 批量保存单元
     *
     * @param Request $request
     * @return array
     */
    public function batchSave(UnitBatchSaveRequest $request)
    {
        $params = $request->validated();
        $result = $this->service->batchSave($params);
        return $result;
    }

    /**
     * 获取所有学生单元视频报表
     *
     * @param int $id
     * @return array
     */
    public function studentVideoReport($id, Request $request)
    {
        $params = $request->validate([
            'page' => 'nullable|integer',
            'limit' => 'nullable|integer',
            'first_initial' => 'nullable|string',
            'last_initial' => 'nullable|string',
        ],[
            'page.integer' => T("Course::validation.unit_student_video_report.page.integer"),
            'limit.integer' => T("Course::validation.unit_student_video_report.limit.integer"),
            'first_initial.string' => T("Course::validation.unit_student_video_report.first_initial.string"),
            'last_initial.string' => T("Course::validation.unit_student_video_report.last_initial.string"),
        ]);
        $result = $this->service->studentVideoReport($id, $params);
        return $result;
    }

    /**
     * 更新单元信息
     *
     * @param int $id
     * @param Request $request
     * @return array
     */
    public function save(Request $request)
    {
        $params = $request->validate([
            'id' => 'nullable|integer',
            'course_id' => 'required|integer',
            'title' => 'nullable|string',
            'title_cn' => 'required|string',
            'title_en' => 'required|string',
            'description' => 'required|string',
            'icon_url' => 'required|string',
            'sort_order' => 'nullable|integer',
            'is_completed' => 'nullable|boolean',
        ],[
            'id.integer' => T("Course::validation.unit_save.id.integer"),
            'course_id.required' => T("Course::validation.unit_save.course_id.required"),
            'course_id.integer' => T("Course::validation.unit_save.course_id.integer"),
            'title.string' => T("Course::validation.unit_save.title.string"),
            'title_cn.required' => T("Course::validation.unit_save.title_cn.required"),
            'title_cn.string' => T("Course::validation.unit_save.title_cn.string"),
            'title_en.required' => T("Course::validation.unit_save.title_en.required"),
            'title_en.string' => T("Course::validation.unit_save.title_en.string"),
            'description.required' => T("Course::validation.unit_save.description.required"),
            'description.string' => T("Course::validation.unit_save.description.string"),
            'icon_url.required' => T("Course::validation.unit_save.icon_url.required"),
            'icon_url.string' => T("Course::validation.unit_save.icon_url.string"),
            'sort_order.integer' => T("Course::validation.unit_save.sort_order.integer"),
            'is_completed.boolean' => T("Course::validation.unit_save.is_completed.boolean"),
        ]);
        $result = $this->service->save($params);
        return [
            'success' => $result > 0,
        ];
    }

    /**
     * 更新单元排序
     *
     * @param Request $request
     * @return array
     */
    public function sort(Request $request): array
    {
        $items = $request->json()->all();
        
        if (!is_array($items)) {
            throw new BizException(ErrorCode::INVALID_PARAMS, T("Course::validation.unit_sort.invalid_format"));
        }

        $result = $this->service->sort($items);
        
        return [
            'success' => true,
            'message' => T('Course::message.unit_sort_success')
        ];
    }

    /**
     * 更新视频排序
     *
     * @param Request $request
     * @return array
     */
    public function sortVideo(Request $request): array
    {
        $items = $request->json()->all();
        
        if (!is_array($items)) {
            throw new BizException(ErrorCode::INVALID_PARAMS, T("Course::validation.video_sort.invalid_format"));
        }

        $result = $this->service->sortVideo($items);
        
        return [
            'success' => true,
            'message' => T('Course::message.video_sort_success')
        ];
    }
}