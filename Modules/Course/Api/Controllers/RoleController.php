<?php

namespace Modules\Course\Api\Controllers;

use Illuminate\Http\Request;
use Modules\Course\Models\Role;
use Bingo\Exceptions\BizException;
use App\Http\Controllers\Controller;
use Modules\Course\Services\RoleService;

class RoleController extends Controller
{
    protected RoleService $service;

    public function __construct(RoleService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取角色列表
     * 
     * @param Request $request
     * @return array
     */
    public function list(Request $request): array
    {
        return $this->service->list($request->all());
    }

    /**
     * 获取角色信息
     *
     * @param int $id
     * @return array
     * @throws BizException
     */
    public function info(int $id): array
    {
        return $this->service->info($id);
    }
    
    /**
     * 创建角色
     * 
     * @param Request $request
     * @return array
     */
    public function save(Request $request): array
    {
        // 验证请求参数
        $params = $request->validate([
            'id' => 'nullable|sometimes|integer',
            'name' => 'required|string|max:50',
            'description' => 'nullable|string',
            'status' => 'nullable|integer|in:0,1',
            'permissions' => 'nullable|array',
            'permissions.*' => 'nullable|integer',
        ], [
            'name.required' => T("Course::validation.role_save.name.required"),
            'name.string' => T("Course::validation.role_save.name.string"),
            'name.max' => T("Course::validation.role_save.name.max"),
            'status.integer' => T("Course::validation.role_save.status.integer"),
            'status.in' => T("Course::validation.role_save.status.in"),
            'permissions.array' => T("Course::validation.role_save.permissions.array"),
            'permissions.*.integer' => T("Course::validation.role_save.permissions._integer"),
        ]);

        return $this->service->save($params);
    }
    
    /**
     * 删除角色
     * 
     * @param int $id
     * @return array
     */
    public function delete(int $id): array
    {
        $this->service->delete($id);
        return [];
    }
    
    /**
     * 开关角色状态
     * 
     * @param int $id
     * @return array
     */
    public function toggle(int $id): array
    {
        $this->service->toggle($id);
        return [];
    }

    /**
     * 根据课程id获取角色列表
     * 
     * @param int $id
     * @return array
     */
    public function getRoleListByCourseId(int $id): array
    {
        return $this->service->getRoleListByCourseId($id);
    }

    /**
     * 根据课程id获取班主任信息
     * 
     * @param int $id   
     * @return array
     */
    public function getClassTeacherByCourseId(int $id): array
    {
        return $this->service->getRoleListByCourseId($id, Role::TYPE_CLASS_TEACHER);
    }
    

} 