<?php

namespace Modules\Course\Api\Controllers;

use App\Http\Controllers\Controller;
use Modules\Course\Api\Requests\AddCourseRequest;
use Modules\Course\Api\Requests\UpdateCourseRequest;
use Modules\Course\Services\CourseService;
use Illuminate\Http\Request;
use Bingo\Exceptions\BizException;
use Bingo\Enums\Code;
use Modules\Course\Enums\CourseErrorCode;
use Illuminate\Support\Facades\Auth;

class CourseController extends Controller
{
    /**
     * @var CourseService
     */
    protected $courseService;

    /**
     * 构造函数
     *
     * @param CourseService $courseService
     */
    public function __construct(CourseService $courseService)
    {
        $this->courseService = $courseService;
    }

    /**
     * 添加课程
     *
     * @param AddCourseRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function add(AddCourseRequest $request)
    {
        // 准备数据
        $data = $request->validated();
        $data['creator_id'] = Auth::user()->id ?? 1;

        // 调用服务层处理业务逻辑
        $result = $this->courseService->addCourse($data);

        return  $result;
    }

    /**
     * 获取课程列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request)
    {
        $params = $request->all();
        $result = $this->courseService->getCourses($params);
        return $result;
    }

    /**
     * 获取课程详情
     *
     * @param int $id 课程ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function info($id)
    {
        $result = $this->courseService->getCourseDetail($id);

        return  $result;
    }

    /**
     * 编辑课程
     *
     * @param UpdateCourseRequest $request
     * @param int $id 课程ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateCourseRequest $request, int $id)
    {
        // 准备数据
        $data = $request->validated();

        // 调用服务层处理业务逻辑
        $result = $this->courseService->updateCourse($id, $data);

        return $result;
    }

    /**
     * 删除课程
     *
     * @param int $id 课程ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id = null)
    {
        // 获取请求参数
        $ids = request()->input('ids');

        // 如果是单个ID（通过路由参数传递）
        if ($id) {
            $ids = [$id];
        }

        // 如果是字符串，尝试解析为数组
        if (is_string($ids)) {
            // 尝试解析 JSON 字符串
            $decoded = json_decode($ids, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $ids = $decoded;
            } else {
                // 如果不是 JSON，尝试解析为逗号分隔的字符串
                $ids = explode(',', $ids);
            }
        }

        // 确保 $ids 是数组
        if (!is_array($ids)) {
            $ids = [$ids];
        }

        // 过滤空值并转换为整数
        $ids = array_filter(array_map('intval', $ids));

        // 如果没有有效的ID，抛出异常
        if (empty($ids)) {
            BizException::throws(CourseErrorCode::FAILED, T('Course::base.course.api.delete_no_ids', '请提供有效的课程ID'));
        }

        // 如果只有一个ID，使用单个删除方法
        if (count($ids) === 1) {
            $this->courseService->deleteCourse($ids[0]);

            return ['items'=>['id'=>$ids[0]]];
            //return ['item' => ['id' => $ids[0]]];$ids[0];
        }

        // 否则使用批量删除方法
        $result = $this->courseService->batchDeleteCourses($ids);
        return $result;
    }

    /**
     * 调整课程排序
     *
     * @param Request $request
     * @param int $id 课程ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function adjustOrder(Request $request, int $id)
    {
        // 获取请求参数
        $direction = $request->input('direction');

        // 验证方向参数
        if (!in_array($direction, ['up', 'down'])) {
            BizException::throws(CourseErrorCode::FAILED, T('Course::base.course.api.invalid_direction', '无效的调整方向，必须是 up 或 down'));
        }

        // 调用服务层处理业务逻辑
        $result = $this->courseService->adjustCourseOrder($id, $direction);

        return  $result;
    }

    /**
     * 批量移动课程到指定分类
     *
     * @param Request $request
     * @return array
     */
    public function moveToCategory(Request $request)
    {
        // 验证请求参数
        $validated = $request->validate([
            'course_ids' => 'required|array',
            'course_ids.*' => 'integer|exists:course,id,deleted_at,0',
            'category_id' => 'required|integer|exists:course_category,id,deleted_at,0',
        ], [
            'course_ids.required' => T('Course::base.course.api.move_course_ids_required', '请选择要移动的课程'),
            'course_ids.array' => T('Course::base.course.api.move_course_ids_array', '课程ID必须是数组'),
            'course_ids.*.integer' => T('Course::base.course.api.move_course_ids_integer', '课程ID必须是整数'),
            'course_ids.*.exists' => T('Course::base.course.api.move_course_ids_exists', '选择的课程不存在'),
            'category_id.required' => T('Course::base.course.api.move_category_id_required', '请选择目标分类'),
            'category_id.integer' => T('Course::base.course.api.move_category_id_integer', '分类ID必须是整数'),
            'category_id.exists' => T('Course::base.course.api.move_category_id_exists', '目标分类不存在'),
        ]);

        // 调用服务层处理业务逻辑
        $result = $this->courseService->moveCoursesToCategory($validated['course_ids'], $validated['category_id']);

        return $result;
    }
}
