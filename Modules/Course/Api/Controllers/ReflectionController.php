<?php

namespace Modules\Course\Api\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Modules\Course\Models\ReflectionReport;
use Modules\Course\Services\ReflectionService;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class ReflectionController extends Controller
{
    protected $service;

    public function __construct(ReflectionService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取课程反思报告
     *
     * @param int $id 课程ID
     * @return array
     */
    public function getCourseReflection(int $id): array
    {
        return $this->service->getCourseReflection($id);
    }

    /**
     * 获取课程反思报告评分
     *
     * @param int $id 课程ID
     * @return array
     */
    public function getCourseReflectionList(int $id,Request $request): array
    {
        $params = $request->validate([
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1',
            'status' => 'nullable|integer|min:0',
            'sort_field' => 'nullable|string',
            'sort_order' => 'nullable|string|in:asc,desc',
            'first_initial' => 'nullable|string|min:1',
            'last_initial' => 'nullable|string|min:1',
        ],[
            'page.integer' => T("Course::validation.getCourseReflectionList.page.integer"),
            'page.min' => T("Course::validation.getCourseReflectionList.page.min"),
            'limit.integer' => T("Course::validation.getCourseReflectionList.limit.integer"),
            'limit.min' => T("Course::validation.getCourseReflectionList.limit.min"),
            'status.integer' => T("Course::validation.getCourseReflectionList.status.integer"),
            'status.min' => T("Course::validation.getCourseReflectionList.status.min"),
            'sort_field.string' => T("Course::validation.getCourseReflectionList.sort_field.string"),
            'sort_order.string' => T("Course::validation.getCourseReflectionList.sort_order.string"),
            'sort_order.in' => T("Course::validation.getCourseReflectionList.sort_order.in"),
            'first_initial.string' => T("Course::validation.getCourseReflectionList.first_initial.string"),
            'first_initial.min' => T("Course::validation.getCourseReflectionList.first_initial.min"),
            'last_initial.string' => T("Course::validation.getCourseReflectionList.last_initial.string"),
            'last_initial.min' => T("Course::validation.getCourseReflectionList.last_initial.min"),
        ]);
        return $this->service->getCourseReflectionList($id, $params);
    }

    /**
     * 批量切换反思报告锁定状态
     *
     * @param Request $request
     * @return array
     */
    public function batchToggleLock(Request $request): array
    {
        $params = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'required|integer',
        ],[
            'ids.required' => T("Course::validation.batchToggleLock.ids.required"),
            'ids.array' => T("Course::validation.batchToggleLock.ids.array"),
            'ids.*.required' => T("Course::validation.batchToggleLock.ids.*.required"),
            'ids.*.integer' => T("Course::validation.batchToggleLock.ids.*.integer"),
        ]);
        return $this->service->batchToggleLock($params['ids']);
    }

    /**
     * 批量下载反思报告
     *
     * @param Request $request
     * @return StreamedResponse
     */
    public function batchDownload(Request $request): StreamedResponse
    {
        // 获取GET请求中的ids参数
        $ids = $request->get('ids');
        return $this->service->batchDownload($ids);
    }

    /**
     * 批量退回草稿状态
     *
     * @param Request $request
     * @return array
     */
    public function batchBackDraft(Request $request): array
    {
        $params = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'required|integer',
        ],[
            'ids.required' => T("Course::validation.batchBackDraft.ids.required"),
            'ids.array' => T("Course::validation.batchBackDraft.ids.array"),
            'ids.*.required' => T("Course::validation.batchBackDraft.ids.*.required"),
            'ids.*.integer' => T("Course::validation.batchBackDraft.ids.*.integer"),
        ]);

        return $this->service->batchBackDraft($params['ids']);
    }

    /**
     * 批量延期提交
     *
     * @param Request $request
     * @return array
     */
    public function batchDelaySubmit(Request $request): array
    {
        $params = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'required|integer',
            'delay_days' => 'required|date_format:Y-m-d H:i:s',
        ],[
            'ids.required' => T("Course::validation.batchDelaySubmit.ids.required"),
            'ids.array' => T("Course::validation.batchDelaySubmit.ids.array"),
            'ids.*.required' => T("Course::validation.batchDelaySubmit.ids.*.required"),
            'ids.*.integer' => T("Course::validation.batchDelaySubmit.ids.*.integer"),
            'delay_days.required' => T("Course::validation.batchDelaySubmit.delay_days.required"),
            'delay_days.date_format' => T("Course::validation.batchDelaySubmit.delay_days.date_format"),
        ]);
        return $this->service->batchDelaySubmit($params['ids'], $params['delay_days']);
    }

    /**
     * 获取反思报告详情
     *
     * @param Request $request
     * @return array
     */
    public function reportDetail(Request $request): array
    {
        // 获取所有请求参数
        $params = $request->all();

        // 调用服务层处理业务逻辑
        return $this->service->getReportDetail($params);
    }

    /**
     * 获取评分标准
     *
     * @return array
     */
    public function getScoringCriteria(): array
    {
        return [
            'criteria' => ReflectionReport::getFormattedScoringCriteria()
        ];
    }

    /**
     * 进行评分
     *
     * @param Request $request
     * @return array
     */
    public function sendReport(Request $request): array
    {
        // 验证请求参数
        $request->validate([
            'courseId' => 'required|numeric',
            'assignmentName' => 'required|string',
            'submissionId' => 'required|numeric',
            'cmid' => 'required|numeric',
            'FileName' => 'required|string',
            'FileCheckSum' => 'required|string',
            'studentId' => 'required|numeric',
            'FilePath' => 'required|file'
        ], [
            'courseId.required' => T('Course::validation.reflection_send_report.courseId.required'),
            'courseId.numeric' => T('Course::validation.reflection_send_report.courseId.numeric'),
            'assignmentName.required' => T('Course::validation.reflection_send_report.assignmentName.required'),
            'assignmentName.string' => T('Course::validation.reflection_send_report.assignmentName.string'),
            'submissionId.required' => T('Course::validation.reflection_send_report.submissionId.required'),
            'submissionId.numeric' => T('Course::validation.reflection_send_report.submissionId.numeric'),
            'cmid.required' => T('Course::validation.reflection_send_report.cmid.required'),
            'cmid.numeric' => T('Course::validation.reflection_send_report.cmid.numeric'),
            'FileName.required' => T('Course::validation.reflection_send_report.FileName.required'),
            'FileName.string' => T('Course::validation.reflection_send_report.FileName.string'),
            'FileCheckSum.required' => T('Course::validation.reflection_send_report.FileCheckSum.required'),
            'FileCheckSum.string' => T('Course::validation.reflection_send_report.FileCheckSum.string'),
            'studentId.required' => T('Course::validation.reflection_send_report.studentId.required'),
            'studentId.numeric' => T('Course::validation.reflection_send_report.studentId.numeric'),
            'FilePath.required' => T('Course::validation.reflection_send_report.FilePath.required'),
            'FilePath.file' => T('Course::validation.reflection_send_report.FilePath.file')
        ]);

        // 获取所有请求参数
        $params = $request->except('FilePath');

        // 获取上传的文件
        $file = $request->file('FilePath');

        // 调用服务层处理业务逻辑
        return $this->service->sendReport($params, $file);
    }

    /**
     * 进行评分
     *
     * @param Request $request
     * @return array
     */
    public function score(Request $request): array
    {
        $params = $request->validate([
            'id' => 'required|integer',
            'teacher_comment' => 'nullable|string',
            'score_result' => 'required|array',
            'score_result.understanding' => 'required|array',
            'score_result.understanding.is_passed' => 'required|integer|in:0,1',
            'score_result.understanding.comment' => 'nullable|string',
            'score_result.cognition' => 'required|array',
            'score_result.cognition.is_passed' => 'required|integer|in:0,1',
            'score_result.cognition.comment' => 'nullable|string',
            'score_result.discussion' => 'required|array',
            'score_result.discussion.is_passed' => 'required|integer|in:0,1',
            'score_result.discussion.comment' => 'nullable|string',
            'score_result.writing' => 'required|array',
            'score_result.writing.is_passed' => 'required|integer|in:0,1',
            'score_result.writing.comment' => 'nullable|string',
        ],[
            'id.required' => T("Course::validation.score.id.required"),
            'id.integer' => T("Course::validation.score.id.integer"),
            'teacher_comment.string' => T("Course::validation.score.teacher_comment.string"),
            'score_result.understanding.is_passed.required' => T("Course::validation.score.score_result.understanding.is_passed.required"),
            'score_result.understanding.is_passed.integer' => T("Course::validation.score.score_result.understanding.is_passed.integer"),
            'score_result.understanding.is_passed.in' => T("Course::validation.score.score_result.understanding.is_passed.in"),
            'score_result.understanding.comment.string' => T("Course::validation.score.score_result.understanding.comment.string"),
            'score_result.cognition.is_passed.required' => T("Course::validation.score.score_result.cognition.is_passed.required"),
            'score_result.cognition.is_passed.integer' => T("Course::validation.score.score_result.cognition.is_passed.integer"),
            'score_result.cognition.is_passed.in' => T("Course::validation.score.score_result.cognition.is_passed.in"),
            'score_result.cognition.comment.string' => T("Course::validation.score.score_result.cognition.comment.string"),
            'score_result.discussion.is_passed.required' => T("Course::validation.score.score_result.discussion.is_passed.required"),
            'score_result.discussion.is_passed.integer' => T("Course::validation.score.score_result.discussion.is_passed.integer"),
            'score_result.discussion.is_passed.in' => T("Course::validation.score.score_result.discussion.is_passed.in"),
            'score_result.discussion.comment.string' => T("Course::validation.score.score_result.discussion.comment.string"),
            'score_result.writing.is_passed.required' => T("Course::validation.score.score_result.writing.is_passed.required"),
            'score_result.writing.is_passed.integer' => T("Course::validation.score.score_result.writing.is_passed.integer"),
            'score_result.writing.is_passed.in' => T("Course::validation.score.score_result.writing.is_passed.in"),
            'score_result.writing.comment.string' => T("Course::validation.score.score_result.writing.comment.string"),
        ]);
        return $this->service->score($params);
    }

    /**
     * 添加反思报告提交记录
     *
     * @param Request $request
     * @return array
     */
    public function add(Request $request): array
    {
        // 验证请求参数
        $params = $request->validate([
            'reflection_id' => 'required|integer|exists:reflection_report,id,deleted_at,0',
            'file_url' => 'required|string|max:500',
        ],[
            'reflection_id.required' => T("Course::validation.reflection_add.reflection_id.required"),
            'reflection_id.integer' => T("Course::validation.reflection_add.reflection_id.integer"),
            'reflection_id.exists' => T("Course::validation.reflection_add.reflection_id.exists"),
            'file_url.required' => T("Course::validation.reflection_add.file_url.required"),
            'file_url.string' => T("Course::validation.reflection_add.file_url.string"),
            'file_url.max' => T("Course::validation.reflection_add.file_url.max"),
        ]);

        // 调用服务层处理业务逻辑
        return $this->service->addReflection($params);
    }

    /**
     * 获取学生反思报告
     *
     * @param int $courseId 课程ID
     * @param int $studentId 学生ID
     * @return array
     */
    public function getStudentReflection(int $courseId, int $studentId): array
    {
        return $this->service->getStudentReflection($courseId, $studentId);
    }
}