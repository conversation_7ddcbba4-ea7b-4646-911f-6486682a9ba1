<?php

namespace Modules\Course\Api\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Course\Services\CourseUserService;

class CourseUserController extends Controller
{
    /**
     * 课程用户服务
     *
     * @var CourseUserService
     */
    protected $courseUserService;

    /**
     * 构造函数
     *
     * @param CourseUserService $courseUserService
     */
    public function __construct(CourseUserService $courseUserService)
    {
        $this->courseUserService = $courseUserService;
    }

    /**
     * 获取当前用户参与的课程列表
     *
     * @param Request $request
     * @return array
     */
    public function getUserCourseList(Request $request)
    {
        // 获取请求参数
        $params = $request->all();
        
        // 调用服务层处理业务逻辑
        $result = $this->courseUserService->getUserCourseList($params);
        
        return $result;
    }
}
