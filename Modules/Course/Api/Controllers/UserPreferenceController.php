<?php

namespace Modules\Course\Api\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Course\Services\UserPreferenceService;

class UserPreferenceController extends Controller
{
    protected $service;

    public function __construct(UserPreferenceService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取用户偏好
     * 
     * @param Request $request
     * @return array
     */
    public function get(Request $request): array
    {
        $params = $request->validate([
            'user_id' => 'required|integer',
            'key' => 'required|string',
        ], [
            'user_id.required' => T("Course::validation.user_preference_get.user_id.required"),
            'user_id.integer' => T("Course::validation.user_preference_get.user_id.integer"),
            'key.required' => T("Course::validation.user_preference_get.key.required"),
            'key.string' => T("Course::validation.user_preference_get.key.string"),
        ]);
        return $this->service->get($params);
    }

    /**
     * 更新用户偏好
     * 
     * @param Request $request
     * @return array
     */
    public function update(Request $request): array
    {
        $params = $request->validate([
            'user_id' => 'required|integer',
            'key' => 'required|string',
            'value' => 'required',
        ], [
            'user_id.required' => T("Course::validation.user_preference_update.user_id.required"),
            'user_id.integer' => T("Course::validation.user_preference_update.user_id.integer"),
            'key.required' => T("Course::validation.user_preference_update.key.required"),
            'key.string' => T("Course::validation.user_preference_update.key.string"),
            'value.required' => T("Course::validation.user_preference_update.value.required"),
        ]);
        return $this->service->update($params);
    }

    /**
     * 获取用户偏好列表
     * 
     * @param Request $request
     * @return array
     */
    public function list(Request $request): array
    {
        $params = $request->validate([
            'user_id' => 'required|integer',
        ], [
            'user_id.required' => T("Course::validation.user_preference_list.user_id.required"),
            'user_id.integer' => T("Course::validation.user_preference_list.user_id.integer"),
        ]);
        $result =  $this->service->list($params);
        // 将结果转换为key-value格式
        $formattedResult = [];
        foreach ($result as $item) {
            $formattedResult[$item['key']] = $item;
        }
        return $formattedResult;
    }
}
