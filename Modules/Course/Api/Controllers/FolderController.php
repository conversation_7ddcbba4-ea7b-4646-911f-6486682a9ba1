<?php

namespace Modules\Course\Api\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\Course\Services\FolderService;
use Modules\Course\Api\Requests\FolderSaveRequest;
use Modules\Course\Api\Requests\FolderMoveRequest;

/**
 * 文件夹资源控制器
 */
class FolderController extends Controller
{
    protected $service;

    /**
     * 构造函数
     * 
     * @param FolderService $service
     */
    public function __construct(FolderService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取单元的文件夹树结构
     * 
     * @param Request $request
     * @param int $unitId 单元ID
     * @return array
     */
    public function tree(Request $request)
    {
        $params = $request->validate([
            'root_directory_id' => 'required|integer',
        ],[
            'root_directory_id.required' => T("Course::validation.folder_tree.root_directory_id.required"),
            'root_directory_id.integer' => T("Course::validation.folder_tree.root_directory_id.integer"),
        ]);
        return $this->service->getFolderTree($params['root_directory_id']);
    }

    /**
     * 保存文件夹资源
     * 
     * @param FolderSaveRequest $request
     * @return array
     */
    public function save(FolderSaveRequest $request)
    {
        return $this->service->save($request->validated());
    }

    /**
     * 下载文件夹资源
     * 
     * @param int $id 文件夹资源ID
     * @return array
     */
    public function download(int $id)
    {
        return $this->service->download($id);
    }

    /**
     * 获取所有根目录资源
     * 
     * @param Request $request
     * @return array
     */
    public function rootList(Request $request)
    {
        $params = $request->validate([
            'page' => 'nullable|integer',
            'limit' => 'nullable|integer',
            'unit_id' => 'nullable|integer',
        ],[
            'page.integer' => T("Course::validation.folder_root.page.integer"),
            'limit.integer' => T("Course::validation.folder_root.limit.integer"),
            'unit_id.integer' => T("Course::validation.folder_root.unit_id.integer"),
        ]);
        return $this->service->rootList($params);
    }
    
} 