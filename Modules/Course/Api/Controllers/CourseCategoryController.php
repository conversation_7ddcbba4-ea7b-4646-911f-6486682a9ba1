<?php

namespace Modules\Course\Api\Controllers;

use App\Http\Controllers\Controller;
use Modules\Course\Api\Requests\AddCourseCategoryRequest;
use Modules\Course\Api\Requests\UpdateCourseCategoryRequest;
use Modules\Course\Services\CourseCategoryService;
use Bingo\Exceptions\BizException;
use Bingo\Enums\Code;

class CourseCategoryController extends Controller
{
    /**
     * @var CourseCategoryService
     */
    protected $categoryService;

    /**
     * 构造函数
     *
     * @param CourseCategoryService $categoryService
     */
    public function __construct(CourseCategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    /**
     * 添加课程分类
     *
     * @param AddCourseCategoryRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function add(AddCourseCategoryRequest $request)
    {
        // 准备数据
        $data = $request->validated();
        $data['creator_id'] = $request->user() ? $request->user()->id : 1;

        // 调用服务层处理业务逻辑
        $result = $this->categoryService->addCategory($data);

        return  $result;
    }

    /**
     * 获取课程分类列表
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getList(\Illuminate\Http\Request $request)
    {
        $params = $request->all();
        $result = $this->categoryService->getCategories($params);
        return  $result;
    }

    /**
     * 获取课程分类详情
     *
     * @param int $id 分类ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function info($id)
    {
        $result = $this->categoryService->getCategoryDetail($id);

        return  $result;
    }

    /**
     * 编辑课程分类
     *
     * @param UpdateCourseCategoryRequest $request
     * @param int $id 分类ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateCourseCategoryRequest $request, int $id)
    {
        // 准备数据
        $data = $request->validated();

        // 调用服务层处理业务逻辑
        $result = $this->categoryService->updateCategory($id, $data);

        return $result;
    }

    /**
     * 删除课程分类
     *
     * @param int $id 分类ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id = null)
    {
        // 获取请求参数
        $ids = request()->input('ids');

        // 如果是单个ID（通过路由参数传递）
        if ($id) {
            $ids = [$id];
        }

        // 如果是字符串，尝试解析为数组
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }

        // 确保 $ids 是数组
        if (!is_array($ids)) {
            $ids = [$ids];
        }

        // 过滤空值并转换为整数
        $ids = array_filter(array_map('intval', $ids));

        // 如果没有有效的ID，抛出异常
        if (empty($ids)) {
            BizException::throws(Code::FAILED, T('Course::base.course_category.api.delete_no_ids', '请提供有效的分类ID'));
        }

        // 如果只有一个ID，使用单个删除方法
        if (count($ids) === 1) {
            $this->categoryService->deleteCategory($ids[0]);

            return ['items'=>['id'=>$ids[0]]];
        }

        // 否则使用批量删除方法
        $result = $this->categoryService->batchDeleteCategories($ids);

        return  $result;
    }

    /**
     * 将多个分类转移到目标分类下
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function moveToTarget(\Illuminate\Http\Request $request)
    {
        // 获取请求参数
        $sourceIds = $request->input('source_ids');
        $targetId = $request->input('target_id');

        // 验证目标分类ID
        if (empty($targetId) || !is_numeric($targetId)) {
            BizException::throws(Code::FAILED, T('Course::base.course_category.api.invalid_target_id', '请提供有效的目标分类ID'));
        }

        // 如果是字符串，尝试解析为数组
        if (is_string($sourceIds)) {
            // 尝试解析 JSON 字符串
            $decoded = json_decode($sourceIds, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $sourceIds = $decoded;
            } else {
                // 如果不是 JSON，尝试解析为逗号分隔的字符串
                $sourceIds = explode(',', $sourceIds);
            }
        }

        // 确保 $sourceIds 是数组
        if (!is_array($sourceIds)) {
            $sourceIds = [$sourceIds];
        }

        // 过滤空值并转换为整数
        $sourceIds = array_filter(array_map('intval', $sourceIds));

        // 如果没有有效的源分类ID，抛出异常
        if (empty($sourceIds)) {
            BizException::throws(Code::FAILED, T('Course::base.course_category.api.no_source_ids', '请提供有效的源分类ID'));
        }

        // 调用服务层处理业务逻辑
        $result = $this->categoryService->moveCategoriesToTarget($sourceIds, (int)$targetId);

        return  $result;
    }

    /**
     * 调整课程分类排序
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id 分类ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function adjustOrder(\Illuminate\Http\Request $request, int $id)
    {
        // 获取请求参数
        $direction = $request->input('direction');

        // 验证方向参数
        if (!in_array($direction, ['up', 'down'])) {
            BizException::throws(CourseErrorCode::FAILED, T('Course::base.course_category.api.invalid_direction', '无效的调整方向，必须是 up 或 down'));
        }

        // 调用服务层处理业务逻辑
        $result = $this->categoryService->adjustCategoryOrder($id, $direction);

        return $result;
    }
}
