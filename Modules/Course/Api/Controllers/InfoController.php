<?php

namespace Modules\Course\Api\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Course\Services\InfoService;
use Modules\Course\Models\Info;

class InfoController extends Controller
{
    /**
     * 信息服务
     *
     * @var InfoService
     */
    protected $infoService;

    /**
     * 构造函数
     *
     * @param InfoService $infoService
     */
    public function __construct(InfoService $infoService)
    {
        $this->infoService = $infoService;
    }

    /**
     * 创建网站信息
     *
     * @param Request $request
     * @return array
     */
    public function createInfo(Request $request)
    {
        // 获取请求参数
        $data = $request->all();

        // 调用服务层处理业务逻辑
        $result = $this->infoService->createInfo($data);

        return [
            'items' => $result
        ];
    }

    /**
     * 获取网站信息列表
     *
     * @param Request $request
     * @return array
     */
    public function getInfoList(Request $request)
    {
        // 获取请求参数
        $params = $request->all();

        // 调用服务层处理业务逻辑
        $result = $this->infoService->getInfoList($params);

        return $result;
    }

    /**
     * 获取网站信息详情
     *
     * @param int $id
     * @return array
     */
    public function getInfoDetail(int $id)
    {
        // 调用服务层处理业务逻辑
        $result = $this->infoService->getInfoDetail($id);

        return [
            'items' => $result
        ];
    }

    /**
     * 获取FAQ列表
     *
     * @param Request $request
     * @return array
     */
    public function getFaqList(Request $request)
    {
        // 设置类型为FAQ
        $params = $request->all();
        $params['type'] = Info::TYPE_FAQ;

        // 调用服务层处理业务逻辑
        $result = $this->infoService->getInfoList($params);

        return $result;
    }

    /**
     * 获取公告列表
     *
     * @param Request $request
     * @return array
     */
    public function getAnnouncementList(Request $request)
    {
        // 设置类型为公告
        $params = $request->all();
        $params['type'] = Info::TYPE_ANNOUNCEMENT;

        // 调用服务层处理业务逻辑
        $result = $this->infoService->getInfoList($params);

        return $result;
    }

    /**
     * 获取联系我们列表
     *
     * @param Request $request
     * @return array
     */
    public function getContactList(Request $request)
    {
        // 设置类型为联系我们
        $params = $request->all();
        $params['type'] = Info::TYPE_CONTACT;

        // 调用服务层处理业务逻辑
        $result = $this->infoService->getInfoList($params);

        return $result;
    }

    /**
     * 获取学生学习详情
     *
     * 当提供student_id时，查询单个学生的学习情况
     * 当不提供student_id时，查询所有选课学生的学习情况
     *
     * @param Request $request
     * @return array
     */
    public function studyDetail(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'course_id' => 'required|integer',
            'student_id' => 'integer|nullable',
        ]);

        // 获取请求参数
        $params = $request->all();

        // 调用服务层处理业务逻辑
        $result = $this->infoService->getStudyDetail($params);

        return [
            'items' => $result
        ];
    }

    /**
     * 获取所有学生学习详情（支持按首字母筛选）
     *
     * 当只有course_id时，查询当前课程下的所有报名的学生学习信息
     * 当有course_id、additional_first_initial、additional_last_initial时，
     * 先通过首字母筛选学生，再查询这些学生在课程中的学习情况
     *
     * @param Request $request
     * @return array
     */
    public function allStudyDetail(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'course_id' => 'required|integer',
            'additional_first_initial' => 'string|nullable',
            'additional_last_initial' => 'string|nullable',
        ]);

        // 获取请求参数
        $params = $request->all();

        // 调用服务层处理业务逻辑
        $result = $this->infoService->getAllStudyDetail($params);

        return [
            'items' => $result
        ];
    }

}
