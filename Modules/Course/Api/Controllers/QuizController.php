<?php

namespace Modules\Course\Api\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Course\Services\QuizService;
use Bingo\Exceptions\BizException;
use Modules\Course\Enums\CourseErrorCode;
use Illuminate\Support\Facades\DB;

class QuizController extends Controller
{
    /**
     * @var QuizService
     */
    protected $quizService;

    /**
     * 构造函数
     *
     * @param QuizService $quizService
     */
    public function __construct(QuizService $quizService)
    {
        $this->quizService = $quizService;
    }

    /**
     * 获取题库并生成试卷
     *
     * @param int $id 题库ID
     * @return array
     */
    public function getQuiz($id)
    {
        // 获取当前用户
        $studentId = Auth::user()->id;

        // 调用服务层处理业务逻辑
        $result = $this->quizService->generateQuiz($id, $studentId);

        return ['items'=>$result];
    }

    /**
     * 提交或保存答卷
     *
     * @param Request $request
     * @param int $id 答卷ID
     * @return array
     */
    public function submitAnswer(Request $request, int $id)
    {
        // 获取当前用户
        $studentId = Auth::user()->id;

        // 验证提交的答案数据
        $data = $request->all();

        // 验证答案数据结构
        if (!isset($data['answers']) || !is_array($data['answers'])) {
            BizException::throws(CourseErrorCode::VALIDATION_FAILED, T('Course::error.answer_format_invalid', '答卷格式无效，必须包含answers数组'));
        }

        // 获取操作类型，默认为提交
        $action = isset($data['action']) ? $data['action'] : 'submit';
        // 如果是保存操作，状态为1(进行中)，如果是提交操作，状态为2(已完成)
        $status = ($action === 'save') ? 1 : 2;

        // 准备用户答案 - 确保每个答案不包含action属性
        $cleanAnswers = [];
        foreach ($data['answers'] as $answer) {
            // 如果answer中有action字段，移除它
            if (is_array($answer) && isset($answer['action'])) {
                unset($answer['action']);
            }
            $cleanAnswers[] = $answer;
        }

        // 调用服务层处理业务逻辑
        $result = $this->quizService->submitQuizAnswer($id, $studentId, $cleanAnswers, $status);

        return ['items'=>$result];
    }

    /**
     * 获取当前学生在特定题库下的作答记录列表
     *
     * @param int $id 题库ID
     * @return array
     */
    public function userAnswerList(int $id)
    {
        // 获取当前用户ID
        $studentId = Auth::user()->id;
        // 查询该学生在该题库下的所有答卷记录
        $records = DB::table('questions_answer')
            ->where('quiz_id', $id)
            ->where('student_id', $studentId)
            ->orderBy('id', 'asc') // 按ID正序排列
            ->get();

        $result = [];

        foreach ($records as $record) {
            // 处理每条记录，解析JSON字段
            $item = (array)$record;

            // 排除不需要的字段
            unset($item['created_at']);
            unset($item['updated_at']);
            unset($item['deleted_at']);

            // 转换状态为更具描述性的文本
            $statusText = '未知';
            switch ($record->status) {
                case 1:
                    $statusText = '进行中';
                    break;
                case 2:
                    $statusText = '已完成';
                    break;
            }
            $item['status_text'] = $statusText;

            // 添加到结果数组
            $result[] = $item;
        }

        return [
            'total' => count($result),
            'items' => $result
        ];
    }

    /**
     * 获取答卷明细
     *
     * @param int $id 答卷ID
     * @return array
     */
    public function getAnswerDetail(int $id)
    {
        // 获取当前用户ID
        $studentId = Auth::user()->id;

        // 调用服务层处理业务逻辑
        $result = $this->quizService->getAnswerDetail($id, $studentId);

        return [
            'items' => $result
        ];
    }

    /**
     * 获取测验明细
     *
     * @param int $id 测验ID
     * @return array
     */
    public function getQuizDetail(int $id)
    {
        // 调用服务层处理业务逻辑
        $result = $this->quizService->getQuizDetail($id);

        return [
            'items' => $result
        ];
    }

    /**
     * 搜索答卷列表
     *
     * @param Request $request
     * @param int $id 测验ID
     * @return array
     */
    public function searchAnswerList(Request $request, int $id)
    {
        // 获取搜索参数
        $params = $request->all();

        // 调用服务层处理业务逻辑
        $result = $this->quizService->searchAnswerList($id, $params);

        return $result;
    }

    /**
     * 导出答卷列表为CSV
     *
     * @param Request $request
     * @param int $id 测验ID
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function exportAnswerListToCsv(Request $request, int $id)
    {
        // 获取搜索参数
        $params = $request->all();

        // 调用服务层处理业务逻辑
        $csvContent = $this->quizService->exportAnswerListToCsv($id, $params);

        // 获取测验信息，用于文件名
        $quiz = DB::table('quiz')
            ->where('id', $id)
            ->where('deleted_at', 0)
            ->first();

        $fileName = '答卷列表_';
        if ($quiz) {
            $fileName .= $quiz->title . '_';
        }
        $fileName .= date('YmdHis') . '.csv';

        // 设置响应头
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
        ];

        // 返回CSV文件
        return response($csvContent, 200, $headers);
    }


    /**
     * 获取单个题目详情
     *
     * @param Request $request
     * @return array
     */
    public function getSingleDetail(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'id' => 'required|integer',
            'question_id' => 'required|integer'
        ]);

        $answerId = $request->input('id');
        $questionId = $request->input('question_id');

        // 调用服务层处理业务逻辑
        $result = $this->quizService->getSingleQuestionDetail($answerId, $questionId);

        return [
            'items' => $result
        ];
    }

    /**
     * 修改题目判分
     *
     * @param Request $request
     * @return array
     */
    public function editScore(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'id' => 'required|integer',
            'question_id' => 'required|integer',
            'score' => 'nullable|numeric|min:0',
            'comment' => 'nullable|string|max:500'
        ]);

        $answerId = $request->input('id');
        $questionId = $request->input('question_id');
        $score = $request->has('score') ? floatval($request->input('score')) : null;
        $comment = $request->has('comment') ? $request->input('comment') : null;

        // 调用服务层处理业务逻辑
        $result = $this->quizService->editQuestionScore($answerId, $questionId, $score, $comment);

        return [
            'items' => $result
        ];
    }

}