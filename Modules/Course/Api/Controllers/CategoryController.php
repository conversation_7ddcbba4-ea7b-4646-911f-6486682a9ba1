<?php

namespace Modules\Course\Api\Controllers;

use App\Http\Controllers\Controller;
use Bingo\Exceptions\BizException;
use Modules\Course\Api\Requests\AddCategoryRequest;
use Modules\Course\Api\Requests\UpdateCategoryRequest;
use Modules\Course\Enums\CourseErrorCode;
use Modules\Course\Services\CategoryService;

class CategoryController extends Controller
{
    /**
     * @var CategoryService
     */
    protected $categoryService;

    /**
     * 构造函数
     *
     * @param CategoryService $categoryService
     */
    public function __construct(CategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    /**
     * 添加题库分类
     *
     * @param AddCategoryRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function add(AddCategoryRequest $request)
    {
        // 准备数据
        $data = $request->validated();
        $data['creator_id'] = $request->user() ? $request->user()->id : 1;

        // 调用服务层处理业务逻辑
        $result = $this->categoryService->addCategory($data);
        return $result;
    }

    /**
     * 获取题库分类列表
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getList(\Illuminate\Http\Request $request)
    {
        $params = $request->all();
        $result = $this->categoryService->getCategories($params);

        // 直接返回服务层的结果
        return $result;
    }

    /**
     * 通过父级分类名称获取所有子类（支持模糊查询）
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getChildrenByParentName(\Illuminate\Http\Request $request)
    {
        $parentName = $request->input('name', '');

        if (empty($parentName)) {
            BizException::throws(CourseErrorCode::FAILED, '分类名称不能为空');
        }

        $result = $this->categoryService->getCategoriesByParentName($parentName);
        return  $result;
    }

    /**
     * 编辑题库分类
     *
     * @param UpdateCategoryRequest $request
     * @param int $id 分类ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateCategoryRequest $request, int $id)
    {
        // 准备数据
        $data = $request->validated();
        // 调用服务层处理业务逻辑
        $result = $this->categoryService->updateCategory($id, $data);
        return $result;
    }

    /**
     * 删除题库分类（软删除）
     *
     * @param int $id 分类ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(int $id)
    {
        // 调用服务层处理业务逻辑
        $this->categoryService->deleteCategory($id);

        return ['item' => ['id' => $id]];
    }

    /**
     * 批量删除题库分类（软删除）
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function del(\Illuminate\Http\Request $request)
    {
        // 获取请求参数
        $ids = $request->input('ids');

        // 如果是字符串，尝试解析为数组
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }

        // 确保 $ids 是数组
        if (!is_array($ids)) {
            $ids = [$ids];
        }

        // 过滤空值并转换为整数
        $ids = array_filter(array_map('intval', $ids));

        // 如果没有有效的ID，抛出异常
        if (empty($ids)) {
            BizException::throws(CourseErrorCode::FAILED, '请提供有效的分类ID');
        }

        // 调用服务层处理业务逻辑
        $result = $this->categoryService->batchDeleteCategories($ids);

        return $result;
    }
}
