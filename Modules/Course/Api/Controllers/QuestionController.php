<?php

namespace Modules\Course\Api\Controllers;

use App\Http\Controllers\Controller;
use Modules\Course\Api\Requests\AddQuestionRequest;
use Modules\Course\Api\Requests\UpdateQuestionRequest;
use Modules\Course\Services\QuestionService;
use Modules\Course\Enums\CourseErrorCode;
use Bingo\Exceptions\BizException;

class Question<PERSON>ontroller extends Controller
{
    /**
     * @var QuestionService
     */
    protected $questionService;

    /**
     * 构造函数
     *
     * @param QuestionService $questionService
     */
    public function __construct(QuestionService $questionService)
    {
        $this->questionService = $questionService;
    }

    /**
     * 添加题目
     *
     * @param AddQuestionRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function add(AddQuestionRequest $request)
    {
        // 准备数据
        $data = $request->validated();
        $data['creator_id'] = $request->user() ? $request->user()->id : 1;

        // 调用服务层处理业务逻辑
        $result = $this->questionService->addQuestion($data);

        return  $result;
    }

    /**
     * 更新题目
     *
     * @param UpdateQuestionRequest $request
     * @param int $id 题目ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateQuestionRequest $request, $id)
    {
        // 准备数据
        $data = $request->validated();

        // 调用服务层处理业务逻辑
        $result = $this->questionService->updateQuestion($id, $data);

        return $result  ;
    }

    /**
     * 获取题目详情
     *
     * @param int $id 题目ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function info($id)
    {
        // 调用服务层处理业务逻辑
        $result = $this->questionService->getQuestionDetail($id);

        return $result;
    }

    /**
     * 删除题目
     *
     * @param int|null $id 题目ID（可选，通过路由参数传递）
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id = null)
    {
        // 获取请求参数
        $ids = request()->input('ids');

        // 如果是单个ID（通过路由参数传递）
        if ($id) {
            $ids = [$id];
        }

        // 如果是字符串，尝试解析为数组
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }

        // 确保 $ids 是数组
        if (!is_array($ids)) {
            $ids = [$ids];
        }

        // 过滤空值并转换为整数
        $ids = array_filter(array_map('intval', $ids));

        // 如果没有有效的ID，抛出异常
        if (empty($ids)) {
            BizException::throws(CourseErrorCode::FAILED, T('Course::base.question.api.delete_no_ids', '请提供有效的题目ID'));
        }

        // 调用服务层处理业务逻辑
        $result = $this->questionService->batchDeleteQuestions($ids);

        return $result;
    }

    /**
     * 获取题目列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function list()
    {
        // 获取查询参数
        $params = request()->all();

        // 调用服务层处理业务逻辑
        $result = $this->questionService->getQuestions($params);

        // 直接返回服务层的结果
        return $result;
    }
}
