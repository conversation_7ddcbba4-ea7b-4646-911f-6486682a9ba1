<?php

namespace Modules\Course\Api\Controllers;

use Illuminate\Http\Request;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\Log;
use Modules\Course\Enums\ErrorCode;
use App\Http\Controllers\Controller;
use Modules\Course\Services\ResourceService;
use Modules\Course\Domain\Repositories\ResourceRepository;

/**
 * 资源控制器
 * 
 * 处理课程资源的上传、下载、管理功能
 */
class ResourceController extends Controller
{
    /**
     * 资源服务
     * 
     * @var ResourceService
     */
    protected $service;

    /**
     * 构造函数
     * 
     * @param ResourceService $service
     */
    public function __construct(ResourceService $service)
    {
        $this->service = $service;
    }

    /**
     * 上传资源文件
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function upload(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'file' => 'required|max:10240',
            'type' => 'nullable|in:file,video,image,document,audio',
            'filename' => 'nullable|string|max:255',
            'disk' => 'nullable|in:public,local',
        ],[
            'file.required' => T("Course::validation.resource_upload.file.required"),
            'file.file' => T("Course::validation.resource_upload.file.file"),
            'file.max' => T("Course::validation.resource_upload.file.max",['size' => '10240']),
        ]);

        $result = $this->service->upload($request->all(), $request->file('file'));
        return $result;
    }

    /**
     * 获取视频资源信息
     * 
     * @param int $id   
     * @return array
     */
    public function videoInfo(int $id, Request $request):array
    {
        $params = $request->validate([
            'with_progress' => 'nullable|boolean',
            'student_id' => 'required_if:with_progress,true|integer|exists:user,id',
            'unit_id' => 'required_if:with_progress,true|integer|exists:course_unit,id',
        ],[
            'with_progress.boolean' => T("Course::validation.resource_video_info.with_progress.boolean"),
            'student_id.required_if' => T("Course::validation.resource_video_info.student_id.required_if"),
            'student_id.integer' => T("Course::validation.resource_video_info.student_id.integer"),
            'student_id.exists' => T("Course::validation.resource_video_info.student_id.exists"),
            'unit_id.required_if' => T("Course::validation.resource_video_info.unit_id.required_if"),
            'unit_id.integer' => T("Course::validation.resource_video_info.unit_id.integer"),
            'unit_id.exists' => T("Course::validation.resource_video_info.unit_id.exists"),
        ]);
        $result = $this->service->videoInfo($id,$params);
        return $result;
    }

    /**
     * 获取文件资源信息
     * 
     * @param int $id
     * @return array
     */
    public function fileInfo(int $id):array
    {
        $result = $this->service->fileInfo($id);
        return $result;
    }

    /**
     * 保存视频进度
     * 
     * @param int $id
     * @param Request $request
     * @return array
     */
    public function saveVideoProgress(int $id, Request $request):array
    {
        $params = $request->validate([
            'student_id' => 'required|integer|exists:user,id',
            'position' => 'required|integer',
            'duration' => 'required|integer',
            'unit_id' => 'required|integer|exists:course_unit,id',
            'course_id' => 'required|integer|exists:course,id',
            'start_time' => 'required|date',
        ],[
            'student_id.required' => T("Course::validation.resource_save_video_progress.student_id.required"),
            'student_id.integer' => T("Course::validation.resource_save_video_progress.student_id.integer"),
            'student_id.exists' => T("Course::validation.resource_save_video_progress.student_id.exists"),
            'position.required' => T("Course::validation.resource_save_video_progress.position.required"),
            'position.integer' => T("Course::validation.resource_save_video_progress.position.integer"),
            'duration.required' => T("Course::validation.resource_save_video_progress.duration.required"),
            'duration.integer' => T("Course::validation.resource_save_video_progress.duration.integer"),
            'course_id.required' => T("Course::validation.resource_save_video_progress.course_id.required"),
            'course_id.integer' => T("Course::validation.resource_save_video_progress.course_id.integer"),
            'course_id.exists' => T("Course::validation.resource_save_video_progress.course_id.exists"),
            'start_time.required' => T("Course::validation.resource_save_video_progress.start_time.required"),
            'start_time.date' => T("Course::validation.resource_save_video_progress.start_time.date"),
        ]);
        $result = $this->service->saveVideoProgress($id, $params);
        return $result;
    }

    /**
     * 保存视频资源
     * 
     * @param Request $request
     * @return array
     */
    public function saveVideo(Request $request):array
    {
        $params = $request->validate([
            'id' => 'nullable|integer|exists:video_resource,id',
            'course_id' => 'required|integer|exists:course,id',
            'unit_id' => 'required|integer|exists:course_unit,id',
            'title' => 'required|string',
            'description' => 'nullable|string',
            'video_url' => 'required|string',
            'duration' => 'required|integer',
            'allow_fast_forward' => 'required|boolean',
            'sort_order' => 'nullable|integer|min:0',
        ],[
            'id.integer' => T("Course::validation.resource_save_video.id.integer"),
            'id.exists' => T("Course::validation.resource_save_video.id.exists"),
            'course_id.required' => T("Course::validation.resource_save_video.course_id.required"),
            'course_id.integer' => T("Course::validation.resource_save_video.course_id.integer"),
            'course_id.exists' => T("Course::validation.resource_save_video.course_id.exists"),
            'unit_id.required' => T("Course::validation.resource_save_video.unit_id.required"), 
            'unit_id.integer' => T("Course::validation.resource_save_video.unit_id.integer"),
            'unit_id.exists' => T("Course::validation.resource_save_video.unit_id.exists"),
            'title.required' => T("Course::validation.resource_save_video.title.required"),
            'title.string' => T("Course::validation.resource_save_video.title.string"),
            'description.string' => T("Course::validation.resource_save_video.description.string"),
            'video_url.string' => T("Course::validation.resource_save_video.video_url.string"),
            'duration.integer' => T("Course::validation.resource_save_video.duration.integer"),
            'allow_fast_forward.boolean' => T("Course::validation.resource_save_video.allow_fast_forward.boolean"),
            'sort_order.integer' => T("Course::validation.resource_save_video.sort_order.integer"),
            'sort_order.min' => T("Course::validation.resource_save_video.sort_order.min"),
        ]);
        $result = $this->service->saveVideoResource($params);
        return $result;
    }

    /**
     * 保存文件资源
     * 
     * @param Request $request
     * @return array
     */
    public function saveFile(Request $request):array
    {
        $params = $request->validate([
            'id' => 'nullable|integer|exists:unit_file,id',
            'unit_id' => 'required|integer|exists:course_unit,id',
            'title' => 'required|string|max:200',
            'description' => 'nullable|string',
            'file_url' => 'required|string|max:500',
            'file_type' => 'required|string|max:50',
            'is_required' => 'nullable|boolean',
            'sort_order' => 'nullable|integer|min:0',
        ], [
            'id.integer' => T("Course::validation.resource_save_file.id.integer"),
            'id.exists' => T("Course::validation.resource_save_file.id.exists"),
            'unit_id.required' => T("Course::validation.resource_save_file.unit_id.required"),
            'unit_id.integer' => T("Course::validation.resource_save_file.unit_id.integer"),
            'unit_id.exists' => T("Course::validation.resource_save_file.unit_id.exists"),
            'title.string' => T("Course::validation.resource_save_file.title.string"),
            'title.max' => T("Course::validation.resource_save_file.title.max"),
            'description.string' => T("Course::validation.resource_save_file.description.string"),
            'file_url.string' => T("Course::validation.resource_save_file.file_url.string"),
            'file_url.max' => T("Course::validation.resource_save_file.file_url.max"),
            'file_type.string' => T("Course::validation.resource_save_file.file_type.string"),
            'file_type.max' => T("Course::validation.resource_save_file.file_type.max"),
            'is_required.boolean' => T("Course::validation.resource_save_file.is_required.boolean"),
            'sort_order.integer' => T("Course::validation.resource_save_file.sort_order.integer"),
            'sort_order.min' => T("Course::validation.resource_save_file.sort_order.min"),
        ]);
        $result = $this->service->saveFileResource($params);
        return $result;
    }


    public function saveQuiz(Request $request):array
    {
        $params = $request->validate([
            'id' => 'nullable|integer|exists:quiz,id',
            'unit_id' => 'required|integer|exists:course_unit,id',
            'title' => 'required|string|max:200',
            'description' => 'nullable|string',
            'question_category_id' => 'required|integer|exists:question_category,id',
            'question_num' => 'required|integer|min:1',
            'type' => 'required|string|max:200',
            'pass_score' => 'required|numeric|min:0',
            'total_score' => 'required|numeric|min:0',
            'sort_order' => 'nullable|integer|min:0',
        ], [
            'id.integer' => T("Course::validation.resource_save_file_question.id.integer"),
            'id.exists' => T("Course::validation.resource_save_file_question.id.exists"),
            'unit_id.required' => T("Course::validation.resource_save_file_question.unit_id.required"),
            'unit_id.integer' => T("Course::validation.resource_save_file_question.unit_id.integer"),
            'unit_id.exists' => T("Course::validation.resource_save_file_question.unit_id.exists"),
            'title.string' => T("Course::validation.resource_save_file_question.title.string"),
            'title.max' => T("Course::validation.resource_save_file_question.title.max"),
            'description.string' => T("Course::validation.resource_save_file_question.description.string"),
            'question_category_id.integer' => T("Course::validation.resource_save_file_question.question_category_id.integer"),
            'question_category_id.exists' => T("Course::validation.resource_save_file_question.question_category_id.exists"),
            'question_num.integer' => T("Course::validation.resource_save_file_question.question_num.integer"),
            'question_num.min' => T("Course::validation.resource_save_file_question.question_num.min"),
            'type.string' => T("Course::validation.resource_save_file_question.type.string"),
            'type.max' => T("Course::validation.resource_save_file_question.type.max"),
            'pass_score.numeric' => T("Course::validation.resource_save_file_question.pass_score.numeric"),
            'pass_score.min' => T("Course::validation.resource_save_file_question.pass_score.min"),
            'total_score.numeric' => T("Course::validation.resource_save_file_question.total_score.numeric"),
            'total_score.min' => T("Course::validation.resource_save_file_question.total_score.min"),
            'sort_order.integer' => T("Course::validation.resource_save_file_question.sort_order.integer"),
            'sort_order.min' => T("Course::validation.resource_save_file_question.sort_order.min"),
        ]);
        $result = $this->service->saveQuiz($params);
        return $result;
    }

    /**
     * 保存反思报告
     * 
     * @param Request $request
     * @return array
     */
    public function saveReflection(Request $request):array
    {
        $params = $request->validate([
            'id' => 'nullable|integer|exists:reflection_report,id',
            'course_id' => 'required|integer|exists:course,id',
            'unit_id' => 'required|integer|exists:course_unit,id',
            'title' => 'required|string|max:200',
            'description' => 'nullable|string',
            'is_hidden' => 'nullable|boolean',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'total_score' => 'required|numeric|min:0|max:100',
            'sort_order' => 'nullable|integer|min:0',
        ], [
            'id.integer' => T("Course::validation.resource_save_reflection.id.integer"),
            'id.exists' => T("Course::validation.resource_save_reflection.id.exists"),
            'course_id.required' => T("Course::validation.resource_save_reflection.course_id.required"),
            'course_id.integer' => T("Course::validation.resource_save_reflection.course_id.integer"),
            'course_id.exists' => T("Course::validation.resource_save_reflection.course_id.exists"),
            'unit_id.required' => T("Course::validation.resource_save_reflection.unit_id.required"),
            'unit_id.integer' => T("Course::validation.resource_save_reflection.unit_id.integer"),
            'unit_id.exists' => T("Course::validation.resource_save_reflection.unit_id.exists"),
            'title.string' => T("Course::validation.resource_save_reflection.title.string"),
            'title.max' => T("Course::validation.resource_save_reflection.title.max"),
            'description.string' => T("Course::validation.resource_save_reflection.description.string"),
            'is_hidden.boolean' => T("Course::validation.resource_save_reflection.is_hidden.boolean"),
            'start_date.date' => T("Course::validation.resource_save_reflection.start_date.date"),
            'end_date.date' => T("Course::validation.resource_save_reflection.end_date.date"),
            'end_date.after_or_equal' => T("Course::validation.resource_save_reflection.end_date.after_or_equal"),
            'total_score.numeric' => T("Course::validation.resource_save_reflection.total_score.numeric"),
            'total_score.min' => T("Course::validation.resource_save_reflection.total_score.min"),
            'total_score.max' => T("Course::validation.resource_save_reflection.total_score.max"),
            'sort_order.integer' => T("Course::validation.resource_save_reflection.sort_order.integer"),
            'sort_order.min' => T("Course::validation.resource_save_reflection.sort_order.min"),
        ]);
        $result = $this->service->saveReflection($params);
        return $result;
    }

    /**
     * 根据类型删除资源
     * 
     * @param int $id 资源ID
     * @param Request $request
     * @return array
     */
    public function delByType(int $id, Request $request): array
    {
        $params = $request->validate([
            'type' => 'required|string|in:video,file,folder,quiz,reflection',
        ], [
            'type.required' => T("Course::validation.resource_del.type.required"),
            'type.string' => T("Course::validation.resource_del.type.string"),
            'type.in' => T("Course::validation.resource_del.type.in"),
        ]);

        $result = $this->service->delByType($id, $params['type']);
        return $result;
    }
} 