<?php

namespace Modules\Course\Api\Controllers;

use Illuminate\Http\Request;
use Modules\Course\Models\User;
use Bingo\Exceptions\BizException;
use Modules\Course\Enums\ErrorCode;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Modules\Course\Services\UserService;

class UserController extends Controller
{
    protected UserService $service;

    public function __construct(UserService $service)
    {
        $this->service = $service;
    }

    /**
     * 获取用户信息
     * 
     * @param int $id
     * @return array
     */
    public function info(int $id): array
    {
        return $this->service->info($id);
    }
    
    /**
     * 获取用户列表
     * 
     * @param Request $request
     * @return array
     */
    public function list(Request $request): array
    {
        return $this->service->list($request->all());
    }

    /**
     * 用户登录
     *
     * @param Request $request
     * @return array
     * @throws BizException
     */
    public function login(Request $request): array
    {
         // 验证请求参数
         $params = $request->validate([
            'account' => 'required|string',
            'password' => 'required|string',
        ],[
            'account.required' => T("Course::validation.user_login.account.required"),
            'password.required' => T("Course::validation.user_login.password.required"),
        ]);

        return $this->service->login($params);
    }
    
    /**
     * 用户登出
     * 
     * @return array
     */
    public function logout(): array
    {
        $this->service->logout();
        return [];
    }

    /**
     * 忘记密码
     *
     * @param Request $request
     * @return array
     * @throws BizException
     */
    public function forgetPassword(Request $request): array
    {
        $params = $request->validate([
            'account' => 'required_without:email|string',
            'email' => 'nullable|email|required_without:account',
            'send_email' => 'nullable|boolean',
        ],[
            'account.required_without' => T("Course::validation.user_forget_password.account.required_without"),
            'email.required_without' => T("Course::validation.user_forget_password.email.required_without"),
            'email.email' => T("Course::validation.user_forget_password.email.email"),
            'send_email.boolean' => T("Course::validation.user_forget_password.send_email.boolean"),
        ]);

        return $this->service->forgetPassword($params);
    }

        /**
         * 验证重置密码令牌
         * 
         * @param Request $request
         * @return array
         */
        public function verifyResetPasswordToken(Request $request): array
        {
            $params = $request->validate([
                'token' => 'required|string',
            ],[
                'token.required' => T("Course::validation.user_verify_reset_password_token.token.required"),
            ]);
            return $this->service->verifyResetPasswordToken($params);
        }
    
    
    /**
     * 重置密码
     * 
     * @param Request $request
     * @return array
     */
    public function resetPassword(Request $request): array
    {
        $params = $request->validate([  
            'id' => 'required|integer',
            'password' => 'required|string',
            'confirm_password' => 'required|string',
            'token' => 'nullable|string',
        ],[
            'id.required' => T("Course::validation.user_reset_password.id.required"),
            'password.required' => T("Course::validation.user_reset_password.password.required"),
            'confirm_password.required' => T("Course::validation.user_reset_password.confirm_password.required"),
            'token.required' => T("Course::validation.user_reset_password.token.required"),
        ]);
        $this->service->resetPassword($params);
        return [];
    }

    /**
     * 更新密码
     * 
     * @param Request $request
     * @return array
     */
    public function updatePassword(Request $request): array
    {
        $params = $request->validate([
            'id' => 'required|integer',
            'old_password' => 'required|string',
            'password' => 'required|string',
            'confirm_password' => 'required|string',
        ], [
            'id.required' => T("Course::validation.user_update_password.id.required"),
            'old_password.required' => T("Course::validation.user_update_password.old_password.required"),
            'password.required' => T("Course::validation.user_update_password.password.required"),
            'confirm_password.required' => T("Course::validation.user_update_password.confirm_password.required"),
        ]);
        $this->service->updatePassword($params);
        return [];
    }
    

    /**
     * 获取用户权限树
     * 
     * @param int $id
     * @return array
     */
    public function permissionTree(int $id): array  
    {
        return $this->service->permissionTree($id);
    }

    /**
     * 更新用户信息
     * 
     * @param Request $request
     * @return array
     */
    public function update(Request $request): array
    {
        $params = $request->validate([
            'id' => 'required|integer',
            'show_email_type' => 'nullable|integer|in:'.implode(',', array_keys(User::SHOW_EMAIL_TYPE_MAP)),
            'moodlenet_account' => 'nullable|string|max:50',
            'city_address' => 'nullable|string|max:200',
            'country' => 'nullable|string|max:50',
            'timezone' => 'nullable|string|max:50',
            'introduction' => 'nullable|string',
            'avatar_url' => 'nullable|string|url|max:255',
            'avatar_description' => 'nullable|string|max:100',
            'interests' => 'nullable|string|max:200',
            'phone_number' => 'nullable|string|max:20',
            'mobile_number' => 'nullable|string|max:20',
            'detailed_address' => 'nullable|string|max:200',
            'department' => 'nullable|string|max:100',
            'additional_last_name' => 'nullable|string|max:50',
            'additional_first_name' => 'nullable|string|max:50',
            'additional_middle_name' => 'nullable|string|max:50',
            'additional_alias' => 'nullable|string|max:50',
            'status' => 'nullable|integer|in:0,1',
        ], [
            'id.required' => T("Course::validation.user_update.id.required"),
            'id.integer' => T("Course::validation.user_update.id.integer"),
            'show_email_type.in' => T("Course::validation.user_update.show_email_type.in"),
            'avatar_url.url' => T("Course::validation.user_update.avatar_url.url"),
            'status.in' => T("Course::validation.user_update.status.in"),
            'moodlenet_account.max' => T("Course::validation.user_update.moodlenet_account.max"),
            'city_address.max' => T("Course::validation.user_update.city_address.max"),
            'country.max' => T("Course::validation.user_update.country.max"),
            'timezone.max' => T("Course::validation.user_update.timezone.max"),
            'introduction.max' => T("Course::validation.user_update.introduction.max"),
            'avatar_description.max' => T("Course::validation.user_update.avatar_description.max"),
            'interests.max' => T("Course::validation.user_update.interests.max"),
            'phone_number.max' => T("Course::validation.user_update.phone_number.max"),
            'mobile_number.max' => T("Course::validation.user_update.mobile_number.max"),
            'detailed_address.max' => T("Course::validation.user_update.detailed_address.max"),
            'department.max' => T("Course::validation.user_update.department.max"),
            'additional_last_name.max' => T("Course::validation.user_update.additional_last_name.max"),
            'additional_first_name.max' => T("Course::validation.user_update.additional_first_name.max"),
            'additional_middle_name.max' => T("Course::validation.user_update.additional_middle_name.max"),
            'additional_alias.max' => T("Course::validation.user_update.additional_alias.max"),
            'status.in' => T("Course::validation.user_update.status.in"),
        ]);
        return $this->service->update($params);
    }

    /**
     * 刷新令牌
     * 
     * @return array
     */
    public function refreshToken(): array
    {
        return $this->service->refreshToken();
    }

    /**
     * 获取课程成员列表
     * 
     * @param Request $request
     * @return array
     */
    public function courseMembers(Request $request): array
    {
        $params = $request->validate([  
            'course_id' => 'required|integer',
            'first_initial' => 'nullable|string|max:50',
            'last_initial' => 'nullable|string|max:50',
            'keyword' => 'nullable|string|max:50',
            'role_id' => 'nullable|integer',
            'page'=> 'nullable|integer',
            'limit' => 'nullable|integer'
        ], [
            'course_id.required' => T("Course::validation.user_course_members.course_id.required"),
            'course_id.integer' => T("Course::validation.user_course_members.course_id.integer"),
            'first_initial.string' => T("Course::validation.user_course_members.first_initial.string"),
            'first_initial.max' => T("Course::validation.user_course_members.first_initial.max"),
            'last_initial.string' => T("Course::validation.user_course_members.last_initial.string"),
            'last_initial.max' => T("Course::validation.user_course_members.last_initial.max"),
            'keyword.string' => T("Course::validation.user_course_members.keyword.string"),
            'keyword.max' => T("Course::validation.user_course_members.keyword.max"),
            'role_id.integer' => T("Course::validation.user_course_members.role_id.integer"),
            'page.integer' => T("Course::validation.user_course_members.page.integer"),
            'limit.integer' => T("Course::validation.user_course_members.limit.integer"),
        ]);
        return $this->service->courseMembers($params);
    }

    /**
     * 导入课程用户
     * 
     * @param Request $request
     * @return array
     */
    public function importCourseMembers(Request $request): array
    {
        $params = $request->validate([
            'course_id' => 'required|integer',
            'file' => 'required|file|mimes:csv,xlsx,xls',
        ], [
            'course_id.required' => T("Course::validation.user_import_course_members_check.course_id.required"),
            'course_id.integer' => T("Course::validation.user_import_course_members_check.course_id.integer"),
            'file.required' => T("Course::validation.user_import_course_members_check.file.required"),
            'file.file' => T("Course::validation.user_import_course_members_check.file.file"),
            'file.mimes' => T("Course::validation.user_import_course_members_check.file.mimes"),
        ]);
        
        return $this->service->importCourseMembers($params);
    }

    /**
     * 导入课程用户检查
     * 
     * @param Request $request
     * @return array
     */
    public function importCourseMembersCheck(Request $request): array
    {
        $params = $request->validate([
            'course_id' => 'required|integer',
            'file' => 'required|file|mimes:csv,xlsx,xls|max:10240', // 限制文件大小最大为10MB
        ], [
            'course_id.required' => T("Course::validation.user_import_course_members_check.course_id.required"),
            'course_id.integer' => T("Course::validation.user_import_course_members_check.course_id.integer"),
            'file.required' => T("Course::validation.user_import_course_members_check.file.required"),
            'file.file' => T("Course::validation.user_import_course_members_check.file.file"),
            'file.mimes' => T("Course::validation.user_import_course_members_check.file.mimes"),
            'file.max' => T("Course::validation.user_import_course_members_check.file.max"),
        ]);
        
        return $this->service->importCourseMembersCheck($params);
    }

    /**
     * 获取登录用户信息
     * 
     * @return array
     */
    public function getUserInfo(): array
    {
        $id = Auth::user()->id ?? 0;
        if (!$id) {
            BizException::throws(ErrorCode::USER_LOGIN_EXPIRED,ErrorCode::USER_LOGIN_EXPIRED->value);
        }

        return $this->service->info($id);
    }   
}
