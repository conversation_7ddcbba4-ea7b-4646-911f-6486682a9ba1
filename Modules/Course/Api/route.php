<?php

use Modules\Course\Api\Controllers\InfoController;
use Modules\Course\Api\Controllers\QuizController;
use Modules\Course\Api\Controllers\RoleController;
use Modules\Course\Api\Controllers\UnitController;
use Modules\Course\Api\Controllers\UserController;
use Modules\Course\Api\Controllers\CourseController;
use Modules\Course\Api\Controllers\FolderController;
use Modules\Course\Api\Controllers\CategoryController;
use Modules\Course\Api\Controllers\QuestionController;
use Modules\Course\Api\Controllers\ResourceController;
use Modules\Course\Api\Controllers\CourseUserController;
use Modules\Course\Api\Controllers\PermissionController;
use Modules\Course\Api\Controllers\ReflectionController;
use Modules\Course\Api\Controllers\CourseCategoryController;
use Modules\Course\Api\Controllers\UserPreferenceController;
use App\Http\Middleware\TokenAuthentication;
use App\Http\Middleware\CheckPermission;
// 这里已经是api路由，由系统自动添加api前缀
Route::prefix('course')->group(function () {
        // 授权管理
        Route::prefix('auth')->group(function () {
                // login
                Route::post('login', [UserController::class, 'login']);
                // 忘记密码
                Route::post('forget-password', [UserController::class, 'forgetPassword']);
                // 重置密码
                Route::post('reset-password', [UserController::class, 'resetPassword']);
                // 验证重置密码令牌
                Route::post('verify-reset-password-token', [UserController::class, 'verifyResetPasswordToken']);
        });

        // 用户管理
        Route::prefix('user')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 获取用户列表
                Route::get('list', [UserController::class, 'list'])->name('user.list');
                // 获取用户信息
                Route::get('/{id}/info', [UserController::class, 'info'])->name('user.info');
                // 更新用户信息
                Route::post('update', [UserController::class, 'update'])->name('user.update');
                // logout
                Route::post('logout', [UserController::class, 'logout']);
                // 更新密码
                Route::post('update-password', [UserController::class, 'updatePassword'])->name('user.update-password');
                // 获取用户权限树
                Route::get('/{id}/permission-tree', [UserController::class, 'permissionTree'])->name('user.permission-tree');
                // 刷新令牌
                Route::post('refresh-token', [UserController::class, 'refreshToken'])->name('user.refresh-token');
                // 获取课程成员列表
                Route::get('/course-members', [UserController::class, 'courseMembers'])->name('user.course-members');
                // 导入课程用户
                Route::post('import-course-members', [UserController::class, 'importCourseMembers'])->name('user.import-course-members');
                // 导入课程用户检查
                Route::post('import-course-members-check', [UserController::class, 'importCourseMembersCheck'])->name('user.import-course-members-check');
                // 获取我参与的课程列表
                Route::get('/courseList', [CourseUserController::class, 'getUserCourseList'])->name('user.course-list');
                // 获取登录用户信息
                Route::get('/user-info', [UserController::class, 'getUserInfo'])->name('user.user-info');
        });

        // 用户偏好管理
        Route::prefix('user-preference')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 获取用户具体偏好
                Route::get('get', [UserPreferenceController::class, 'get'])->name('user-preference.get');
                // 更新用户偏好
                Route::post('update', [UserPreferenceController::class, 'update'])->name('user-preference.update');
                // 获取用户偏好列表
                Route::get('list', [UserPreferenceController::class, 'list'])->name('user-preference.list');
        });

        // 题库管理
        Route::prefix('question')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 添加题目
                Route::post('add', [QuestionController::class, 'add'])->name('question.add');
                // 获取题目列表
                Route::get('list', [QuestionController::class, 'list'])->name('question.list');
                // 获取题目信息
                Route::get('/{id}/info', [QuestionController::class, 'info'])->name('question.info');
                // 更新题目
                Route::post('/{id}/update', [QuestionController::class, 'update'])->name('question.update');
                // 批量删除题目
                Route::delete('/batch', [QuestionController::class, 'delete'])->name('question.batch-delete');
                // 删除单个题目
                Route::delete('/{id}', [QuestionController::class, 'delete'])->name('question.delete');


                // 题库分类管理
                Route::prefix('cate')->group(function () {
                        // 添加分类
                        Route::post('add', [CategoryController::class, 'add'])->name('question.category.add');
                        // 获取分类列表
                        Route::get('list', [CategoryController::class, 'getList'])->name('question.category.list');
                        // 通过父级分类名称获取所有子类（支持模糊查询）
                        Route::get('children', [CategoryController::class, 'getChildrenByParentName'])->name('question.category.children');
                        // 编辑分类
                        Route::post('update/{id}', [CategoryController::class, 'update'])->name('question.category.update');
                        // 删除分类（软删除）
                        Route::delete('{id}', [CategoryController::class, 'delete'])->name('question.category.delete');
                });


        });

        // 角色管理
        Route::prefix('role')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 获取角色列表
                Route::get('list', [RoleController::class, 'list'])->name('role.list');
                // 获取角色信息
                Route::get('/{id}/info', [RoleController::class, 'info'])->name('role.info');
                // 创建或更新角色
                Route::post('save', [RoleController::class, 'save'])->name('role.save');
                // 删除角色
                Route::delete('/{id}', [RoleController::class, 'delete'])->name('role.delete');
                // 开关角色
                Route::post('/{id}/toggle', [RoleController::class, 'toggle'])->name('role.toggle');
                // 根据课程id获取角色列表
                Route::get('/course/{id}/list', [RoleController::class, 'getRoleListByCourseId'])->name('role.get-role-list-by-course-id');
                // 根据课程id获取班主任信息
                Route::get('/course/{id}/class-teacher', [RoleController::class, 'getClassTeacherByCourseId'])->name('role.get-class-teacher-by-course-id');
        });

        // 权限管理
        Route::prefix('permission')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 获取权限树
                Route::get('tree', [PermissionController::class, 'tree'])->name('permission.tree');
                // 获取权限信息
                Route::get('/{id}/info', [PermissionController::class, 'info'])->name('permission.info');
        });

        // 课程分类管理
        Route::prefix('category')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 添加分类
                Route::post('add', [CourseCategoryController::class, 'add'])->name('category.add');
                // 获取分类列表
                Route::get('list', [CourseCategoryController::class, 'getList'])->name('category.list');
                // 获取分类详情
                Route::get('/{id}/info', [CourseCategoryController::class, 'info'])->name('category.info');
                // 编辑分类
                Route::post('/{id}/update', [CourseCategoryController::class, 'update'])->name('category.update');
                // 批量删除分类
                Route::delete('/batch', [CourseCategoryController::class, 'delete'])->name('category.batch-delete');
                // 删除单个分类
                Route::delete('/{id}', [CourseCategoryController::class, 'delete'])->name('category.delete');
                // 将多个分类转移到目标分类下
                Route::post('/move', [CourseCategoryController::class, 'moveToTarget'])->name('category.move');
                // 调整分类排序
                Route::post('/{id}/adjust-order', [CourseCategoryController::class, 'adjustOrder'])->name('category.adjust-order');
        });

        // 单元管理
        Route::prefix('unit')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 获取单元列表
                Route::get('list', [UnitController::class, 'list'])->name('unit.list');
                // 获取单元信息
                Route::get('/{id}/info', [UnitController::class, 'info'])->name('unit.info');
                // 批量保存单元
                Route::post('batch-save', [UnitController::class, 'batchSave'])->name('unit.batch-save');
                // 获取所有学生单元视频报表
                Route::get('/{id}/student-video-report', [UnitController::class, 'studentVideoReport'])->name('unit.student-video-report');
                // 保存单元信息
                Route::post('/save', [UnitController::class, 'save'])->name('unit.save');
                // 更新单元排序
                Route::post('/sort', [UnitController::class, 'sort'])->name('unit.sort');
                // 更新视频排序
                Route::post('/sortVideo', [UnitController::class, 'sortVideo'])->name('unit.sort-video');
        });

        // 资源管理
        Route::prefix('resource')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 文件夹资源管理
                Route::prefix('folder')->group(function () {
                        // 获取所有根目录资源
                        Route::get('root-list', [FolderController::class, 'rootList'])->name('resource.folder.root-list');
                        // 获取文件夹树结构
                        Route::get('tree', [FolderController::class, 'tree'])->name('resource.folder.tree');
                        // 保存文件夹资源
                        Route::post('save', [FolderController::class, 'save'])->name('resource.folder.save');
                        // 下载文件夹资源
                        Route::get('download/{id}', [FolderController::class, 'download'])->name('resource.folder.download');
                });

                // 上传文件
                Route::post('upload', [ResourceController::class, 'upload'])->name('resource.upload');
                // 获取视频资源
                Route::get('video/{id}/info', [ResourceController::class, 'videoInfo'])->name('resource.video.info');
                // 获取文件资源
                Route::get('file/{id}/info', [ResourceController::class, 'fileInfo'])->name('resource.file.info');
                // 更新视频进度
                Route::post('video/{id}/save-progress', [ResourceController::class, 'saveVideoProgress'])->name('resource.video.save-progress');
                // 保存视频资源
                Route::post('video/save', [ResourceController::class, 'saveVideo'])->name('resource.video.save');
                // 保存文件资源
                Route::post('file/save', [ResourceController::class, 'saveFile'])->name('resource.file.save');
                // 保存测验资源
                Route::post('quiz/save', [ResourceController::class, 'saveQuiz'])->name('resource.quiz.save');
                // 保存反思报告资源
                Route::post('reflection/save', [ResourceController::class, 'saveReflection'])->name('resource.reflection.save');
                // 根据类型删除资源
                Route::delete('{id}/delByType', [ResourceController::class, 'delByType'])->name('resource.delete-by-type');
        });

        // 课程管理
        Route::prefix('info')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 添加课程
                Route::post('add', [CourseController::class, 'add'])->name('course.add');
                // 获取课程列表
                Route::get('list', [CourseController::class, 'list'])->name('course.list');
                // 获取课程详情
                Route::get('/{id}/info', [CourseController::class, 'info'])->name('course.info');
                // 编辑课程
                Route::post('/{id}/update', [CourseController::class, 'update'])->name('course.update');
                // 调整课程排序
                Route::post('/{id}/adjust-order', [CourseController::class, 'adjustOrder'])->name('course.adjust-order');
                // 批量删除课程
                Route::delete('/batch', [CourseController::class, 'delete'])->name('course.batch-delete');
                // 删除单个课程
                Route::delete('/{id}', [CourseController::class, 'delete'])->name('course.delete');
                // 批量移动课程到指定分类
                Route::post('/move-to-category', [CourseController::class, 'moveToCategory'])->name('course.move-to-category');
                // 获取学生学习详情
                Route::post('/studyDetail', [InfoController::class, 'studyDetail'])->name('course.study-detail');
                // 新增：按首字母筛选的学生学习详情
                Route::post('/allStudyDetail', [InfoController::class, 'allStudyDetail'])->name('course.all-study-detail');
        });

        // 考试管理
        Route::prefix('quiz')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 获取题库并生成试卷
                Route::get('/{id}', [QuizController::class, 'getQuiz'])->name('quiz.get');

                // 提交答卷
                Route::post('/{id}', [QuizController::class, 'submitAnswer'])->name('quiz.submit-answer');

                // 获取当前用户在特定题库下的作答记录列表
                Route::get('/{id}/userAnswerList', [QuizController::class, 'userAnswerList'])->name('quiz.user-answer-list');

                // 获取答卷明细
                Route::get('/answer/{id}', [QuizController::class, 'getAnswerDetail'])->name('quiz.answer-detail');

                // 获取考考你绑定明细
                Route::get('/detail/{id}', [QuizController::class, 'getQuizDetail'])->name('quiz.detail');

                // 获取单个题目详情
                Route::get('/singleDetail', [QuizController::class, 'getSingleDetail'])->name('quiz.single-detail');

                // 修改题目判分
                Route::put('/editScore', [QuizController::class, 'editScore'])->name('quiz.edit-score');

                // 搜索答卷列表
                Route::get('/{id}/answer-list', [QuizController::class, 'searchAnswerList'])->name('quiz.answer-list');

                // 导出答卷列表为CSV
                Route::get('/{id}/export-csv', [QuizController::class, 'exportAnswerListToCsv'])->name('quiz.export-csv');
        });


        // 网站信息管理
        Route::prefix('site-info')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 创建网站信息
                Route::post('/add', [InfoController::class, 'createInfo'])->name('site-info.add');

                // 获取网站信息列表
                Route::get('/list', [InfoController::class, 'getInfoList'])->name('site-info.list');

                // 获取网站信息详情
                Route::get('/{id}', [InfoController::class, 'getInfoDetail'])->name('site-info.detail');

                // 获取FAQ列表
                Route::get('/faq/list', [InfoController::class, 'getFaqList'])->name('site-info.faq-list');

                // 获取公告列表
                Route::get('/announcement/list', [InfoController::class, 'getAnnouncementList'])->name('site-info.announcement-list');

                // 获取联系我们列表
                Route::get('/contact/list', [InfoController::class, 'getContactList'])->name('site-info.contact-list');
        });

        // 反思报告
        Route::prefix('reflection')->middleware([TokenAuthentication::class, CheckPermission::class])->group(function () {
                // 获取课程反思报告
                Route::get('/course/{id}', [ReflectionController::class, 'getCourseReflection'])->name('reflection.course.get');
                // 获取学生反思报告
                Route::get('/course/{courseId}/student/{studentId}', [ReflectionController::class, 'getStudentReflection'])->name('reflection.student.get');
                // 获取课程反思报告列表
                Route::get('/course/{id}/list', [ReflectionController::class, 'getCourseReflectionList'])->name('reflection.course.list');
                // 批量切换反思报告锁定状态
                Route::post('/batch-toggle-lock', [ReflectionController::class, 'batchToggleLock'])->name('reflection.batch-toggle-lock');
                // 批量下载反思报告
                Route::get('/batch-download', [ReflectionController::class, 'batchDownload'])->name('reflection.batch-download');
                // 批量退回草稿状态
                Route::post('/batch-back-draft', [ReflectionController::class, 'batchBackDraft'])->name('reflection.batch-back-draft');
                // 批量延期提交
                Route::post('/batch-delay-submit', [ReflectionController::class, 'batchDelaySubmit'])->name('reflection.batch-delay-submit');
                // 获取反思报告详情
                Route::post('/reportDetail', [ReflectionController::class, 'reportDetail'])->name('reflection.report-detail');
                // 提交反思报告
                Route::post('/sendReport', [ReflectionController::class, 'sendReport'])->name('reflection.send-report');
                // 获取评分标准
                Route::get('/scoring-criteria', [ReflectionController::class, 'getScoringCriteria'])->name('reflection.scoring-criteria');
                // 进行评分
                Route::post('/score', [ReflectionController::class, 'score'])->name('reflection.score');
                // 添加反思报告提交记录
                Route::post('/add', [ReflectionController::class, 'add'])->name('reflection.add');
        });
});

