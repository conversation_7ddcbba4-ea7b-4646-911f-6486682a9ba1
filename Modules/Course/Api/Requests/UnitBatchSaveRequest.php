<?php

namespace Modules\Course\Api\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Course\Domain\Repositories\ResourceRepository;

class UnitBatchSaveRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * 获取验证规则
     *
     * @return array
     */
    public function rules()
    {
        return [
            'course_id' => 'required|integer|exists:course,id',
            'units' => 'nullable|array',
            'units.*.id' => 'nullable|integer',
            'units.*.title_cn' => 'required|string|max:200',
            'units.*.title_en' => 'required|string|max:200',
            'units.*.title' => 'required|string|max:200',
            'units.*.description' => 'nullable|string',
            'units.*.icon_url' => 'nullable|string|max:500',
            'units.*.sort_order' => 'nullable|integer|min:0',
            'units.*.resources' => 'nullable|array',
            'units.*.resources.*.id' => 'nullable|integer',
            'units.*.resources.*.type' => 'required|string|in:' . implode(',', [
                ResourceRepository::TYPE_FILE,
                ResourceRepository::TYPE_VIDEO,
                ResourceRepository::TYPE_QUIZ,
                ResourceRepository::TYPE_FOLDER,
                ResourceRepository::TYPE_REPORT,
            ]),
            'units.*.resources.*.title' => 'required|string|max:200',
            'units.*.resources.*.description' => 'nullable|string',
            'units.*.resources.*.sort_order' => 'required|integer|min:0',
            // 文件资源特有字段
            'units.*.resources.*.file_type' => 'required_if:units.*.resources.*.type,file|string|in:pdf,word,excel,ppt',
            'units.*.resources.*.file_size' => 'required_if:units.*.resources.*.type,file|integer|min:0',
            'units.*.resources.*.url' => 'required_if:units.*.resources.*.type,file|string|max:500',
            // 视频资源特有字段
            'units.*.resources.*.duration' => 'required_if:units.*.resources.*.type,video|integer|min:0',
            'units.*.resources.*.allow_fast_forward' => 'required_if:units.*.resources.*.type,video|boolean',
            'units.*.resources.*.url' => 'required_if:units.*.resources.*.type,video|string|max:500',
            // 试题资源特有字段 - 从题库中选择的题目ID数组
            'units.*.resources.*.quiz_category_id' => 'required_if:units.*.resources.*.type,quiz|integer|exists:question_category,id',
            'units.*.resources.*.question_num' => 'required_if:units.*.resources.*.type,quiz|integer|min:0',
            'units.*.resources.*.pass_score' => 'required_if:units.*.resources.*.type,quiz|decimal:0,2|min:0',
            'units.*.resources.*.total_score' => 'required_if:units.*.resources.*.type,quiz|decimal:0,2|min:0',
            // 反思报告特有字段
            'units.*.resources.*.start_date' => 'required_if:units.*.resources.*.type,report|date',
            'units.*.resources.*.end_date' => 'required_if:units.*.resources.*.type,report|date',
            'units.*.resources.*.is_hidden' => 'required_if:units.*.resources.*.type,report|boolean',
            'units.*.resources.*.title' => 'required_if:units.*.resources.*.type,report|string|max:200',
            'units.*.resources.*.description' => 'required_if:units.*.resources.*.type,report|string',
            'units.*.resources.*.total_score' => 'required_if:units.*.resources.*.type,report|decimal:0,2|min:0',
        ];
    }


    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages()
    {
        return [
            'course_id.required' => T("Course::validation.unit_save.course_id.required"),
            'course_id.exists' => T("Course::validation.unit_save.course_id.exists"),
            'units.required' => T("Course::validation.unit_save.units.required"),
            'units.array' => T("Course::validation.unit_save.units.array"),
            'units.*.id.nullable' => T("Course::validation.unit_save.units.id.nullable"),
            'units.*.id.integer' => T("Course::validation.unit_save.units.id.integer"),
            'units.*.title_cn.required' => T("Course::validation.unit_save.title_cn.required"),
            'units.*.title_en.required' => T("Course::validation.unit_save.title_en.required"),
            'units.*.title.required' => T("Course::validation.unit_save.title.required"),
            'units.*.resources.required' => T("Course::validation.unit_save.resources.required"),
            'units.*.resources.array' => T("Course::validation.unit_save.resources.array"),
            'units.*.resources.*.id.nullable' => T("Course::validation.unit_save.resources.id.nullable"),
            'units.*.resources.*.id.integer' => T("Course::validation.unit_save.resources.id.integer"),
            'units.*.resources.*.type.required' => T("Course::validation.unit_save.resource_type.required"),
            'units.*.resources.*.type.in' => T("Course::validation.unit_save.resource_type.invalid"),
            'units.*.resources.*.title.required' => T("Course::validation.unit_save.resource_title.required"),
            'units.*.resources.*.url.required' => T("Course::validation.unit_save.resource_url.required"),
            'units.*.resources.*.sort_order.required' => T("Course::validation.unit_save.resource_sort_order.required"),
            // 文件资源验证消息
            'units.*.resources.*.file_type.required_if' => T("Course::validation.unit_save.file_type.required"),
            'units.*.resources.*.file_size.required_if' => T("Course::validation.unit_save.file_size.required"),
            'units.*.resources.*.url.required_if' => T("Course::validation.unit_save.file_url.required"),
            // 视频资源验证消息
            'units.*.resources.*.duration.required_if' => T("Course::validation.unit_save.duration.required"),
            'units.*.resources.*.allow_fast_forward.required_if' => T("Course::validation.unit_save.allow_fast_forward.required"),
            'units.*.resources.*.url.required_if' => T("Course::validation.unit_save.video_url.required"),
            // 试题资源验证消息
            'units.*.resources.*.quiz_category_id.required_if' => T("Course::validation.unit_save.quiz_category_id.required"),
            'units.*.resources.*.quiz_category_id.integer' => T("Course::validation.unit_save.quiz_category_id.integer"),
            'units.*.resources.*.quiz_category_id.exists' => T("Course::validation.unit_save.quiz_category_id.exists"),
            'units.*.resources.*.question_num.required_if' => T("Course::validation.unit_save.question_num.required"),
            'units.*.resources.*.question_num.integer' => T("Course::validation.unit_save.question_num.integer"),
            'units.*.resources.*.question_num.min' => T("Course::validation.unit_save.question_num.min"),
            'units.*.resources.*.score_type.required_if' => T("Course::validation.unit_save.score_type.required"),
            'units.*.resources.*.score_type.string' => T("Course::validation.unit_save.score_type.string"),
            'units.*.resources.*.pass_score.required_if' => T("Course::validation.unit_save.pass_score.required"),
            'units.*.resources.*.pass_score.decimal' => T("Course::validation.unit_save.pass_score.decimal"),
            'units.*.resources.*.pass_score.min' => T("Course::validation.unit_save.pass_score.min"),
            'units.*.resources.*.total_score.required_if' => T("Course::validation.unit_save.total_score.required"),
            'units.*.resources.*.total_score.decimal' => T("Course::validation.unit_save.total_score.decimal"),
            'units.*.resources.*.total_score.min' => T("Course::validation.unit_save.total_score.min"),
            // 反思报告验证消息
            'units.*.resources.*.report_type.required_if' => T("Course::validation.unit_save.report_type.required"),
            'units.*.resources.*.report_type.in' => T("Course::validation.unit_save.report_type.invalid"),
            'units.*.resources.*.start_date.required_if' => T("Course::validation.unit_save.start_date.required"),
            'units.*.resources.*.end_date.required_if' => T("Course::validation.unit_save.end_date.required"),
            'units.*.resources.*.is_hidden.required_if' => T("Course::validation.unit_save.is_hidden.required"),
            'units.*.resources.*.title.required_if' => T("Course::validation.unit_save.title.required"),
            'units.*.resources.*.description.required_if' => T("Course::validation.unit_save.description.required"),  
        ];
    }
} 