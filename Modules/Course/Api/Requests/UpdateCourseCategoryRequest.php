<?php

namespace Modules\Course\Api\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCourseCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'parent_id' => 'nullable|integer|min:0',
            'name' => 'nullable|string|max:100',
            'cate_id_num' => 'nullable|string|max:300',
            'description' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.max' => T('Course::base.course_category.validation.name.max', '分类名称不能超过100个字符'),
            'parent_id.integer' => T('Course::base.course_category.validation.parent_id.integer', '父分类ID必须是整数'),
            'parent_id.min' => T('Course::base.course_category.validation.parent_id.min', '父分类ID不能小于0'),
            'cate_id_num.max' => T('Course::base.course_category.validation.cate_id_num.max', '分类ID号不能超过300个字符'),
            'sort_order.integer' => T('Course::base.course_category.validation.sort_order.integer', '排序序号必须是整数'),
            'sort_order.min' => T('Course::base.course_category.validation.sort_order.min', '排序序号不能小于0'),
        ];
    }
}
