<?php

namespace Modules\Course\Api\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateQuestionRequest extends FormRequest
{
    /**
     * 确定用户是否有权提交此请求
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // 可以根据实际需求调整授权逻辑
    }

    /**
     * 获取适用于请求的验证规则
     *
     * @return array
     */
    public function rules()
    {
        return [
            'category_id' => 'nullable|integer|exists:question_category,id',
            'question_point' => 'nullable|string|max:300',
            'question_detail' => 'nullable|string',
            'score' => 'nullable|numeric|min:0',
            'question_feedback' => 'nullable|string',
            'question_no' => 'nullable|string|max:50',
            'question_type' => 'nullable|integer|in:1,2',
            'is_range' => 'nullable|integer|in:1,2',
            'answer' => 'nullable|string', // 接收JSON字符串
            'answer_feedback' => 'nullable|string', // 接收JSON字符串
        ];
    }

    /**
     * 准备验证数据
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // 如果answer和answer_feedback是JSON字符串，尝试解码
        if ($this->has('answer') && is_string($this->answer)) {
            $this->merge([
                'answer_decoded' => json_decode($this->answer, true)
            ]);
        }
        
        if ($this->has('answer_feedback') && is_string($this->answer_feedback)) {
            $this->merge([
                'answer_feedback_decoded' => json_decode($this->answer_feedback, true)
            ]);
        }
    }

    /**
     * 获取已定义验证规则的错误消息
     *
     * @return array
     */
    public function messages()
    {
        return [
            'category_id.integer' => T('Course::base.question.validation.category_id.integer'),
            'category_id.exists' => T('Course::base.question.validation.category_id.exists'),
            'question_point.max' => T('Course::base.question.validation.question_point.max'),
            'score.numeric' => T('Course::base.question.validation.score.numeric'),
            'score.min' => T('Course::base.question.validation.score.min'),
            'question_type.integer' => T('Course::base.question.validation.question_type.integer'),
            'question_type.in' => T('Course::base.question.validation.question_type.in'),
            'is_range.integer' => T('Course::base.question.validation.is_range.integer'),
            'is_range.in' => T('Course::base.question.validation.is_range.in'),
            'answer.string' => T('Course::base.question.validation.answer.string'),
            'answer_feedback.string' => T('Course::base.question.validation.answer_feedback.string'),
        ];
    }
}
