<?php

namespace Modules\Course\Api\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddQuestionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // 可以根据实际需求调整授权逻辑
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'category_id' => 'required|integer|exists:question_category,id',
            'question_point' => 'required|string|max:300',
            'question_detail' => 'required|string',
            'score' => 'required|numeric|min:0',
            'question_feedback' => 'nullable|string',
            'question_no' => 'nullable|string|max:50',
            'question_type' => 'required|integer|in:1,2',
            'is_range' => 'nullable|integer|in:1,2',
            'answer' => 'required|string', // 接收JSON字符串
            'answer_feedback' => 'required|string', // 接收JSON字符串
        ];
    }

    /**
     * 准备验证数据
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // 如果answer和answer_feedback是JSON字符串，尝试解码
        if ($this->has('answer') && is_string($this->answer)) {
            $this->merge([
                'answer_decoded' => json_decode($this->answer, true)
            ]);
        }

        if ($this->has('answer_feedback') && is_string($this->answer_feedback)) {
            $this->merge([
                'answer_feedback_decoded' => json_decode($this->answer_feedback, true)
            ]);
        }
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'category_id.required' => T('Course::base.question.validation.category_id.required'),
            'category_id.integer' => T('Course::base.question.validation.category_id.integer'),
            'category_id.exists' => T('Course::base.question.validation.category_id.exists'),
            'question_point.required' => T('Course::base.question.validation.question_point.required'),
            'question_point.max' => T('Course::base.question.validation.question_point.max'),
            'question_detail.required' => T('Course::base.question.validation.question_detail.required'),
            'score.required' => T('Course::base.question.validation.score.required'),
            'score.numeric' => T('Course::base.question.validation.score.numeric'),
            'score.min' => T('Course::base.question.validation.score.min'),
            'question_type.required' => T('Course::base.question.validation.question_type.required'),
            'question_type.integer' => T('Course::base.question.validation.question_type.integer'),
            'question_type.in' => T('Course::base.question.validation.question_type.in'),
            'is_range.integer' => T('Course::base.question.validation.is_range.integer'),
            'is_range.in' => T('Course::base.question.validation.is_range.in'),
            'answer.required' => T('Course::base.question.validation.answer.required'),
            'answer.array' => T('Course::base.question.validation.answer.array'),
            'answer_feedback.required' => T('Course::base.question.validation.answer_feedback.required'),
            'answer_feedback.array' => T('Course::base.question.validation.answer_feedback.array'),
        ];
    }
}
