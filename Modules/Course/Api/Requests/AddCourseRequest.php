<?php

namespace Modules\Course\Api\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AddCourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:200',
            'short_name' => 'required|string|max:200',
            'category_id' => 'required|integer|exists:course_category,id',
            'is_show' => 'required|integer|in:1,2',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'course_id' => 'nullable|string|max:50|unique:course,course_id,NULL,id,deleted_at,0',
            'is_user' => 'required|integer|in:1,2',
            'roles' => 'nullable|string|max:200',
            'description' => 'nullable|string',
            'image_url' => 'nullable|string|max:500',
            'report_deadline' => 'nullable|date|after_or_equal:start_date',
            'max_students' => 'nullable|integer|min:1',
            'sort_order' => 'nullable|integer|min:1',
            'is_active' => 'nullable|integer|in:1,2',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => T('Course::base.course.validation.name.required', '课程名称不能为空'),
            'name.max' => T('Course::base.course.validation.name.max', '课程名称不能超过200个字符'),
            'short_name.required' => T('Course::base.course.validation.short_name.required', '课程简称不能为空'),
            'short_name.max' => T('Course::base.course.validation.short_name.max', '课程简称不能超过200个字符'),
            'category_id.required' => T('Course::base.course.validation.category_id.required', '课程分类不能为空'),
            'category_id.integer' => T('Course::base.course.validation.category_id.integer', '课程分类ID必须是整数'),
            'category_id.exists' => T('Course::base.course.validation.category_id.exists', '所选课程分类不存在'),
            'is_show.required' => T('Course::base.course.validation.is_show.required', '可见性不能为空'),
            'is_show.in' => T('Course::base.course.validation.is_show.in', '可见性值无效'),
            'start_date.required' => T('Course::base.course.validation.start_date.required', '开课日期不能为空'),
            'start_date.date' => T('Course::base.course.validation.start_date.date', '开课日期格式无效'),
            'end_date.required' => T('Course::base.course.validation.end_date.required', '结课日期不能为空'),
            'end_date.date' => T('Course::base.course.validation.end_date.date', '结课日期格式无效'),
            'end_date.after_or_equal' => T('Course::base.course.validation.end_date.after_or_equal', '结课日期必须晚于或等于开课日期'),
            'course_id.unique' => T('Course::base.course.validation.course_id.unique', '课程ID号码已存在'),
            'course_id.max' => T('Course::base.course.validation.course_id.max', '课程ID号码不能超过50个字符'),
            'is_user.required' => T('Course::base.course.validation.is_user.required', '是否包含用户不能为空'),
            'is_user.in' => T('Course::base.course.validation.is_user.in', '是否包含用户值无效'),
            'roles.max' => T('Course::base.course.validation.roles.max', '用户角色不能超过200个字符'),
            'image_url.max' => T('Course::base.course.validation.image_url.max', '图片URL不能超过500个字符'),
            'report_deadline.date' => T('Course::base.course.validation.report_deadline.date', '报告截止日期格式无效'),
            'report_deadline.after_or_equal' => T('Course::base.course.validation.report_deadline.after_or_equal', '报告截止日期必须晚于或等于开课日期'),
            'max_students.integer' => T('Course::base.course.validation.max_students.integer', '最大学生数必须是整数'),
            'max_students.min' => T('Course::base.course.validation.max_students.min', '最大学生数必须大于0'),
            'is_active.boolean' => T('Course::base.course.validation.is_active.boolean', '是否激活必须是布尔值'),
        ];
    }
}
