<?php

namespace Modules\Course\Api\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\Course\Models\FolderResource;

class FolderSaveRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * 获取验证规则
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id' => 'nullable|integer|exists:folder_resource,id',
            'unit_id' => 'required|integer|exists:course_unit,id',
            'title' => 'required|string|max:200',
            'description' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
            'children' => 'nullable|array',
            'children.*.id' => 'nullable|integer|exists:folder_resource,id',
            'children.*.title' => 'required|string|max:200',
            'children.*.description' => 'nullable|string',
            'children.*.sort_order' => 'nullable|integer|min:0',
            'children.*.resource_type' => 'required|string|in:' . FolderResource::TYPE_FOLDER . ',' . FolderResource::TYPE_FILE,
            'children.*.file_url' => 'required_if:children.*.resource_type,' . FolderResource::TYPE_FILE . '|string|max:500',
            'children.*.file_type' => 'required_if:children.*.resource_type,' . FolderResource::TYPE_FILE . '|string|max:50',
            'children.*.children' => 'nullable|array|required_if:children.*.resource_type,' . FolderResource::TYPE_FOLDER,
        ];
    }

    /**
     * 获取验证错误的自定义消息
     *
     * @return array
     */
    public function messages()
    {
        return [
            'id.integer' => T("Course::validation.folder_save.id.integer"),
            'id.exists' => T("Course::validation.folder_save.id.exists"),
            'unit_id.required' => T("Course::validation.folder_save.unit_id.required"),
            'unit_id.exists' => T("Course::validation.folder_save.unit_id.exists"),
            'title.required' => T("Course::validation.folder_save.title.required"),
            'description.string' => T("Course::validation.folder_save.description.string"),
            'sort_order.integer' => T("Course::validation.folder_save.sort_order.integer"),
            'sort_order.min' => T("Course::validation.folder_save.sort_order.min"),
            'children.array' => T("Course::validation.folder_save.children.array"),
            'children.*.id.integer' => T("Course::validation.folder_save.children.id.integer"),
            'children.*.id.exists' => T("Course::validation.folder_save.children.id.exists"),
            'children.*.title.required' => T("Course::validation.folder_save.children.title.required"),
            'children.*.title.string' => T("Course::validation.folder_save.children.title.string"),
            'children.*.title.max' => T("Course::validation.folder_save.children.title.max"),
            'children.*.description.string' => T("Course::validation.folder_save.children.description.string"),
            'children.*.sort_order.integer' => T("Course::validation.folder_save.children.sort_order.integer"),
            'children.*.sort_order.min' => T("Course::validation.folder_save.children.sort_order.min"),
            'children.*.resource_type.required' => T("Course::validation.folder_save.children.resource_type.required"),
            'children.*.resource_type.in' => T("Course::validation.folder_save.children.resource_type.in"),
            'children.*.file_url.required_if' => T("Course::validation.folder_save.children.file_url.required"),
            'children.*.file_url.string' => T("Course::validation.folder_save.children.file_url.string"),
            'children.*.file_url.max' => T("Course::validation.folder_save.children.file_url.max"),
            'children.*.file_type.required_if' => T("Course::validation.folder_save.children.file_type.required"),
            'children.*.file_type.string' => T("Course::validation.folder_save.children.file_type.string"),
            'children.*.file_type.max' => T("Course::validation.folder_save.children.file_type.max"),
            'children.*.children.array' => T("Course::validation.folder_save.children.children.array"),
            'children.*.children.required_if' => T("Course::validation.folder_save.children.children.required_if"),   
        ];
    }
} 