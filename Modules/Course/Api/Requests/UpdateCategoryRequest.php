<?php

namespace Modules\Course\Api\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // 可以根据实际需求调整授权逻辑
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'sometimes|required|string|max:100',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|integer',
            'sort_order' => 'nullable|integer',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => T('Course::base.category.validation.name.required'),
            'name.max' => T('Course::base.category.validation.name.max'),
            'parent_id.integer' => T('Course::base.category.validation.parent_id.integer'),
            'sort_order.integer' => T('Course::base.category.validation.sort_order.integer'),
        ];
    }
}
