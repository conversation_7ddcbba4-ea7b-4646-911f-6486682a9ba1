# Course 模块

## 邮件发送工具使用说明

Course模块提供了一个独立的邮件发送工具，不依赖其他模块。这个工具基于PHPMailer实现，支持SMTP发送邮件。

### 基本使用

```php
<?php

use Modules\Course\Domain\Mail\MailSender;

// 获取邮件发送器实例
$mailSender = app(MailSender::class);

// 发送简单邮件
$result = $mailSender->send(
    '<EMAIL>',
    '这是一封测试邮件',
    '<p>这是邮件内容，支持HTML格式</p>'
);

// 发送带附件的邮件
$result = $mailSender->send(
    '<EMAIL>',
    '这是一封带附件的测试邮件',
    '<p>这是邮件内容，带有附件</p>',
    [
        [
            'path' => storage_path('app/attachments/file.pdf'),
            'name' => '重命名的文件.pdf' // 可选，默认使用原文件名
        ]
    ]
);
```

### 邮件配置

系统使用`.env`文件中的环境变量进行邮件配置，请确保在`.env`文件中正确设置以下变量：

```
MAIL_MAILER=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=your_username
MAIL_PASSWORD=your_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Your Company Name"
```

这些配置将被系统自动加载，无需硬编码到代码中。

### 自定义使用

如果你需要更直接地使用邮件工具，可以手动创建和配置，建议从环境变量中读取配置：

```php
<?php

use Modules\Course\Domain\Mail\MailConfig;
use Modules\Course\Domain\Mail\MailSender;
use Modules\Course\Domain\Mail\LoggingMailLogger;

// 创建邮件配置（从环境变量获取）
$config = new MailConfig(
    env('MAIL_HOST', 'smtp.example.com'),
    env('MAIL_PORT', 587),
    env('MAIL_USERNAME', 'your_username'),
    env('MAIL_PASSWORD', 'your_password'),
    env('MAIL_ENCRYPTION', 'tls'),
    env('MAIL_FROM_ADDRESS', '<EMAIL>'),
    env('MAIL_FROM_NAME', 'Your Company Name')
);

// 创建日志记录器
$logger = new LoggingMailLogger();

// 创建邮件发送器
$mailSender = new MailSender($config, $logger);

// 发送邮件
$mailSender->send('<EMAIL>', '邮件主题', '邮件内容');
```

### 注意事项

1. 确保安装了`phpmailer/phpmailer`包
2. 邮件发送日志会记录在默认的`mail`日志频道中
3. 如果邮件发送失败，可以查看日志中的详细错误信息
4. 请勿在代码中硬编码邮件凭证，始终使用环境变量
5. 生产环境中请确保`.env`文件安全，不要将其添加到版本控制系统

## 令牌规则说明

Course模块使用Redis实现无状态令牌管理，以下是基于实际代码实现的令牌规则说明：

### 令牌生成规则

1. 令牌格式：使用MD5加密的UUID+时间戳+随机数生成
2. 令牌有效期：2小时（7200秒）
3. 令牌存储：使用Redis存储，key格式为`api_token:{token}`
4. 令牌包含信息：
   - 用户ID
   - 用户账号
   - 用户角色ID
   - 令牌过期时间
   - 创建时间
   - 设备信息
   - IP地址
   - 最后使用时间

### 令牌使用规则

1. 单点登录：同一用户同一时间只能有一个有效令牌
2. 令牌验证：所有需要认证的API请求必须携带令牌
3. 令牌格式：请求头中携带`Authorization: Bearer {token}`
4. 令牌验证流程：
   - 检查令牌是否存在
   - 检查令牌是否过期
   - 自动续期：如果令牌剩余有效期小于5分钟，将自动续期
   - 更新令牌最后使用时间

### 令牌手动续期

1. 续期接口：
   - 请求方式：POST
   - 接口路径：/api/course/user/refresh-token
   - 请求头：需要携带当前有效令牌
   - 响应格式：JSON

2. 续期条件：
   - 令牌必须有效且未过期
   - 令牌未被加入黑名单
   - 续期次数未超过限制（5次）
   - 续期间隔符合要求（最小间隔5分钟）

3. 续期流程：
   - 验证当前令牌有效性
   - 检查令牌刷新限制
   - 创建新令牌
   - 更新用户当前令牌
   - 记录续期历史
   - 旧令牌加入黑名单
   - 返回新令牌信息
   
### 令牌自动续期机制

1. 续期条件：
   - 环境变量`TOKEN_AUTO_REFRESH`设置为`true`
   - 令牌必须有效
   - 令牌剩余有效期小于5分钟
   - 令牌未被加入黑名单
   - 续期次数未超过限制（5次）
   - 续期间隔符合要求（最小间隔5分钟）
2. 续期流程：
   - 创建新令牌
   - 更新用户当前令牌
   - 记录续期历史
   - 旧令牌自动加入黑名单
3. 续期响应：
   - 新令牌通过响应头返回：`X-New-Token`
   - 新令牌过期时间通过响应头返回：`X-New-Token-Expired-At`

### 安全机制

1. 令牌黑名单：
   - 被吊销的令牌会被加入黑名单
   - 黑名单有效期与令牌有效期相同
   - 黑名单key格式：`token_refresh_blacklist:{token}`
2. 用户信息缓存：
   - 用户信息缓存在Redis中
   - 缓存key格式为`user_info:{user_id}`
   - 缓存时间24小时
3. 令牌刷新历史：
   - 记录最近5次令牌刷新历史
   - 历史记录key格式：`token_refresh_history:{user_id}`
   - 历史记录包含：旧令牌、新令牌、刷新时间、IP、User-Agent
   - 历史记录保存时间：令牌过期时间的2倍
4. 日志记录：
   - 记录令牌创建
   - 记录令牌自动续期
   - 记录令牌刷新
   - 记录令牌吊销
   - 记录异常情况

### 错误码说明

1. TOKEN_INVALID (10013)：令牌无效
2. TOKEN_REFRESH_FAILED (10014)：令牌刷新失败
3. USER_NOT_LOGIN (10006)：用户未登录
4. USER_LOGIN_EXPIRED (10009)：登录已过期
5. USER_LOGIN_KICKED (10010)：账号在其他设备登录 


### 忘记密码流程

1. 用户请求重置密码：
   - 通过账号或邮箱查找用户
   - 验证用户是否存在
   - 生成重置密码令牌
   - 发送重置密码邮件

2. 重置密码令牌：
   - 令牌有效期1小时
   - 令牌存储在Redis中
   - 令牌key格式：`reset_password_token:{token}`
   - 令牌数据包含：用户ID、邮箱、令牌类型、过期时间

3. 重置密码邮件：
   - 邮件主题：重置密码
   - 邮件内容：包含重置密码链接
   - 链接格式：`http://localhost:8000/reset-password?token={token}`
   - 邮件有效期：1小时

### 重置密码流程

1. 验证重置密码令牌：
   - 检查令牌是否存在
   - 验证令牌类型
   - 检查令牌是否过期
   - 验证用户是否存在

2. 重置密码：
   - 验证新密码和确认密码是否一致
   - 更新用户密码
   - 如果用户已登录，强制登出
   - 删除重置密码令牌

3. 安全措施：
   - 密码加密存储
   - 令牌一次性使用
   - 令牌使用后立即删除
   - 重置密码后强制登出

4. 错误处理：
   - TOKEN_NOT_FOUND (10015)：重置密码令牌未找到
   - TOKEN_TYPE_INVALID (10016)：令牌类型错误
   - TOKEN_EXPIRED (10017)：令牌已过期
   - USER_PASSWORD_NOT_MATCH (10018)：密码不匹配

### 使用示例
