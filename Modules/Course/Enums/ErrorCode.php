<?php

declare(strict_types=1);

namespace Modules\Course\Enums;


enum ErrorCode: int
{
    case USER_LOGIN_FAILED = 10001;
    case ROLE_NOT_FOUND = 10002;
    case USER_NOT_FOUND = 10003;
    case PERMISSION_NOT_FOUND = 10004;
    case PERMISSION_TREE_GET_FAILED = 10005;
    case USER_NOT_LOGIN = 10006;
    case USER_LOGOUT_FAILED = 10007;
    case USER_ALREADY_LOGIN = 10008;
    case USER_LOGIN_EXPIRED = 10009;
    case USER_LOGIN_KICKED = 10010;
    case USER_OLD_PASSWORD_INCORRECT = 10011;
    case USER_PASSWORD_NOT_MATCH = 10012;
    case TOKEN_INVALID = 10013;
    case TOKEN_REFRESH_FAILED = 10014;
    case USER_NO_PERMISSION = 10015;
    case USER_PREFERENCE_NOT_FOUND = 10016;
    case UPLOAD_FILE_FAILED = 10017;
    case ROLE_SAVE_FAILED = 10018;
    case SAVE_FAILED = 10019;
    case VIDEO_NOT_FOUND = 10020;
    case SUBMIT_TOO_FREQUENTLY = 10021;
    case VIDEO_SAVE_FAILED = 10022;
    case VIDEO_PROGRESS_EXCEPTION = 10023;
    case VIDEO_WATCHING_EXCEPTION = 10024;
    case VIDEO_PLAY_SESSION_EXPIRED = 10025;
    case VIDEO_PLAY_SESSION_NOT_MATCH = 10026;  
    case VIDEO_PLAY_SESSION_IP_CHANGE = 10027; // 播放会话ip改变
    case VIDEO_PLAY_SESSION_USER_AGENT_CHANGE = 10028; // 播放会话user_agent改变
    case VIDEO_PLAY_SESSION_REFERRER_CHANGE = 10029; // 播放会话referer改变
    case VIDEO_PLAY_SESSION_TIME_DIFF_TOO_LONG = 10030; // 播放会话时间差过大
    case VIDEO_PLAY_SESSION_TIME_DIFF_TOO_SHORT = 10031; // 播放会话时间差过短
    case FILE_INVALID = 10032; // 文件无效
    case FILE_TYPE_INVALID = 10033; // 文件类型无效
    case FILE_EMPTY = 10034; // 文件为空
    case COURSE_USER_IMPORT_NOT_FOUND = 10035; // 课程用户导入数据不存在
    case USER_NOT_ROLE = 10036; // 用户没有角色
    case PERMISSION_DENIED = 10037; // 没有操作权限
    case REFLECTION_NOT_FOUND = 10038; // 反思报告不存在
    case REFLECTION_FILE_NOT_FOUND = 10039; // 反思报告文件不存在
    case SYSTEM_ERROR = 10040; // 系统错误
    case RESOURCE_NOT_FOUND = 10041; // 资源不存在
    case TRY_REFRESH_NON_CURRENT_TOKEN = 10042; // 尝试刷新非当前令牌
    case TRY_REFRESH_BLACKLIST_TOKEN = 10043; // 尝试刷新黑名单中的令牌
    case TOKEN_REFRESH_TIMES_LIMIT = 10044; // 令牌刷新次数超过限制
    case TOKEN_REFRESH_INTERVAL_TOO_SHORT = 10045; // 令牌刷新间隔过短
    case TOKEN_EXPIRED = 10046; // 令牌已过期
    case TOKEN_TYPE_INVALID = 10047; // 令牌类型错误
    case TOKEN_NOT_FOUND = 10048; // 令牌不存在
    case SUBMISSION_NOT_FOUND = 10049; // 提交记录不存在
    case INVALID_PARAMS = 10050; // 参数错误
    case UNIT_NOT_FOUND = 10051; // 单元不存在
    case UNIT_UPDATE_FAILED = 10052; // 单元更新失败
    case FILE_SAVE_FAILED = 10053; // 文件保存失败
    case FILE_RESOURCE_NOT_FOUND = 10054; // 文件资源不存在
    case QUIZ_SAVE_FAILED = 10055; // 测验保存失败
    case REFLECTION_SAVE_FAILED = 10056; // 反思报告保存失败
    case INVALID_RESOURCE_TYPE = 10057; // 无效的资源类型
    case RESOURCE_DELETE_FAILED = 10058; // 资源删除失败
    case UNIT_SORT_FAILED = 10059; // 单元排序失败
    case VIDEO_SORT_FAILED = 10060; // 视频排序失败

    public function getMessage(): string
    {
        return T("Course::error.{$this->name}");
    }
}
