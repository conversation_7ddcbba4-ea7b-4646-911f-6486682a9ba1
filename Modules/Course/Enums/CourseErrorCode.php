<?php

declare(strict_types=1);

namespace Modules\Course\Enums;

use Bingo\Framework\Core\Util\T;

/**
 * 课程模块错误码枚举
 *
 * 错误码格式：18XXXX
 * 18: 课程模块编号
 * XXXX: 具体错误码
 */
enum CourseErrorCode: int
{
    /**
     * 通用错误码 (180001-180999)
     */
    case FAILED = 180001;
    case NOT_FOUND = 180002;
    case VALIDATION_FAILED = 180003;

    /**
     * 分类相关错误码 (181000-181999)
     */
    case CATEGORY_NOT_FOUND = 181000;
    case CATEGORY_ALREADY_DELETED = 181001;
    case CATEGORY_CREATE_FAILED = 181002;
    case CATEGORY_UPDATE_FAILED = 181003;
    case CATEGORY_DELETE_FAILED = 181004;
    case CATEGORY_SELF_PARENT = 181005;
    case CATEGORY_CHILD_PARENT = 181006;
    case CATEGORY_PARENT_NOT_FOUND = 181007;
    case CATEGORY_HAS_CHILDREN = 181008;
    case CATEGORY_HAS_COURSES = 181009;

    /**
     * 题目相关错误码 (182000-182999)
     */
    case QUESTION_NOT_FOUND = 182000;
    case QUESTION_CREATE_FAILED = 182001;
    case QUESTION_UPDATE_FAILED = 182002;
    case QUESTION_DELETE_FAILED = 182003;

    /**
     * 课程相关错误码 (183000-183999)
     */
    case COURSE_NOT_FOUND = 183000;
    case COURSE_CREATE_FAILED = 183001;
    case COURSE_UPDATE_FAILED = 183002;
    case COURSE_DELETE_FAILED = 183003;

    /**
     * FAQ相关错误码 (184000-184999)
     */
    case FAQ_NOT_FOUND = 184000;
    case FAQ_CREATE_FAILED = 184001;
    case FAQ_UPDATE_FAILED = 184002;
    case FAQ_DELETE_FAILED = 184003;

    /**
     * 网站信息相关错误码 (185000-185999)
     */
    case INFO_NOT_FOUND = 185000;
    case INFO_CREATE_FAILED = 185001;
    case INFO_UPDATE_FAILED = 185002;
    case INFO_DELETE_FAILED = 185003;
    case INVALID_PARAMS = 185004;

    /**
     * 获取错误信息
     *
     * @return string
     */
    public function message(): string
    {
        return match ($this) {
            // 通用错误码
            self::FAILED => T('Course::error.' . $this->value, '操作失败'),
            self::NOT_FOUND => T('Course::error.' . $this->value, '数据不存在'),
            self::VALIDATION_FAILED => T('Course::error.' . $this->value, '数据验证失败'),

            // 分类相关错误码
            self::CATEGORY_NOT_FOUND => T('Course::error.' . $this->value, '分类不存在'),
            self::CATEGORY_ALREADY_DELETED => T('Course::error.' . $this->value, '分类已经删除'),
            self::CATEGORY_CREATE_FAILED => T('Course::error.' . $this->value, '分类创建失败'),
            self::CATEGORY_UPDATE_FAILED => T('Course::error.' . $this->value, '分类更新失败'),
            self::CATEGORY_DELETE_FAILED => T('Course::error.' . $this->value, '分类删除失败'),
            self::CATEGORY_SELF_PARENT => T('Course::error.' . $this->value, '不能将分类自身设为父级分类'),
            self::CATEGORY_CHILD_PARENT => T('Course::error.' . $this->value, '不能将分类设为其子分类的子分类'),
            self::CATEGORY_PARENT_NOT_FOUND => T('Course::error.' . $this->value, '父级分类不存在'),
            self::CATEGORY_HAS_CHILDREN => T('Course::error.' . $this->value, '该分类下有子分类，无法删除'),
            self::CATEGORY_HAS_COURSES => T('Course::error.' . $this->value, '该分类下有课程，无法删除'),

            // 题目相关错误码
            self::QUESTION_NOT_FOUND => T('Course::error.' . $this->value, '题目不存在'),
            self::QUESTION_CREATE_FAILED => T('Course::error.' . $this->value, '题目创建失败'),
            self::QUESTION_UPDATE_FAILED => T('Course::error.' . $this->value, '题目更新失败'),
            self::QUESTION_DELETE_FAILED => T('Course::error.' . $this->value, '题目删除失败'),

            // 课程相关错误码
            self::COURSE_NOT_FOUND => T('Course::error.' . $this->value, '课程不存在'),
            self::COURSE_CREATE_FAILED => T('Course::error.' . $this->value, '课程创建失败'),
            self::COURSE_UPDATE_FAILED => T('Course::error.' . $this->value, '课程更新失败'),
            self::COURSE_DELETE_FAILED => T('Course::error.' . $this->value, '课程删除失败'),

            // FAQ相关错误码
            self::FAQ_NOT_FOUND => T('Course::error.' . $this->value, 'FAQ不存在'),
            self::FAQ_CREATE_FAILED => T('Course::error.' . $this->value, 'FAQ创建失败'),
            self::FAQ_UPDATE_FAILED => T('Course::error.' . $this->value, 'FAQ更新失败'),
            self::FAQ_DELETE_FAILED => T('Course::error.' . $this->value, 'FAQ删除失败'),

            // 网站信息相关错误码
            self::INFO_NOT_FOUND => T('Course::error.' . $this->value, '信息不存在'),
            self::INFO_CREATE_FAILED => T('Course::error.' . $this->value, '信息创建失败'),
            self::INFO_UPDATE_FAILED => T('Course::error.' . $this->value, '信息更新失败'),
            self::INFO_DELETE_FAILED => T('Course::error.' . $this->value, '信息删除失败'),
            self::INVALID_PARAMS => T('Course::error.' . $this->value, '参数无效'),
        };
    }

    /**
     * 获取 HTTP 状态码
     *
     * @return int
     */
    public function httpCode(): int
    {
        return match ($this) {
            self::VALIDATION_FAILED => 400,
            self::NOT_FOUND,
            self::CATEGORY_NOT_FOUND,
            self::QUESTION_NOT_FOUND,
            self::COURSE_NOT_FOUND,
            self::FAQ_NOT_FOUND,
            self::INFO_NOT_FOUND,
            self::CATEGORY_PARENT_NOT_FOUND => 404,
            self::CATEGORY_HAS_CHILDREN,
            self::CATEGORY_HAS_COURSES => 400,
            default => 400,
        };
    }
}
