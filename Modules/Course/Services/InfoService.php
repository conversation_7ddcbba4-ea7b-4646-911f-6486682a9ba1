<?php

namespace Modules\Course\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Course\Models\Info;
use Modules\Course\Models\Quiz;
use Modules\Course\Models\QuestionsAnswer;
use Modules\Course\Enums\CourseErrorCode;
use Bingo\Exceptions\BizException;
use Bingo\Framework\Core\Util\T;

class InfoService
{
    /**
     * 创建网站信息
     *
     * @param array $data 信息数据
     * @return array 创建的信息数据
     */
    public function createInfo(array $data): array
    {
        try {
            // 验证必填字段
            if (empty($data['title'])) {
                BizException::throws(CourseErrorCode::INVALID_PARAMS, T('Course::error.title_required', '标题不能为空'));
            }

            if (!isset($data['type']) || !in_array((int)$data['type'], [Info::TYPE_FAQ, Info::TYPE_ANNOUNCEMENT, Info::TYPE_CONTACT])) {
                BizException::throws(CourseErrorCode::INVALID_PARAMS, T('Course::error.type_invalid', '类型无效'));
            }

            // 准备数据
            $infoData = [
                'title' => $data['title'],
                'description' => $data['description'] ?? '',
                'type' => (int)$data['type'],
                'sort_order' => isset($data['sort_order']) ? (int)$data['sort_order'] : $this->getNextSortOrder((int)$data['type']),
                'creator_id' => Auth::user()->id,
                'created_at' => time(),
                'updated_at' => time(),
                'deleted_at' => 0
            ];

            // 创建信息
            $info = Info::create($infoData);

            // 返回创建的信息数据
            return $this->formatInfoData($info);

        } catch (\Exception $e) {
            Log::error('创建网站信息失败', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::INFO_CREATE_FAILED, '创建网站信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取网站信息列表
     *
     * @param array $params 查询参数
     * @return array 信息列表数据
     */
    public function getInfoList(array $params): array
    {
        try {
            $page = isset($params['current_page']) ? (int)$params['current_page'] : 1;
            $perPage = isset($params['per_page']) ? (int)$params['per_page'] : 50;
            $type = isset($params['type']) ? (int)$params['type'] : null;
            $keyword = $params['keyword'] ?? '';

            // 构建查询
            $query = Info::where('deleted_at', 0);

            // 如果指定了类型，则按类型筛选
            if ($type !== null) {
                $query->where('type', $type);
            }

            // 如果有关键字，则搜索标题和描述
            if (!empty($keyword)) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('title', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%");
                });
            }

            // 获取总数
            $total = $query->count();

            // 分页查询
            $infos = $query->orderBy('sort_order', 'asc')
                ->orderBy('id', 'desc')
                ->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get();

            // 格式化数据
            $items = [];
            foreach ($infos as $info) {
                $items[] = $this->formatInfoData($info);
            }

            // 返回结果
            return [
                'items' => $items,
                'pagination' => [
                    'total' => $total,
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'last_page' => ceil($total / $perPage)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取网站信息列表失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '获取网站信息列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取网站信息详情
     *
     * @param int $id 信息ID
     * @return array 信息详情数据
     */
    public function getInfoDetail(int $id): array
    {
        try {
            // 查询信息
            $info = Info::where('id', $id)
                ->where('deleted_at', 0)
                ->first();

            if (!$info) {
                BizException::throws(CourseErrorCode::INFO_NOT_FOUND, T('Course::error.info_not_found', '信息不存在'));
            }

            // 返回信息详情
            return $this->formatInfoData($info);

        } catch (\Exception $e) {
            Log::error('获取网站信息详情失败', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '获取网站信息详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 格式化信息数据
     *
     * @param Info $info 信息模型
     * @return array 格式化后的数据
     */
    private function formatInfoData(Info $info): array
    {
        // 处理 created_at 和 updated_at 字段，确保它们是 YYYY-MM-DD HH:MM:SS 格式
        $createdAt = $info->created_at;
        if ($createdAt instanceof \Carbon\Carbon || $createdAt instanceof \Illuminate\Support\Carbon) {
            $createdAt = $createdAt->format('Y-m-d H:i:s');
        } elseif (is_numeric($createdAt)) {
            $createdAt = date('Y-m-d H:i:s', $createdAt);
        }

        $updatedAt = $info->updated_at;
        if ($updatedAt instanceof \Carbon\Carbon || $updatedAt instanceof \Illuminate\Support\Carbon) {
            $updatedAt = $updatedAt->format('Y-m-d H:i:s');
        } elseif (is_numeric($updatedAt)) {
            $updatedAt = date('Y-m-d H:i:s', $updatedAt);
        }

        return [
            'id' => $info->id,
            'title' => $info->title,
            'description' => $info->description,
            'type' => $info->type,
            'type_name' => Info::getTypeName($info->type),
            'sort_order' => $info->sort_order,
            'creator_id' => $info->creator_id,
            'created_at' => $createdAt,
            'updated_at' => $updatedAt
        ];
    }



    /**
     * 获取下一个排序值
     *
     * @param int $type 信息类型
     * @return int 下一个排序值
     */
    private function getNextSortOrder(int $type): int
    {
        // 获取当前类型下的最大排序值
        $maxSortOrder = Info::where('type', $type)
            ->where('deleted_at', 0)
            ->max('sort_order');

        // 如果没有记录，则返回1，否则返回最大值+1
        return $maxSortOrder ? $maxSortOrder + 1 : 1;
    }

    /**
     * 获取学生学习详情
     *
     * 当提供student_id时，查询单个学生的学习情况
     * 当不提供student_id时，查询所有选课学生的学习情况
     *
     * @param array $params 查询参数
     * @return array 学习详情数据
     */
    public function getStudyDetail(array $params): array
    {
        try {
            $courseId = (int)$params['course_id'];

            // 检查是否提供了student_id
            $isSingleStudent = isset($params['student_id']) && !empty($params['student_id']);

            // 如果提供了student_id，则查询单个学生的学习情况
            if ($isSingleStudent) {
                $studentId = (int)$params['student_id'];
                return $this->getStudentDetail($courseId, $studentId);
            }

            // 如果没有提供student_id，则查询所有选课学生的学习情况
            return $this->getAllStudentsDetail($courseId);
        } catch (\Exception $e) {
            Log::error('获取学生学习详情失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '获取学生学习详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取单个学生的学习详情
     *
     * @param int $courseId 课程ID
     * @param int $studentId 学生ID
     * @return array 学习详情数据
     */
    private function getStudentDetail(int $courseId, int $studentId): array
    {
        // 查询学生信息
        $student = DB::table('user')
            ->where('id', $studentId)
            ->where('deleted_at', 0)
            ->select('id', 'first_name', 'last_name')
            ->first();

        // 如果学生不存在，使用默认值
        $studentName = $student ? $student->first_name . ' ' . $student->last_name : '未知学生';

        // 初始化结果数组，分为quiz、video和reflection三部分
        $result = [
            'quiz' => [],
            'video' => [],
            'reflection' => [],
            'student_id' => $studentId,
            'student_name' => $studentName
        ];

        // 使用单一查询获取所有数据，减少数据库查询次数

        // 1. 查询课程下的所有测验（使用索引提示）
        $quizzes = DB::table('quiz')
            ->where('course_id', $courseId)
            ->where('deleted_at', 0)
            ->select('id', 'course_id', 'unit_id', 'title', 'total_score')
            ->orderBy('unit_id', 'asc')
            ->get();

        $quizIds = $quizzes->pluck('id')->toArray();

        // 2. 一次性查询该学生的所有测验答卷
        $quizAnswers = [];
        if (!empty($quizIds)) {
            // 使用子查询获取每个测验的最高分
            $highestScores = DB::table(function($query) use ($studentId, $quizIds) {
                $query->from('questions_answer')
                    ->select('quiz_id', 'actual_score')
                    ->whereIn('quiz_id', $quizIds)
                    ->where('student_id', $studentId)
                    ->where('status', 2) // 已完成的答卷
                    ->where('deleted_at', 0)
                    ->orderBy('quiz_id')
                    ->orderBy('actual_score', 'desc');
            }, 'scores')
            ->select('quiz_id', DB::raw('MAX(actual_score) as highest_score'))
            ->groupBy('quiz_id')
            ->get();

            foreach ($highestScores as $score) {
                $quizAnswers[$score->quiz_id] = $score->highest_score;
            }
        }

        // 3. 查询课程下的所有视频资源
        $videos = DB::table('video_resource')
            ->where('course_id', $courseId)
            ->where('deleted_at', 0)
            ->select('id', 'course_id', 'unit_id', 'title')
            ->orderBy('unit_id', 'asc')
            ->orderBy('sort_order', 'asc')
            ->get();

        $videoIds = $videos->pluck('id')->toArray();

        // 4. 一次性查询该学生的所有视频观看记录
        $completedVideoIds = [];
        if (!empty($videoIds)) {
            $completedVideoIds = DB::table('student_video_watching')
                ->whereIn('video_id', $videoIds)
                ->where('student_id', $studentId)
                ->where('is_completed', 1)
                ->where('deleted_at', 0)
                ->pluck('video_id')
                ->toArray();
        }

        // 5. 查询课程下的所有反思报告
        $reflectionReports = DB::table('reflection_report')
            ->where('course_id', $courseId)
            ->where('deleted_at', 0)
            ->select('id', 'course_id', 'unit_id', 'title', 'total_score')
            ->orderBy('unit_id', 'asc')
            ->get();

        $reportIds = $reflectionReports->pluck('id')->toArray();

        // 6. 一次性查询该学生的所有反思报告提交记录
        $reflectionScores = [];
        if (!empty($reportIds)) {
            $submissions = DB::table('reflection_submission_record')
                ->whereIn('reflection_id', $reportIds)
                ->where('student_id', $studentId)
                ->where('overall_status', '>', 1) // 已评分的记录
                ->where('deleted_at', 0)
                ->select('reflection_id', 'score')
                ->get();

            foreach ($submissions as $submission) {
                $reflectionScores[$submission->reflection_id] = $submission->score;
            }
        }

        // 处理测验数据
        foreach ($quizzes as $quiz) {
            $result['quiz'][] = [
                'course_id' => $quiz->course_id,
                'unit_id' => $quiz->unit_id,
                'quiz_id' => $quiz->id,
                'title' => $quiz->title,
                'total_score' => $quiz->total_score,
                'student_id' => $studentId,
                'actual_score' => isset($quizAnswers[$quiz->id]) ? $quizAnswers[$quiz->id] : 0
            ];
        }

        // 处理视频数据
        foreach ($videos as $video) {
            $result['video'][] = [
                'course_id' => $video->course_id,
                'unit_id' => $video->unit_id,
                'video_id' => $video->id,
                'title' => $video->title,
                'total_score' => 1,
                'student_id' => $studentId,
                'actual_score' => in_array($video->id, $completedVideoIds) ? 1 : 0
            ];
        }

        // 处理反思报告数据
        foreach ($reflectionReports as $report) {
            $result['reflection'][] = [
                'course_id' => $report->course_id,
                'unit_id' => $report->unit_id,
                'reflection_id' => $report->id,
                'title' => $report->title,
                'total_score' => $report->total_score,
                'student_id' => $studentId,
                'actual_score' => isset($reflectionScores[$report->id]) ? $reflectionScores[$report->id] : 0
            ];
        }

        // 计算统计数据
        $totalActualScore = 0;
        $totalPossibleScore = 0;

        // 累加测验得分
        foreach ($result['quiz'] as $quiz) {
            $totalActualScore += $quiz['actual_score'];
            $totalPossibleScore += $quiz['total_score'];
        }

        // 累加视频得分
        foreach ($result['video'] as $video) {
            $totalActualScore += $video['actual_score'];
            $totalPossibleScore += $video['total_score'];
        }

        // 累加反思报告得分
        foreach ($result['reflection'] as $reflection) {
            $totalActualScore += $reflection['actual_score'];
            $totalPossibleScore += $reflection['total_score'];
        }

        // 添加统计数据到结果中
        $result['statistics'] = [
            'actual_score' => $totalActualScore,  // 实际得分总和
            'total_score' => $totalPossibleScore  // 总分总和
        ];

            return $result;
    }

    /**
     * 获取所有选课学生的学习详情
     *
     * @param int $courseId 课程ID
     * @return array 所有学生的学习详情数据
     */
    private function getAllStudentsDetail(int $courseId): array
    {
        // 查询选修该课程的所有学生
        $students = DB::table('course_user')
            ->where('course_user.course_id', $courseId)
            ->where('course_user.deleted_at', 0)
            ->join('user', 'course_user.user_id', '=', 'user.id')
            ->where('user.deleted_at', 0)
            ->select('user.id', 'user.first_name', 'user.last_name')
            ->get();

        if ($students->isEmpty()) {
            return [];
        }

        // 获取所有学生ID
        $studentIds = $students->pluck('id')->toArray();

        // 1. 一次性获取所有测验数据
        $quizzes = DB::table('quiz')
            ->where('course_id', $courseId)
            ->where('deleted_at', 0)
            ->select('id', 'course_id', 'unit_id', 'title', 'total_score')
            ->get();

        $quizIds = $quizzes->pluck('id')->toArray();

        // 2. 一次性获取所有学生的测验答卷数据
        $quizAnswers = [];
        if (!empty($quizIds)) {
            $answers = DB::table('questions_answer')
                ->whereIn('quiz_id', $quizIds)
                ->whereIn('student_id', $studentIds)
                ->where('status', 2) // 已完成的答卷
                ->where('deleted_at', 0)
                ->select('quiz_id', 'student_id', 'actual_score')
                ->orderBy('actual_score', 'desc')
                ->get();

            // 按学生ID和测验ID组织数据
            foreach ($answers as $answer) {
                $quizAnswers[$answer->student_id][$answer->quiz_id] = $answer->actual_score;
            }
        }

        // 3. 一次性获取所有视频资源
        $videos = DB::table('video_resource')
            ->where('course_id', $courseId)
            ->where('deleted_at', 0)
            ->select('id', 'course_id', 'unit_id', 'title')
            ->orderBy('unit_id', 'asc')
            ->orderBy('sort_order', 'asc')
            ->get();

        $videoIds = $videos->pluck('id')->toArray();

        // 4. 一次性获取所有学生的视频观看记录
        $videoWatching = [];
        if (!empty($videoIds)) {
            $watchRecords = DB::table('student_video_watching')
                ->whereIn('video_id', $videoIds)
                ->whereIn('student_id', $studentIds)
                ->where('is_completed', 1)
                ->where('deleted_at', 0)
                ->select('video_id', 'student_id')
                ->get();

            // 按学生ID和视频ID组织数据
            foreach ($watchRecords as $record) {
                $videoWatching[$record->student_id][$record->video_id] = true;
            }
        }

        // 5. 一次性获取所有反思报告
        $reflectionReports = DB::table('reflection_report')
            ->where('course_id', $courseId)
            ->where('deleted_at', 0)
            ->select('id', 'course_id', 'unit_id', 'title', 'total_score')
            ->orderBy('unit_id', 'asc')
            ->get();

        $reportIds = $reflectionReports->pluck('id')->toArray();

        // 6. 一次性获取所有学生的反思报告提交记录
        $reflectionSubmissions = [];
        if (!empty($reportIds)) {
            $submissions = DB::table('reflection_submission_record')
                ->whereIn('reflection_id', $reportIds)
                ->whereIn('student_id', $studentIds)
                ->where('overall_status', '>', 1) // 已评分的记录
                ->where('deleted_at', 0)
                ->select('reflection_id', 'student_id', 'score')
                ->get();

            // 按学生ID和报告ID组织数据
            foreach ($submissions as $submission) {
                $reflectionSubmissions[$submission->student_id][$submission->reflection_id] = $submission->score;
            }
        }

        // 为每个学生组装数据
        $result = [];
        foreach ($students as $student) {
            $studentId = $student->id;
            $studentName = $student->first_name . ' ' . $student->last_name;

            $studentData = [
                'quiz' => [],
                'video' => [],
                'reflection' => [],
                'student_id' => $studentId,
                'student_name' => $studentName
            ];

            // 处理测验数据
            foreach ($quizzes as $quiz) {
                $quizData = [
                    'course_id' => $quiz->course_id,
                    'unit_id' => $quiz->unit_id,
                    'quiz_id' => $quiz->id,
                    'title' => $quiz->title,
                    'total_score' => $quiz->total_score,
                    'student_id' => $studentId,
                    'actual_score' => isset($quizAnswers[$studentId][$quiz->id]) ? $quizAnswers[$studentId][$quiz->id] : 0
                ];

                $studentData['quiz'][] = $quizData;
            }

            // 处理视频数据
            foreach ($videos as $video) {
                $videoData = [
                    'course_id' => $video->course_id,
                    'unit_id' => $video->unit_id,
                    'video_id' => $video->id,
                    'title' => $video->title,
                    'total_score' => 1,
                    'student_id' => $studentId,
                    'actual_score' => isset($videoWatching[$studentId][$video->id]) ? 1 : 0
                ];

                $studentData['video'][] = $videoData;
            }

            // 处理反思报告数据
            foreach ($reflectionReports as $report) {
                $reportData = [
                    'course_id' => $report->course_id,
                    'unit_id' => $report->unit_id,
                    'reflection_id' => $report->id,
                    'title' => $report->title,
                    'total_score' => $report->total_score,
                    'student_id' => $studentId,
                    'actual_score' => isset($reflectionSubmissions[$studentId][$report->id]) ? $reflectionSubmissions[$studentId][$report->id] : 0
                ];

                $studentData['reflection'][] = $reportData;
            }

            // 计算统计数据
            $totalActualScore = 0;
            $totalPossibleScore = 0;

            foreach ($studentData['quiz'] as $quiz) {
                $totalActualScore += $quiz['actual_score'];
                $totalPossibleScore += $quiz['total_score'];
            }

            foreach ($studentData['video'] as $video) {
                $totalActualScore += $video['actual_score'];
                $totalPossibleScore += $video['total_score'];
            }

            foreach ($studentData['reflection'] as $reflection) {
                $totalActualScore += $reflection['actual_score'];
                $totalPossibleScore += $reflection['total_score'];
            }

            $studentData['statistics'] = [
                'actual_score' => $totalActualScore,
                'total_score' => $totalPossibleScore
            ];

            $result[] = $studentData;
        }

        return $result;
    }

    /**
     * 获取所有学生学习详情（支持按首字母筛选）
     *
     * @param array $params 查询参数
     * @return array 学习详情数据
     */
    public function getAllStudyDetail(array $params): array
    {
        try {
            $courseId = (int)$params['course_id'];
            $firstInitial = $params['additional_first_initial'] ?? null;
            $lastInitial = $params['additional_last_initial'] ?? null;

            // 查询选修该课程的学生
            $query = DB::table('course_user')
                ->where('course_user.course_id', $courseId)
                ->where('course_user.deleted_at', 0)
                ->join('user', 'course_user.user_id', '=', 'user.id')
                ->where('user.deleted_at', 0);

            // 应用首字母筛选
            if (!empty($firstInitial)) {
                $query->where('user.additional_first_initial', $firstInitial);
            }

            if (!empty($lastInitial)) {
                $query->where('user.additional_last_initial', $lastInitial);
            }

            // 获取符合条件的学生
            $students = $query->select('user.id', 'user.first_name', 'user.last_name')
                ->get();

            if ($students->isEmpty()) {
                return [];
            }

            // 获取所有学生ID
            $studentIds = $students->pluck('id')->toArray();

            // 1. 一次性获取所有测验数据
            $quizzes = DB::table('quiz')
                ->where('course_id', $courseId)
                ->where('deleted_at', 0)
                ->select('id', 'course_id', 'unit_id', 'title', 'total_score')
                ->get();

            $quizIds = $quizzes->pluck('id')->toArray();

            // 2. 一次性获取所有学生的测验答卷数据
            $quizAnswers = [];
            if (!empty($quizIds)) {
                $answers = DB::table('questions_answer')
                    ->whereIn('quiz_id', $quizIds)
                    ->whereIn('student_id', $studentIds)
                    ->where('status', 2) // 已完成的答卷
                    ->where('deleted_at', 0)
                    ->select('quiz_id', 'student_id', 'actual_score')
                    ->orderBy('actual_score', 'desc')
                    ->get();

                // 按学生ID和测验ID组织数据
                foreach ($answers as $answer) {
                    $quizAnswers[$answer->student_id][$answer->quiz_id] = $answer->actual_score;
                }
            }

            // 3. 一次性获取所有视频资源
            $videos = DB::table('video_resource')
                ->where('course_id', $courseId)
                ->where('deleted_at', 0)
                ->select('id', 'course_id', 'unit_id', 'title')
                ->orderBy('unit_id', 'asc')
                ->orderBy('sort_order', 'asc')
                ->get();

            $videoIds = $videos->pluck('id')->toArray();

            // 4. 一次性获取所有学生的视频观看记录
            $videoWatching = [];
            if (!empty($videoIds)) {
                $watchRecords = DB::table('student_video_watching')
                    ->whereIn('video_id', $videoIds)
                    ->whereIn('student_id', $studentIds)
                    ->where('is_completed', 1)
                    ->where('deleted_at', 0)
                    ->select('video_id', 'student_id')
                    ->get();

                // 按学生ID和视频ID组织数据
                foreach ($watchRecords as $record) {
                    $videoWatching[$record->student_id][$record->video_id] = true;
                }
            }

            // 5. 一次性获取所有反思报告
            $reflectionReports = DB::table('reflection_report')
                ->where('course_id', $courseId)
                ->where('deleted_at', 0)
                ->select('id', 'course_id', 'unit_id', 'title', 'total_score')
                ->orderBy('unit_id', 'asc')
                ->get();

            $reportIds = $reflectionReports->pluck('id')->toArray();

            // 6. 一次性获取所有学生的反思报告提交记录
            $reflectionSubmissions = [];
            if (!empty($reportIds)) {
                $submissions = DB::table('reflection_submission_record')
                    ->whereIn('reflection_id', $reportIds)
                    ->whereIn('student_id', $studentIds)
                    ->where('overall_status', '>', 1) // 已评分的记录
                    ->where('deleted_at', 0)
                    ->select('reflection_id', 'student_id', 'score')
                    ->get();

                // 按学生ID和报告ID组织数据
                foreach ($submissions as $submission) {
                    $reflectionSubmissions[$submission->student_id][$submission->reflection_id] = $submission->score;
                }
            }

            // 为每个学生组装数据
            $result = [];
            foreach ($students as $student) {
                $studentId = $student->id;
                $studentName = $student->first_name . ' ' . $student->last_name;

                $studentData = [
                    'quiz' => [],
                    'video' => [],
                    'reflection' => [],
                    'student_id' => $studentId,
                    'student_name' => $studentName
                ];

                // 处理测验数据
                foreach ($quizzes as $quiz) {
                    $quizData = [
                        'course_id' => $quiz->course_id,
                        'unit_id' => $quiz->unit_id,
                        'quiz_id' => $quiz->id,
                        'title' => $quiz->title,
                        'total_score' => $quiz->total_score,
                        'student_id' => $studentId,
                        'actual_score' => isset($quizAnswers[$studentId][$quiz->id]) ? $quizAnswers[$studentId][$quiz->id] : 0
                    ];

                    $studentData['quiz'][] = $quizData;
                }

                // 处理视频数据
                foreach ($videos as $video) {
                    $videoData = [
                        'course_id' => $video->course_id,
                        'unit_id' => $video->unit_id,
                        'video_id' => $video->id,
                        'title' => $video->title,
                        'total_score' => 1,
                        'student_id' => $studentId,
                        'actual_score' => isset($videoWatching[$studentId][$video->id]) ? 1 : 0
                    ];

                    $studentData['video'][] = $videoData;
                }

                // 处理反思报告数据
                foreach ($reflectionReports as $report) {
                    $reportData = [
                        'course_id' => $report->course_id,
                        'unit_id' => $report->unit_id,
                        'reflection_id' => $report->id,
                        'title' => $report->title,
                        'total_score' => $report->total_score,
                        'student_id' => $studentId,
                        'actual_score' => isset($reflectionSubmissions[$studentId][$report->id]) ? $reflectionSubmissions[$studentId][$report->id] : 0
                    ];

                    $studentData['reflection'][] = $reportData;
                }

                // 计算统计数据
                $totalActualScore = 0;
                $totalPossibleScore = 0;

                foreach ($studentData['quiz'] as $quiz) {
                    $totalActualScore += $quiz['actual_score'];
                    $totalPossibleScore += $quiz['total_score'];
                }

                foreach ($studentData['video'] as $video) {
                    $totalActualScore += $video['actual_score'];
                    $totalPossibleScore += $video['total_score'];
                }

                foreach ($studentData['reflection'] as $reflection) {
                    $totalActualScore += $reflection['actual_score'];
                    $totalPossibleScore += $reflection['total_score'];
                }

                $studentData['statistics'] = [
                    'actual_score' => $totalActualScore,
                    'total_score' => $totalPossibleScore
                ];

                $result[] = $studentData;
            }

            // 计算每个学习项目的平均分
            $averages = [
                'quiz' => [],
                'video' => [],
                'reflection' => []
            ];

            // 计算每个测验的平均分
            foreach ($quizzes as $quiz) {
                $quizId = $quiz->id;
                $totalScore = 0;
                $validStudentCount = 0;

                // 遍历所有学生，计算该测验的总分和有效学生数
                foreach ($result as $studentData) {
                    foreach ($studentData['quiz'] as $studentQuiz) {
                        if ($studentQuiz['quiz_id'] == $quizId && $studentQuiz['actual_score'] > 0) {
                            $totalScore += $studentQuiz['actual_score'];
                            $validStudentCount++;
                        }
                    }
                }

                // 计算平均分
                $averageScore = $validStudentCount > 0 ? round($totalScore / $validStudentCount, 2) : 0;

                $averages['quiz'][] = [
                    'id' => $quizId,
                    'title' => $quiz->title,
                    'total_score' => $quiz->total_score,
                    'actual_score' => $averageScore,
                    'valid_student_count' => $validStudentCount
                ];
            }

            // 计算每个视频的平均分
            foreach ($videos as $video) {
                $videoId = $video->id;
                $totalScore = 0;
                $validStudentCount = 0;

                // 遍历所有学生，计算该视频的总分和有效学生数
                foreach ($result as $studentData) {
                    foreach ($studentData['video'] as $studentVideo) {
                        if ($studentVideo['video_id'] == $videoId && $studentVideo['actual_score'] > 0) {
                            $totalScore += $studentVideo['actual_score'];
                            $validStudentCount++;
                        }
                    }
                }

                // 计算平均分
                $averageScore = $validStudentCount > 0 ? round($totalScore / $validStudentCount, 2) : 0;

                $averages['video'][] = [
                    'id' => $videoId,
                    'title' => $video->title,
                    'total_score' => 1,
                    'actual_score' => $averageScore,
                    'valid_student_count' => $validStudentCount
                ];
            }

            // 计算每个反思报告的平均分
            foreach ($reflectionReports as $report) {
                $reportId = $report->id;
                $totalScore = 0;
                $validStudentCount = 0;

                // 遍历所有学生，计算该反思报告的总分和有效学生数
                foreach ($result as $studentData) {
                    foreach ($studentData['reflection'] as $studentReflection) {
                        if ($studentReflection['reflection_id'] == $reportId && $studentReflection['actual_score'] > 0) {
                            $totalScore += $studentReflection['actual_score'];
                            $validStudentCount++;
                        }
                    }
                }

                // 计算平均分
                $averageScore = $validStudentCount > 0 ? round($totalScore / $validStudentCount, 2) : 0;

                $averages['reflection'][] = [
                    'id' => $reportId,
                    'title' => $report->title,
                    'total_score' => $report->total_score,
                    'actual_score' => $averageScore,
                    'valid_student_count' => $validStudentCount
                ];
            }

            // 计算总体平均分
            $totalActualScoreAll = 0;
            $totalPossibleScoreAll = 0;
            $validStudentCountAll = 0;

            foreach ($result as $studentData) {
                if ($studentData['statistics']['actual_score'] > 0) {
                    $totalActualScoreAll += $studentData['statistics']['actual_score'];
                    $totalPossibleScoreAll += $studentData['statistics']['total_score'];
                    $validStudentCountAll++;
                }
            }

            $overallAverageScore = $validStudentCountAll > 0 ? round($totalActualScoreAll / $validStudentCountAll, 2) : 0;

            $averages['overall'] = [
                'actual_score' => $overallAverageScore,
                'total_score' => $validStudentCountAll > 0 ? round($totalPossibleScoreAll / $validStudentCountAll, 2) : 0,
                'valid_student_count' => $validStudentCountAll
            ];

            // 将学生数据和平均分数据合并返回
            return array_merge($result, [$averages]);

        } catch (\Exception $e) {
            Log::error('获取学生学习详情失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '获取学生学习详情失败: ' . $e->getMessage());
        }
    }
}
