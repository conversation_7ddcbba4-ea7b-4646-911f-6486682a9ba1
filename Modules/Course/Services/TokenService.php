<?php

namespace Modules\Course\Services;

use Exception;
use Illuminate\Support\Str;
use Modules\Course\Models\User;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\Log;
use Modules\Course\Enums\ErrorCode;
use Illuminate\Support\Facades\Redis;

/**
 * Token服务类
 * 
 * 管理用户的授权令牌，使用Redis实现无状态token管理
 */
class TokenService
{ 
    /**
     * Redis缓存前缀
     */
    const CACHE_PREFIX = 'api_token:';
    
    /**
     * 用户信息缓存前缀
     */
    const USER_INFO_PREFIX = 'user_info:';

    /**
     * 令牌刷新历史前缀
     */
    const TOKEN_REFRESH_HISTORY_PREFIX = 'token_refresh_history:';

    /**
     * 令牌刷新黑名单前缀
     */
    const TOKEN_REFRESH_BLACKLIST_PREFIX = 'token_refresh_blacklist:';

    /**
     * 最大刷新次数
     */
    const MAX_REFRESH_TIMES = 5;

    /**
     * 最小刷新间隔（秒）
     */
    const MIN_REFRESH_INTERVAL = 300; // 5分钟

    /**
     * 创建新令牌
     *
     * @param User $user 用户对象
     * @param array $options 选项数组 ['device' => '设备名称', 'ip' => '登录IP']
     * @return array 令牌信息
     */
    public function createToken(User $user, array $options = []): array
    {
        // 先删除用户现有的令牌（确保用户只有一个活跃令牌）
        $this->revokeUserToken($user->id);
        
        // 生成令牌
        $token = $this->generateToken();
        
        // 设置令牌过期时间
        $tokenExpiredAt = now()->addSeconds(User::TOKEN_EXPIRED_TIME);
        
        // 准备要存储的数据
        $tokenData = [
            'id' => $user->id,
            'account' => $user->account,
            'role_id' => $user->role_id,
            'token' => $token,
            'token_expired_at' => $tokenExpiredAt->toDateTimeString(),
            'created_at' => now()->toDateTimeString(),
            'device' => $options['device'] ?? 'unknown',
            'ip' => $options['ip'] ?? request()->ip(),
        ];
        
        // 存入Redis，设置过期时间
        $cacheKey = self::CACHE_PREFIX . $token;
        Redis::setex($cacheKey, User::TOKEN_EXPIRED_TIME, json_encode($tokenData));
        
        // 记录用户当前的令牌
        $this->saveUserCurrentToken($user->id, $token);
        
        // 缓存用户信息
        $this->cacheUserInfo($user);
        
        // 记录日志
        Log::info('用户登录成功，创建令牌', [
            'user_id' => $user->id,
            'token' => $token,
            'token_expired_at' => $tokenExpiredAt->toDateTimeString(),
            'device' => $options['device'] ?? 'unknown',
            'ip' => $options['ip'] ?? request()->ip(),
        ]);
        
        return [
            'token' => $token,
            'token_expired_at' => $tokenExpiredAt->toDateTimeString(),
            'expires_in' => User::TOKEN_EXPIRED_TIME
        ];
    }
    
    /**
     * 验证令牌有效性
     * 
     * @param string $token 令牌
     * @return array|null 验证通过返回用户信息，否则返回null
     */
    public function validateToken(string $token): ?array
    {
        $cacheKey = self::CACHE_PREFIX . $token;
        $tokenData = Redis::get($cacheKey);
        
        if (!$tokenData) {
            Log::info('令牌未找到', ['token' => $token]);
            return null;
        }
        
        $tokenData = json_decode($tokenData, true);
        
        // 检查令牌是否过期
        if (isset($tokenData['token_expired_at']) && now()->gt($tokenData['token_expired_at'])) {
            // 删除过期令牌
            $this->revokeToken($token);
            
            Log::warning('令牌已过期', [
                'user_id' => $tokenData['id'] ?? null,
                'token' => $token,
                'token_expired_at' => $tokenData['token_expired_at'],
                'current_time' => now()->toDateTimeString()
            ]);
            
            return null;
        }
        
        // 更新最后使用时间
        $tokenData['last_used_at'] = now()->toDateTimeString();
        Redis::setex($cacheKey, User::TOKEN_EXPIRED_TIME, json_encode($tokenData));
        
        return $tokenData;
    }
    
    /**
     * 获取令牌对应的用户ID
     * 
     * @param string $token
     * @return int|null
     */
    public function getUserIdFromToken(string $token): ?int
    {
        $tokenData = $this->validateToken($token);
        return $tokenData ? $tokenData['id'] : null;
    }
    
    /**
     * 吊销令牌
     * 
     * @param string $token
     * @return bool
     */
    public function revokeToken(string $token): bool
    {
        $cacheKey = self::CACHE_PREFIX . $token;
        $result = Redis::del($cacheKey);
        
        Log::info('令牌已吊销', ['token' => $token, 'result' => $result]);
        
        return $result > 0;
    }
    
    
    /**
     * 缓存用户信息
     * 
     * @param User $user
     * @return bool
     */
    public function cacheUserInfo(User $user): bool
    {
        $cacheKey = self::USER_INFO_PREFIX . $user->id;
        
        $userData = [
            'id' => $user->id,
            'account' => $user->account,
            'role_id' => $user->role_id,
            'email' => $user->email,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            // 其他需要的用户信息
            'cached_at' => now()->toDateTimeString()
        ];
        
        // 缓存24小时
        Redis::setex($cacheKey, 86400, json_encode($userData));
        
        return true;
    }
    
    /**
     * 获取缓存的用户信息
     * 
     * @param int $userId
     * @return array|null
     */
    public function getCachedUserInfo(int $userId): ?array
    {
        $cacheKey = self::USER_INFO_PREFIX . $userId;
        $userData = Redis::get($cacheKey);
        
        return $userData ? json_decode($userData, true) : null;
    }
    
    /**
     * 刷新令牌
     * 
     * @param string $oldToken 旧令牌
     * @return array|null 新令牌信息或null（如果旧令牌无效）
     * @throws BizException 当令牌无效或刷新条件不满足时抛出异常
     */
    public function refreshToken(string $oldToken): ?array
    {
        // 验证旧令牌并获取数据
        $tokenData = $this->validateToken($oldToken);
        if (!$tokenData) {
            BizException::throws(ErrorCode::TOKEN_INVALID, ErrorCode::TOKEN_INVALID->getMessage());
        }
        
        // 获取用户ID
        $userId = $tokenData['id'];
        
        // 验证令牌有效性
        $this->validateTokenForRefresh($userId, $oldToken);
        
        // 创建新令牌
        $newToken = $this->generateToken();
        $tokenExpiredAt = now()->addSeconds(User::TOKEN_EXPIRED_TIME);
        
        // 构建新令牌数据
        $newTokenData = $this->buildNewTokenData($tokenData, $newToken, $tokenExpiredAt, $oldToken);
        
        // 存储新令牌
        $this->storeNewToken($userId, $newToken, $newTokenData);
        
        // 记录刷新历史
        $this->recordRefreshHistory($userId, $oldToken, $newToken);
        
        // 记录日志
        Log::info('令牌已刷新', [
            'user_id' => $userId,
            'old_token' => $oldToken,
            'new_token' => $newToken
        ]);

        // 返回新令牌信息
        return [
            'token' => $newToken,
            'token_expired_at' => $tokenExpiredAt->toDateTimeString(),
            'expires_in' => User::TOKEN_EXPIRED_TIME
        ];
    }

    /**
     * 验证令牌是否可以刷新
     * 
     * @param int $userId 用户ID
     * @param string $oldToken 旧令牌
     * @return void
     * @throws BizException 当验证失败时抛出异常
     */
    private function validateTokenForRefresh(int $userId, string $oldToken): void
    {
        // 验证此令牌是否为用户当前的有效令牌
        $currentToken = $this->getUserCurrentToken($userId);
        if ($currentToken !== $oldToken) {
            Log::warning('尝试刷新非当前令牌', [
                'user_id' => $userId,
                'current_token' => $currentToken,
                'provided_token' => $oldToken
            ]);
            BizException::throws(ErrorCode::TRY_REFRESH_NON_CURRENT_TOKEN, ErrorCode::TRY_REFRESH_NON_CURRENT_TOKEN->getMessage());
        }

        // 检查令牌是否在黑名单中
        if ($this->isTokenInBlacklist($oldToken)) {
            Log::warning('尝试刷新黑名单中的令牌', [
                'user_id' => $userId,
                'token' => $oldToken
            ]);
            BizException::throws(ErrorCode::TRY_REFRESH_BLACKLIST_TOKEN, ErrorCode::TRY_REFRESH_BLACKLIST_TOKEN->getMessage());
        }

        // 检查刷新次数是否超过限制
        $this->checkRefreshLimits($userId);
    }

    /**
     * 检查令牌刷新限制
     * 
     * @param int $userId 用户ID
     * @return void
     * @throws BizException 当超过刷新限制时抛出异常
     */
    private function checkRefreshLimits(int $userId): void
    {
        $refreshHistory = $this->getRefreshHistory($userId);
        
        // 检查刷新次数是否超过限制
        if (count($refreshHistory) >= self::MAX_REFRESH_TIMES) {
            Log::warning('令牌刷新次数超过限制', [
                'user_id' => $userId,
                'refresh_count' => count($refreshHistory)
            ]);
            BizException::throws(ErrorCode::TOKEN_REFRESH_TIMES_LIMIT, ErrorCode::TOKEN_REFRESH_TIMES_LIMIT->getMessage());
        }

        // 检查刷新间隔
        if (!empty($refreshHistory)) {
            $lastRefreshTime = strtotime(end($refreshHistory)['refreshed_at']);
            if (time() - $lastRefreshTime < self::MIN_REFRESH_INTERVAL) {
                Log::warning('令牌刷新间隔过短', [
                    'user_id' => $userId,
                    'last_refresh_time' => end($refreshHistory)['refreshed_at'],
                    'current_time' => now()->toDateTimeString()
                ]);
                BizException::throws(ErrorCode::TOKEN_REFRESH_INTERVAL_TOO_SHORT, ErrorCode::TOKEN_REFRESH_INTERVAL_TOO_SHORT->getMessage());
            }
        }
    }

    /**
     * 构建新令牌数据
     * 
     * @param array $tokenData 旧令牌数据
     * @param string $newToken 新令牌
     * @param \Illuminate\Support\Carbon $tokenExpiredAt 过期时间
     * @param string $oldToken 旧令牌
     * @return array 新令牌数据
     */
    private function buildNewTokenData(array $tokenData, string $newToken, $tokenExpiredAt, string $oldToken): array
    {
        return array_merge($tokenData, [
            'token' => $newToken,
            'token_expired_at' => $tokenExpiredAt->toDateTimeString(),
            'refreshed_at' => now()->toDateTimeString(),
            'refreshed_from' => $oldToken
        ]);
    }

    /**
     * 存储新令牌
     * 
     * @param int $userId 用户ID
     * @param string $newToken 新令牌
     * @param array $newTokenData 新令牌数据
     * @return void
     */
    private function storeNewToken(int $userId, string $newToken, array $newTokenData): void
    {
        // 存入Redis
        $cacheKey = self::CACHE_PREFIX . $newToken;
        Redis::setex($cacheKey, User::TOKEN_EXPIRED_TIME, json_encode($newTokenData));
        
        // 更新用户当前令牌
        $this->saveUserCurrentToken($userId, $newToken);
    }

    /**
     * 记录令牌刷新历史
     * 
     * @param int $userId 用户ID
     * @param string $oldToken 旧令牌
     * @param string $newToken 新令牌
     * @return void
     */
    private function recordRefreshHistory(int $userId, string $oldToken, string $newToken): void
    {
        $historyKey = self::TOKEN_REFRESH_HISTORY_PREFIX . $userId;
        $history = Redis::get($historyKey);
        $history = $history ? json_decode($history, true) : [];

        // 添加新的刷新记录
        $history[] = [
            'old_token' => $oldToken,
            'new_token' => $newToken,
            'refreshed_at' => now()->toDateTimeString(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ];

        // 只保留最近5次刷新记录
        if (count($history) > self::MAX_REFRESH_TIMES) {
            $history = array_slice($history, -self::MAX_REFRESH_TIMES);
        }

        // 保存刷新历史，设置过期时间为令牌过期时间的2倍
        Redis::setex($historyKey, User::TOKEN_EXPIRED_TIME * 2, json_encode($history));
    }

    /**
     * 获取令牌刷新历史
     * 
     * @param int $userId 用户ID
     * @return array 刷新历史记录
     */
    private function getRefreshHistory(int $userId): array
    {
        $historyKey = self::TOKEN_REFRESH_HISTORY_PREFIX . $userId;
        $history = Redis::get($historyKey);
        return $history ? json_decode($history, true) : [];
    }

    /**
     * 将令牌加入黑名单
     * 
     * @param string $token 令牌
     * @return void
     */
    private function addTokenToBlacklist(string $token): void
    {
        $blacklistKey = self::TOKEN_REFRESH_BLACKLIST_PREFIX . $token;
        Redis::setex($blacklistKey, User::TOKEN_EXPIRED_TIME, 1);
    }

    /**
     * 检查令牌是否在黑名单中
     * 
     * @param string $token 令牌
     * @return bool
     */
    private function isTokenInBlacklist(string $token): bool
    {
        $blacklistKey = self::TOKEN_REFRESH_BLACKLIST_PREFIX . $token;
        return Redis::exists($blacklistKey);
    }

    /**
     * 生成令牌
     * 
     * @return string
     */
    private function generateToken(): string
    {
        return md5(Str::uuid() . uniqid() . time() . rand(1000, 9999));
    }

    /**
     * 保存用户当前令牌
     * 
     * @param int $userId 用户ID
     * @param string $token 令牌
     * @return bool 操作结果
     */
    private function saveUserCurrentToken(int $userId, string $token): bool
    {
        $key = 'user_current_token:' . $userId;
        return Redis::set($key, $token) ? true : false;
    }

    /**
     * 获取用户当前令牌
     * 
     * @param int $userId 用户ID
     * @return string|null 当前令牌或null
     */
    public function getUserCurrentToken(int $userId): ?string
    {
        $key = 'user_current_token:' . $userId;
        $token = Redis::get($key);
        return $token ?: null;
    }

    /**
     * 吊销用户的令牌
     * 
     * @param int $userId 用户ID
     * @return bool 操作结果
     */
    public function revokeUserToken(int $userId): bool
    {
        try {
            // 获取用户当前令牌
            $token = $this->getUserCurrentToken($userId);
            if (!$token) {
                // 用户没有活跃令牌
                return true;
            }
            
            // 删除令牌
            $cacheKey = self::CACHE_PREFIX . $token;
            $result = Redis::del($cacheKey);
            
            // 删除用户令牌映射
            $userTokenKey = 'user_current_token:' . $userId;
            Redis::del($userTokenKey);
            
            Log::info('用户令牌已吊销', [
                'user_id' => $userId,
                'token' => $token,
                'result' => $result
            ]);
            
            return $result > 0;
        } catch (Exception $e) {
            Log::error('吊销用户令牌失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 创建重置密码令牌
     * 
     * @param User $user 用户对象
     * @return string 重置密码令牌
     */
    public function createResetPasswordToken(User $user): string
    {
        // 生成重置密码令牌
        $token = $this->generateToken();
        
        // 设置令牌过期时间（1小时）
        $expiredAt = now()->addHour();
        
        // 准备要存储的数据
        $tokenData = [
            'id' => $user->id,
            'email' => $user->email,
            'token' => $token,
            'token_expired_at' => $expiredAt->toDateTimeString(),
            'created_at' => now()->toDateTimeString(),
            'type' => 'reset_password'
        ];
        
        // 存入Redis，设置过期时间为1小时
        $cacheKey = 'reset_password_token:' . $token;
        Redis::setex($cacheKey, 3600, json_encode($tokenData));
        
        // 记录日志
        Log::info('创建重置密码令牌', [
            'user_id' => $user->id,
            'email' => $user->email,
            'token' => $token,
            'expired_at' => $expiredAt->toDateTimeString()
        ]);
        
        return $token;
    }

    /**
     * 验证重置密码令牌
     * 
     * @param string $token 重置密码令牌
     * @return array|null 验证通过返回用户信息，否则返回null
     * @throws BizException 当令牌无效时抛出异常
     */
    public function validateResetPasswordToken(string $token): ?array
    {
        // 获取令牌数据
        $cacheKey = 'reset_password_token:' . $token;
        $tokenData = Redis::get($cacheKey);
        
        if (!$tokenData) {
            Log::warning('重置密码令牌未找到', ['token' => $token]);
            BizException::throws(ErrorCode::TOKEN_NOT_FOUND, ErrorCode::TOKEN_NOT_FOUND->getMessage());
        }
        
        $tokenData = json_decode($tokenData, true);
        
        // 验证令牌类型
        if (!isset($tokenData['type']) || $tokenData['type'] !== 'reset_password') {
            Log::warning('令牌类型错误', [
                'token' => $token,
                'type' => $tokenData['type'] ?? 'unknown'
            ]);
            BizException::throws(ErrorCode::TOKEN_TYPE_INVALID, ErrorCode::TOKEN_TYPE_INVALID->getMessage());
        }
        
        // 检查令牌是否过期
        if (isset($tokenData['token_expired_at']) && now()->gt($tokenData['token_expired_at'])) {
            // 删除过期令牌
            Redis::del($cacheKey);
            
            Log::warning('重置密码令牌已过期', [
                'user_id' => $tokenData['id'] ?? null,
                'token' => $token,
                'token_expired_at' => $tokenData['token_expired_at'],
                'current_time' => now()->toDateTimeString()
            ]);
            
            BizException::throws(ErrorCode::TOKEN_EXPIRED, ErrorCode::TOKEN_EXPIRED->getMessage());
        }
        
        // 记录日志
        Log::info('重置密码令牌验证成功', [
            'user_id' => $tokenData['id'],
            'email' => $tokenData['email'],
            'token' => $token
        ]);
        
        return $tokenData;
    }

    /**
     * 使用重置密码令牌后删除令牌
     * 
     * @param string $token 重置密码令牌
     * @return bool 操作结果
     */
    public function revokeResetPasswordToken(string $token): bool
    {
        $cacheKey = 'reset_password_token:' . $token;
        $result = Redis::del($cacheKey);
        
        Log::info('重置密码令牌已使用并删除', [
            'token' => $token,
            'result' => $result
        ]);
        
        return $result > 0;
    }
} 