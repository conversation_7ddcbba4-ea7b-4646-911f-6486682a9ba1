<?php

namespace Modules\Course\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\Course\Models\Course;
use Modules\Course\Models\CourseUser;
use Modules\Course\Enums\CourseErrorCode;
use Bingo\Exceptions\BizException;
use Bingo\Framework\Core\Util\T;

class CourseUserService
{
    /**
     * 获取当前用户参与的课程列表
     *
     * @param array $params 查询参数
     * @return array 课程列表数据
     */
    public function getUserCourseList(array $params): array
    {
        try {
            // 使用数组解构和默认值简化参数获取
            $page = (int)($params['page'] ?? 1);
            $perPage = (int)($params['per_page'] ?? 10);
            $keyword = $params['keyword'] ?? '';

            // 获取当前用户
            $user = Auth::user();

            // 使用 Eloquent 关系查询构建器
            $query = $user->courses()
                ->when(!empty($keyword), function ($q) use ($keyword) {
                    // 使用 when 方法简化条件查询
                    return $q->where(function ($subQuery) use ($keyword) {
                        $subQuery->where('course.name', 'like', "%{$keyword}%")
                            ->orWhere('course.short_name', 'like', "%{$keyword}%")
                            ->orWhere('course.description', 'like', "%{$keyword}%");
                    });
                })
                ->orderBy('course.sort_order', 'asc')
                ->orderBy('course.id', 'desc');

            // 使用 Eloquent 的分页功能
            $courses = $query->paginate($perPage, ['*'], 'page', $page);

            // 使用集合的 map 方法优雅地格式化数据
            $items = $courses->map(function ($course) {
                return $this->formatCourseData($course);
            })->values()->all();

            // 返回标准化的分页结果
            return [
                'items' => $items,
                'pagination' => [
                    'total' => $courses->total(),
                    'current_page' => $courses->currentPage(),
                    'per_page' => $courses->perPage(),
                    'last_page' => $courses->lastPage()
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取用户课程列表失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '获取用户课程列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 格式化课程数据
     *
     * @param Course $course 课程模型
     * @return array 格式化后的数据
     */
    private function formatCourseData(Course $course): array
    {
        // 处理 created_at 和 updated_at 字段，确保它们是 YYYY-MM-DD HH:MM:SS 格式
        $createdAt = $course->created_at;
        if ($createdAt instanceof \Carbon\Carbon || $createdAt instanceof \Illuminate\Support\Carbon) {
            $createdAt = $createdAt->format('Y-m-d H:i:s');
        } elseif (is_numeric($createdAt)) {
            $createdAt = date('Y-m-d H:i:s', $createdAt);
        }

        $updatedAt = $course->updated_at;
        if ($updatedAt instanceof \Carbon\Carbon || $updatedAt instanceof \Illuminate\Support\Carbon) {
            $updatedAt = $updatedAt->format('Y-m-d H:i:s');
        } elseif (is_numeric($updatedAt)) {
            $updatedAt = date('Y-m-d H:i:s', $updatedAt);
        }

        // 格式化开始日期和结束日期
        $startDate = $course->start_date;
        if ($startDate instanceof \Carbon\Carbon || $startDate instanceof \Illuminate\Support\Carbon) {
            $startDate = $startDate->format('Y-m-d');
        }

        $endDate = $course->end_date;
        if ($endDate instanceof \Carbon\Carbon || $endDate instanceof \Illuminate\Support\Carbon) {
            $endDate = $endDate->format('Y-m-d');
        }

        // 格式化反思报告截止日期
        $reportDeadline = $course->report_deadline;
        if ($reportDeadline instanceof \Carbon\Carbon || $reportDeadline instanceof \Illuminate\Support\Carbon) {
            $reportDeadline = $reportDeadline->format('Y-m-d');
        }

        return [
            'id' => $course->id,
            'name' => $course->name,
            'short_name' => $course->short_name,
            'category_id' => $course->category_id,
            'status' => $course->status,
            'is_show' => $course->is_show,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'course_id' => $course->course_id,
            'is_user' => $course->is_user,
            'roles' => $course->roles,
            'image_url' => $course->image_url,
            'description' => $course->description,
            'report_deadline' => $reportDeadline,
            'max_students' => $course->max_students,
            'sort_order' => $course->sort_order,
            'is_active' => $course->is_active,
            'creator_id' => $course->creator_id,
            'created_at' => $createdAt,
            'updated_at' => $updatedAt
        ];
    }
}
