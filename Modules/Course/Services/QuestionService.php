<?php

namespace Modules\Course\Services;

use Modules\Course\Models\QuestionBank;
use Modules\Course\Enums\CourseErrorCode;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class QuestionService
{
    protected $repository;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->repository = new \Modules\Course\Domain\Repositories\QuestionRepository();
    }
    /**
     * 添加题目到题库
     *
     * @param array $data 题目数据
     * @return array 处理结果
     */
    public function addQuestion(array $data)
    {
        try {
            DB::beginTransaction();

            // 准备题目数据
            $questionData = [
                'category_id' => $data['category_id'],
                'question_point' => $data['question_point'],
                'question_detail' => $data['question_detail'],
                'score' => $data['score'],
                'question_feedback' => $data['question_feedback'] ?? '',
                'question_no' => $data['question_no'] ?? 'Q' . time() . rand(1000, 9999), // 如果没有提供编号，则生成一个
                'question_type' => $data['question_type'],
                'is_range' => $data['is_range'] ?? 1, // 默认不随机排序
                'creator_id' => $data['creator_id'] ?? 1, // 如果有用户信息则使用，否则默认为1
            ];

            // 处理 answer 字段（JSON字符串）
            if (isset($data['answer'])) {
                if (is_string($data['answer'])) {
                    // 如果是字符串，尝试解析为JSON
                    $questionData['answer'] = $data['answer'];
                } else if (is_array($data['answer'])) {
                    // 如果已经是数组，转换为JSON字符串
                    $questionData['answer'] = json_encode($data['answer'], JSON_UNESCAPED_UNICODE);
                }
            } else if (isset($data['answer_decoded']) && is_array($data['answer_decoded'])) {
                // 使用已解码的数据
                $questionData['answer'] = json_encode($data['answer_decoded'], JSON_UNESCAPED_UNICODE);
            }

            // 处理 answer_feedback 字段（JSON字符串）
            if (isset($data['answer_feedback'])) {
                if (is_string($data['answer_feedback'])) {
                    // 如果是字符串，尝试解析为JSON
                    $questionData['answer_feedback'] = $data['answer_feedback'];
                } else if (is_array($data['answer_feedback'])) {
                    // 如果已经是数组，转换为JSON字符串
                    $questionData['answer_feedback'] = json_encode($data['answer_feedback'], JSON_UNESCAPED_UNICODE);
                }
            } else if (isset($data['answer_feedback_decoded']) && is_array($data['answer_feedback_decoded'])) {
                // 使用已解码的数据
                $questionData['answer_feedback'] = json_encode($data['answer_feedback_decoded'], JSON_UNESCAPED_UNICODE);
            }

            // 保存题目
            $question = $this->repository->createQuestion($questionData);

            DB::commit();

            return [
                'items' => [
                    'id' => $question->id,
                    'question_no' => $question->question_no
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('添加题目失败', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            BizException::throws(CourseErrorCode::QUESTION_CREATE_FAILED, $e->getMessage());
        }
    }

    /**
     * 更新题目
     *
     * @param int $id 题目ID
     * @param array $data 题目数据
     * @return array 处理结果
     */
    public function updateQuestion(int $id, array $data)
    {
        try {
            DB::beginTransaction();

            // 检查题目是否存在
            $question = $this->repository->getQuestionById($id);
            if (!$question) {
                BizException::throws(CourseErrorCode::QUESTION_NOT_FOUND);
            }

            // 准备更新数据
            $updateData = [];

            // 更新分类ID
            if (isset($data['category_id'])) {
                $updateData['category_id'] = $data['category_id'];
            }

            // 更新概念/指标能力
            if (isset($data['question_point'])) {
                $updateData['question_point'] = $data['question_point'];
            }

            // 更新试题文字
            if (isset($data['question_detail'])) {
                $updateData['question_detail'] = $data['question_detail'];
            }

            // 更新分值
            if (isset($data['score'])) {
                $updateData['score'] = $data['score'];
            }

            // 更新试题回馈
            if (isset($data['question_feedback'])) {
                $updateData['question_feedback'] = $data['question_feedback'];
            }

            // 更新试题编号
            if (isset($data['question_no'])) {
                $updateData['question_no'] = $data['question_no'];
            }

            // 更新题目类型
            if (isset($data['question_type'])) {
                $updateData['question_type'] = $data['question_type'];
            }

            // 更新是否随机排序
            if (isset($data['is_range'])) {
                $updateData['is_range'] = $data['is_range'];
            }

            // 处理 answer 字段（JSON字符串）
            if (isset($data['answer'])) {
                if (is_string($data['answer'])) {
                    // 如果是字符串，尝试解析为JSON
                    $updateData['answer'] = $data['answer'];
                } else if (is_array($data['answer'])) {
                    // 如果已经是数组，转换为JSON字符串
                    $updateData['answer'] = json_encode($data['answer'], JSON_UNESCAPED_UNICODE);
                }
            } else if (isset($data['answer_decoded']) && is_array($data['answer_decoded'])) {
                // 使用已解码的数据
                $updateData['answer'] = json_encode($data['answer_decoded'], JSON_UNESCAPED_UNICODE);
            }

            // 处理 answer_feedback 字段（JSON字符串）
            if (isset($data['answer_feedback'])) {
                if (is_string($data['answer_feedback'])) {
                    // 如果是字符串，尝试解析为JSON
                    $updateData['answer_feedback'] = $data['answer_feedback'];
                } else if (is_array($data['answer_feedback'])) {
                    // 如果已经是数组，转换为JSON字符串
                    $updateData['answer_feedback'] = json_encode($data['answer_feedback'], JSON_UNESCAPED_UNICODE);
                }
            } else if (isset($data['answer_feedback_decoded']) && is_array($data['answer_feedback_decoded'])) {
                // 使用已解码的数据
                $updateData['answer_feedback'] = json_encode($data['answer_feedback_decoded'], JSON_UNESCAPED_UNICODE);
            }

            // 更新时间
            $updateData['updated_at'] = time();

            // 执行更新
            $result = $this->repository->updateQuestion($id, $updateData);

            if (!$result) {
                BizException::throws(CourseErrorCode::QUESTION_UPDATE_FAILED);
            }

            DB::commit();

            return [
                'items' => [
                    'id' => $id
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('更新题目失败', [
                'error' => $e->getMessage(),
                'id' => $id,
                'data' => $data
            ]);

            BizException::throws(CourseErrorCode::QUESTION_UPDATE_FAILED, $e->getMessage());
        }
    }

    /**
     * 获取题目详情
     *
     * @param int $id 题目ID
     * @return array 题目详情
     */
    public function getQuestionDetail(int $id)
    {
        try {
            $question = $this->repository->getQuestionById($id);

            if (!$question) {
                BizException::throws(CourseErrorCode::QUESTION_NOT_FOUND);
            }

            return [
                'items' => $question
            ];
        } catch (Exception $e) {
            Log::error('获取题目详情失败', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);

            BizException::throws(CourseErrorCode::FAILED, $e->getMessage());
        }
    }

    /**
     * 删除题目
     *
     * @param int $id 题目ID
     * @return array 处理结果
     */
    public function deleteQuestion(int $id)
    {
        try {
            DB::beginTransaction();

            // 检查题目是否存在
            $question = $this->repository->getQuestionById($id);
            if (!$question) {
                BizException::throws(CourseErrorCode::QUESTION_NOT_FOUND);
            }

            // 执行软删除
            $result = $this->repository->deleteQuestion($id);

            if (!$result) {
                BizException::throws(CourseErrorCode::QUESTION_DELETE_FAILED);
            }

            DB::commit();

            return [
                'success' => true,
                'data' => [
                    'id' => $id
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('删除题目失败', [
                'error' => $e->getMessage(),
                'id' => $id
            ]);

            BizException::throws(CourseErrorCode::QUESTION_DELETE_FAILED, $e->getMessage());
        }
    }

    /**
     * 获取题目列表
     *
     * @param array $params 查询参数
     * @return array 题目列表
     */
    public function getQuestions(array $params = [])
    {
        try {
            $result = $this->repository->getQuestions($params);

            return [
                'items' => $result['items'],
                'pagination' => [
                    'total' => $result['total'],
                    'current_page' => $result['current_page'],
                    'per_page' => $result['per_page'],
                    'last_page' => $result['last_page']
                ]
            ];

        } catch (Exception $e) {
            Log::error('获取题目列表失败', [
                'error' => $e->getMessage(),
                'params' => $params
            ]);

            BizException::throws(CourseErrorCode::FAILED, $e->getMessage());
        }
    }

    /**
     * 批量删除题目
     *
     * @param array $ids 题目ID数组
     * @return array 处理结果
     */
    public function batchDeleteQuestions(array $ids)
    {
        try {
            DB::beginTransaction();

            $successCount = 0;
            $failedIds = [];

            foreach ($ids as $id) {
                try {
                    // 检查题目是否存在
                    $question = $this->repository->getQuestionById($id);
                    if (!$question) {
                        $failedIds[] = $id;
                        continue;
                    }

                    // 执行软删除
                    $result = $this->repository->deleteQuestion($id);

                    if ($result) {
                        $successCount++;
                    } else {
                        $failedIds[] = $id;
                    }
                } catch (Exception $e) {
                    Log::error('删除题目失败', [
                        'error' => $e->getMessage(),
                        'id' => $id
                    ]);
                    $failedIds[] = $id;
                }
            }

            DB::commit();

            return [
                'items' => [
                    'success_count' => $successCount,
                    'failed_ids' => $failedIds
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('批量删除题目失败', [
                'error' => $e->getMessage(),
                'ids' => $ids
            ]);

            BizException::throws(CourseErrorCode::QUESTION_DELETE_FAILED, $e->getMessage());
        }
    }
}
