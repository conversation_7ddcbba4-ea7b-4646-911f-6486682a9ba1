<?php

namespace Modules\Course\Services;

use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\DB;
use Modules\Course\Enums\ErrorCode;
use Modules\Course\Models\SystemLog;
use Modules\Course\Models\CourseUnit;
use Modules\Course\Models\CourseUser;
use Modules\Course\Models\StudentVideoWatching;
use Modules\Course\Domain\Repositories\UnitRepository;
use Modules\Course\Domain\Repositories\ResourceRepository;
use Illuminate\Support\Facades\Log;
use Modules\Course\Models\VideoResource;

class UnitService
{
    protected $repository;
    protected $resourceRepository;
    public function __construct(UnitRepository $repository, ResourceRepository $resourceRepository)
    {
        $this->repository = $repository;
        $this->resourceRepository = $resourceRepository;
    }

    /**
     * 获取单元列表
     * 
     * @param array $params
     * @return array
     */
    public function list(array $params)
    {
        // 默认加载单元资源（包括文件、视频和测验）
        $withResources = $params['with_resources'] ?? true;

        return $this->repository->list($params, $withResources);
    }

    /**
     * 获取单元信息
     * 
     * @param int $id
     * @param bool $withResources 是否包含资源信息
     * @return array
     */
    public function info(int $id, bool $withResources = true)
    {
        return $this->repository->info($id, $withResources);
    }

    /**
     * 更新单元信息
     * 
     * @param int $id 单元ID
     * @param array $data 更新数据
     * @return int
     */
    public function save(array $data) : int
    {
        $id = $data['id'] ?? null;
        $now = time();

        // 如果存在ID则更新,不存在则新增
        if ($id) {
            // 检查单元是否存在
            $unit = $this->repository->info($id, false);
            if (empty($unit)) {
                BizException::throws(ErrorCode::UNIT_NOT_FOUND, ErrorCode::UNIT_NOT_FOUND->getMessage());
            }

            // 只更新不为null且发生变化的字段
            $updateData = [];
            foreach ($data as $key => $value) {
                if ($value !== null && $unit[$key] !== $value) {
                    $updateData[$key] = $value;
                }
            }
            
            // 如果没有需要更新的字段,直接返回原数据
            if (empty($updateData)) {
                return 0;
            }
            
            // 添加更新时间
            $updateData['updated_at'] = $now;

            // 更新单元信息
            $result = CourseUnit::where('id', $id)->update($updateData);
            
            // 记录行为日志
            SystemLog::create([
                'action' => SystemLog::ACTION_UPDATE,
                'target_type' => SystemLog::TARGET_TYPE_UNIT,
                'target_id' => $id,
                'description' => '更新单元信息'
            ]);
            
            return $result;
        } else {
            // 新增单元
            $data['created_at'] = $now;
            $data['updated_at'] = $now;
            
            // 创建单元
            $unit = CourseUnit::create($data);
            
            // 记录行为日志
            SystemLog::create([
                'action' => SystemLog::ACTION_CREATE,
                'target_type' => SystemLog::TARGET_TYPE_UNIT,
                'target_id' => $unit->id,
                'description' => '创建单元信息'
            ]);
            
            return $unit->id;
        }
    }

    /**
     * 批量保存单元
     * 
     * @param array $data
     * @return array
     */
    public function batchSave(array $data)
    {
        // 使用事务确保数据一致性
        return DB::transaction(function () use ($data) {
            try {
                $units = [];
                $now = time();
                $resourceData = []; // 用于存储资源数据

                // 预处理数据并收集资源信息
                foreach ($data['units'] as $key => $unit) {
                    // 基本单元信息
                    $unit['course_id'] = $data['course_id'];
                    $unit['created_at'] = $now;
                    $unit['updated_at'] = $now;

                    // 确保每个单元都有排序值
                    if (!isset($unit['sort_order'])) {
                        $unit['sort_order'] = $key + 1;
                    }

                    $units[] = $unit;

                    // 收集资源数据，稍后处理
                    if (!empty($unit['resources'])) {
                        $resourceData[$key] = $unit['resources'];
                        // 从单元数据中移除资源数据，避免批量插入时出错
                        unset($units[count($units) - 1]['resources']);
                    }
                }

                // 批量插入单元数据并获取ID
                $unitIds = $this->repository->batchSaveAndGetIds($units, $data['course_id']);

                // 没有单元数据可以插入
                if (empty($unitIds)) {
                    return [];
                }

                // 处理资源数据
                if (!empty($resourceData)) {
                    $this->processResourceData($resourceData, $unitIds, $now, $data['course_id']);
                }

                // 获取完整的单元数据（包括关联资源）
                $insertedUnits = $this->repository->getDetailsByIds($unitIds);

                // 记录行为日志
                SystemLog::create([
                    'action' => SystemLog::ACTION_CREATE,
                    'target_type' => SystemLog::TARGET_TYPE_UNIT,
                    'target_id' => $data['course_id'],
                    'description' => '单元批量保存'
                ]);

                return $insertedUnits;
            } catch (\Exception $e) {
                // 记录错误日志
                \Log::error('批量保存单元失败: ' . $e->getMessage(), ['data' => $data]);
                throw $e; // 重新抛出异常以便事务回滚
            }
        });
    }

    /**
     * 处理资源数据
     * 
     * @param array $resourceData 资源数据
     * @param array $unitIds 单元ID
     * @param int $now 当前时间戳
     * @param int $courseId 课程ID
     * @return void
     */
    private function processResourceData(array $resourceData, array $unitIds, int $now, int $courseId)
    {
        // 按资源类型分组
        $resourcesByType = [
            'file' => [],
            'video' => [],
            'quiz' => [],
            'folder' => [],
            'report' => []
        ];

        // 处理每种资源类型
        foreach ($resourceData as $unitIndex => $resources) {
            if (empty($unitIds[$unitIndex])) {
                continue;
            }

            $unitId = $unitIds[$unitIndex];

            foreach ($resources as $resource) {
                $resource['unit_id'] = $unitId;
                $resource['created_at'] = $now;
                $resource['updated_at'] = $now;

                // 根据资源类型分组
                if (isset($resource['type']) && isset($resourcesByType[$resource['type']])) {
                    $resourcesByType[$resource['type']][] = $resource;
                }
            }
        }

        // 批量插入不同类型的资源
        foreach ($resourcesByType as $type => $typeResources) {
            if (!empty($typeResources)) {
                $this->resourceRepository->batchSave($type, $typeResources, $courseId);
            }
        }
    }

    /**
     * 获取所有学生单元视频报表
     * 
     * @param int $unitId 单元ID
     * @param array $params 查询参数
     * @return array
     */
    public function studentVideoReport(int $unitId, array $params = [])
    {
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        $firstInitial = $params['first_initial'] ?? null;
        $lastInitial = $params['last_initial'] ?? null;
        
        // 获取单元信息
        $unit = $this->repository->info($unitId, true);
        if (empty($unit)) {
            return ['total' => 0, 'list' => []];
        }
        
        // 获取该单元所有视频资源
        $videoIds = [];
        if (!empty($unit['resources'])) {
            foreach ($unit['resources'] as $resource) {
                if ($resource['type'] === 'video') {
                    $videoIds[] = $resource['id'];
                }
            }
        }
        
        if (empty($videoIds)) {
            return ['total' => 0, 'list' => []];
        }
        
        // 获取该课程的所有学生
        $students = CourseUser::with(['user' => function($query) {
                $query->select('id', 'first_name', 'last_name', 'email');
            }])
            ->where('course_id', $unit['course_id'])
            ->where('deleted_at', 0)
            ->get()
            ->map(function($courseUser) use ($videoIds) {
                $user = $courseUser->user;
                
                // 获取学生的视频观看记录
                $videoWatching = StudentVideoWatching::where('student_id', $user->id)
                    ->whereIn('video_id', $videoIds)
                    ->where('deleted_at', 0)
                    ->get();
                
                return [
                    'student_id' => $user->id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'email' => $user->email,
                    'videos_watched' => $videoWatching->count(),
                    'total_videos_watched' => $videoWatching->unique('video_id')->count(),
                    'total_watch_duration' => $videoWatching->sum('watch_duration'),
                    'average_completion' => $videoWatching->avg('completion_percentage'),
                    'completed_videos' => $videoWatching->where('is_completed', 1)->count()
                ];
            });

        // 应用姓名首字母筛选
        if (!empty($firstInitial)) {
            $students = $students->filter(function($student) use ($firstInitial) {
                return strpos($student['first_name'], $firstInitial) === 0;
            });
        }
        
        if (!empty($lastInitial)) {
            $students = $students->filter(function($student) use ($lastInitial) {
                return strpos($student['last_name'], $lastInitial) === 0;
            });
        }
        
        // 统计总数
        $total = $students->count();
        
        // 分页
        $results = $students->forPage($page, $limit)->values();
            
        // 计算视频观看进度
        $list = [];
        $totalVideos = count($videoIds);
        
        foreach ($results as $result) {
            $list[] = [
                'student_id' => $result['student_id'],
                'first_name' => $result['first_name'],
                'last_name' => $result['last_name'],
                'email' => $result['email'],
                'videos_watched' => (int)$result['videos_watched'],
                'total_videos' => $totalVideos,
                'watch_percentage' => $totalVideos > 0 ? round(($result['videos_watched'] / $totalVideos) * 100, 2) : 0,
                'total_watch_duration' => (int)$result['total_watch_duration'],
                'average_completion' => round($result['average_completion'] ?? 0, 2),
                'completed_videos' => (int)$result['completed_videos'],
                'completion_percentage' => $totalVideos > 0 ? round(($result['completed_videos'] / $totalVideos) * 100, 2) : 0
            ];
        }
        
        return [
            'total' => $total,
            'list' => $list,
            'total_videos' => $totalVideos
        ];
    }

    /**
     * 更新单元排序
     *
     * @param array $data 排序数据
     * @return bool
     * @throws BizException
     */
    public function sort(array $data): bool
    {
        if (empty($data)) {
            return true;
        }

        try {
            DB::beginTransaction();

            foreach ($data as $item) {
                if (!isset($item['id']) || !isset($item['sort_order'])) {
                    continue;
                }
                
                $unit = CourseUnit::find($item['id']);
                if ($unit) {
                    $unit->sort_order = $item['sort_order'];
                    $unit->save();
                }
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Update unit sort failed', [
                'data' => $data,
                'error' => $e->getMessage()
            ]);
            throw new BizException(ErrorCode::UNIT_SORT_FAILED);
        }
    }

    /**
     * 更新视频资源排序
     *
     * @param array $data 排序数据
     * @return bool
     * @throws BizException
     */
    public function sortVideo(array $data): bool
    {
        if (empty($data)) {
            return true;
        }

        try {
            DB::beginTransaction();

            foreach ($data as $item) {
                if (!isset($item['id']) || !isset($item['sort_order'])) {
                    Log::warning('Invalid item data', ['item' => $item]);
                    continue;
                }

                try {
                    DB::table('video_resource')
                        ->where('id', $item['id'])
                        ->where('deleted_at', 0)
                        ->update(['sort_order' => $item['sort_order']]);
                } catch (\Exception $e) {
                    Log::error('Failed to update video sort order', [
                        'item' => $item,
                        'error' => $e->getMessage()
                    ]);
                    throw $e;
                }
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Update video sort failed', [
                'data' => $data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new BizException(ErrorCode::VIDEO_SORT_FAILED, $e->getMessage());
        }
    }
}
