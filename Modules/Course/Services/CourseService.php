<?php

namespace Modules\Course\Services;

use Modules\Course\Enums\CourseErrorCode;
use Bingo\Exceptions\BizException;
use Modules\Course\Models\Course;
use Modules\Course\Models\CourseCategory;
use Illuminate\Support\Facades\DB;
use Exception;

class CourseService
{
    /**
     * 添加课程
     *
     * @param array $data 课程数据
     * @return array 处理结果
     */
    public function addCourse(array $data)
    {
        try {
            DB::beginTransaction();

            // 检查分类是否存在
            $category = CourseCategory::where('deleted_at', 0)->find($data['category_id']);
            if (!$category) {
                BizException::throws(CourseErrorCode::CATEGORY_NOT_FOUND);
            }

            // 计算新课程的排序值
            // 如果用户没有指定排序值，则自动计算为该分类下最大排序值 + 1
            $sortOrder = $data['sort_order'] ?? null;
            if ($sortOrder === null) {
                // 查询该分类下的最大排序值
                $maxSortOrder = Course::where('category_id', $data['category_id'])
                    ->where('deleted_at', 0)
                    ->max('sort_order');

                // 如果没有找到记录，则使用默认值 1，否则使用最大值 + 1
                $sortOrder = $maxSortOrder ? $maxSortOrder + 1 : 1;
            }

            // 准备课程数据
            $courseData = [
                'name' => $data['name'],
                'short_name' => $data['short_name'],
                'category_id' => $data['category_id'],
                'status' => $data['status'] ?? Course::STATUS_PREPARING,
                'is_show' => $data['is_show'],
                'start_date' => $data['start_date'],
                'end_date' => $data['end_date'],
                'course_id' => $data['course_id'] ?? '',
                'is_user' => $data['is_user'],
                'roles' => $data['roles'] ?? '',
                'image_url' => $data['image_url'] ?? '',
                'description' => $data['description'] ?? '',
                'report_deadline' => $data['report_deadline'] ?? null,
                'max_students' => $data['max_students'] ?? 30,
                'sort_order' => $sortOrder,
                'is_active' => $data['is_active'] ?? Course::ACTIVE_NO,
                'creator_id' => $data['creator_id'] ?? 1,
            ];

            // 保存课程
            $course = Course::create($courseData);

            DB::commit();

            return [
                'items' => $course
            ];
        } catch (Exception $e) {
            DB::rollBack();
            BizException::throws(CourseErrorCode::COURSE_CREATE_FAILED, $e->getMessage());
        }
    }

    /**
     * 获取课程列表
     *
     * @param array $params 查询参数
     * @return array 课程列表
     */
    public function getCourses(array $params = [])
    {
        $query = Course::query();

        // 只获取未删除的课程
        $query->where('deleted_at', 0);

        // 按分类ID筛选
        if (isset($params['category_id'])) {
            $query->where('category_id', $params['category_id']);
        }

        // 按状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 按可见性筛选
        if (isset($params['is_show'])) {
            $query->where('is_show', $params['is_show']);
        }

        // 按是否激活筛选
        if (isset($params['is_active'])) {
            $query->where('is_active', $params['is_active']);
        }

        // 按名称搜索
        if (!empty($params['keyword'])) {
            $query->where(function($q) use ($params) {
                $q->where('name', 'like', '%' . $params['keyword'] . '%')
                  ->orWhere('short_name', 'like', '%' . $params['keyword'] . '%')
                  ->orWhere('course_id', 'like', '%' . $params['keyword'] . '%');
            });
        }

        // 按日期范围筛选
        if (!empty($params['start_date_from'])) {
            $query->where('start_date', '>=', $params['start_date_from']);
        }
        if (!empty($params['start_date_to'])) {
            $query->where('start_date', '<=', $params['start_date_to']);
        }
        if (!empty($params['end_date_from'])) {
            $query->where('end_date', '>=', $params['end_date_from']);
        }
        if (!empty($params['end_date_to'])) {
            $query->where('end_date', '<=', $params['end_date_to']);
        }

        // 排序
        // 首先按照sort_order升序排序
        $query->orderBy('sort_order', 'asc');

        // 然后按照其他字段排序（如果指定了）
        $sortField = $params['sort_field'] ?? null;
        $sortOrder = $params['sort_order'] ?? 'desc';
        if ($sortField && $sortField !== 'sort_order') {
            $query->orderBy($sortField, $sortOrder);
        }

        // 分页
        $page = $params['current_page'] ?? 1;
        $limit = $params['per_page'] ?? 20;

        $total = $query->count();
        $items = $query->forPage($page, $limit)->get();

        // 加载关联数据
        $items->load('category');

        // 计算最后一页
        $lastPage = ceil($total / $limit);

        return [
            'items' => $items,
            'pagination' => [
                'total' => $total,
                'current_page' => (int)$page,
                'per_page' => (int)$limit,
                'last_page' => $lastPage
            ]
        ];
    }

    /**
     * 获取课程详情
     *
     * @param int $id 课程ID
     * @return Course 课程详情
     */
    public function getCourseDetail(int $id)
    {
        $course = Course::with('category')->where('deleted_at', 0)->find($id);

        if (!$course) {
            BizException::throws(CourseErrorCode::COURSE_NOT_FOUND);
        }

        return ['items'=>$course];
    }

    /**
     * 更新课程
     *
     * @param int $id 课程ID
     * @param array $data 课程数据
     * @return array 处理结果
     */
    public function updateCourse(int $id, array $data)
    {
        try {
            DB::beginTransaction();

            // 检查课程是否存在
            $course = Course::where('deleted_at', 0)->find($id);

            if (!$course) {
                BizException::throws(CourseErrorCode::COURSE_NOT_FOUND);
            }

            // 如果更新分类ID，检查分类是否存在
            if (isset($data['category_id'])) {
                $category = CourseCategory::where('deleted_at', 0)->find($data['category_id']);
                if (!$category) {
                    BizException::throws(CourseErrorCode::CATEGORY_NOT_FOUND);
                }
            }

            // 更新课程
            $course->update($data);

            DB::commit();

            // 重新加载关联数据
            $course->load('category');

            return [
                'items' => $course
            ];
        } catch (Exception $e) {
            DB::rollBack();
            BizException::throws(CourseErrorCode::COURSE_UPDATE_FAILED, $e->getMessage());
        }
    }

    /**
     * 删除课程
     *
     * @param int $id 课程ID
     * @return bool 是否删除成功
     */
    public function deleteCourse(int $id)
    {
        try {
            DB::beginTransaction();

            // 检查课程是否存在
            $course = Course::where('deleted_at', 0)->find($id);

            if (!$course) {
                BizException::throws(CourseErrorCode::COURSE_NOT_FOUND);
            }

            // 软删除课程
            $course->update(['deleted_at' => time()]);

            DB::commit();

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            BizException::throws(CourseErrorCode::COURSE_DELETE_FAILED, $e->getMessage());
        }
    }

    /**
     * 批量删除课程
     *
     * @param array $ids 课程ID数组
     * @return array 处理结果
     */
    public function batchDeleteCourses(array $ids)
    {
        try {
            DB::beginTransaction();

            $successCount = 0;
            $failedIds = [];

            foreach ($ids as $id) {
                try {
                    // 检查课程是否存在
                    $course = Course::where('deleted_at', 0)->find($id);
                    if (!$course) {
                        $failedIds[] = $id;
                        continue;
                    }

                    // 软删除课程
                    $course->update(['deleted_at' => time()]);

                    $successCount++;
                } catch (Exception $e) {
                    $failedIds[] = $id;
                }
            }

            DB::commit();

            return [
                'items' => [
                    'success_count' => $successCount,
                    'failed_ids' => $failedIds
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            BizException::throws(CourseErrorCode::COURSE_DELETE_FAILED, $e->getMessage());
        }
    }

    /**
     * 调整课程排序
     *
     * @param int $id 课程ID
     * @param string $direction 调整方向 ('up' 或 'down')
     * @return array 处理结果
     */
    public function adjustCourseOrder(int $id, string $direction)
    {
        try {
            DB::beginTransaction();

            // 检查课程是否存在
            $course = Course::where('deleted_at', 0)->find($id);
            if (!$course) {
                BizException::throws(CourseErrorCode::COURSE_NOT_FOUND);
            }

            // 构建查询条件 - 只在同一分类内调整排序
            $query = Course::where('deleted_at', 0)
                ->where('category_id', $course->category_id);

            if ($direction === 'up') {
                // 上移：查找排序值小于当前课程且最接近的课程
                $targetCourse = $query->where('sort_order', '<', $course->sort_order)
                    ->orderBy('sort_order', 'desc')
                    ->first();
            } else if ($direction === 'down') {
                // 下移：查找排序值大于当前课程且最接近的课程
                $targetCourse = $query->where('sort_order', '>', $course->sort_order)
                    ->orderBy('sort_order', 'asc')
                    ->first();
            } else {
                BizException::throws(CourseErrorCode::FAILED, '无效的调整方向');
            }

            // 如果没有找到目标课程，则无法调整
            if (!$targetCourse) {
                if ($direction === 'up') {
                    BizException::throws(CourseErrorCode::FAILED, '已经是第一个，无法上移');
                } else {
                    BizException::throws(CourseErrorCode::FAILED, '已经是最后一个，无法下移');
                }
            }

            // 交换两个课程的排序值
            $tempOrder = $course->sort_order;
            $course->sort_order = $targetCourse->sort_order;
            $targetCourse->sort_order = $tempOrder;

            $course->save();
            $targetCourse->save();

            DB::commit();

            return [
                'items' => [
                    'id' => $course->id,
                    'new_sort_order' => $course->sort_order,
                    'swapped_with' => $targetCourse->id
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            if ($e instanceof BizException) {
                throw $e;
            }
            BizException::throws(CourseErrorCode::FAILED, $e->getMessage());
        }
    }

    /**
     * 批量移动课程到指定分类
     *
     * @param array $courseIds 课程ID数组
     * @param int $categoryId 目标分类ID
     * @return array 处理结果
     */
    public function moveCoursesToCategory(array $courseIds, int $categoryId)
    {
        try {
            DB::beginTransaction();

            // 检查目标分类是否存在
            $category = CourseCategory::where('deleted_at', 0)->find($categoryId);
            if (!$category) {
                BizException::throws(CourseErrorCode::CATEGORY_NOT_FOUND);
            }

            // 获取所有需要移动的课程
            $courses = Course::whereIn('id', $courseIds)
                ->where('deleted_at', 0)
                ->get();

            // 检查是否所有课程都存在
            if ($courses->count() !== count($courseIds)) {
                BizException::throws(CourseErrorCode::COURSE_NOT_FOUND, T('Course::base.course.api.move_some_courses_not_found', '部分课程不存在'));
            }

            // 计算新课程的排序值
            // 查询目标分类下的最大排序值
            $maxSortOrder = Course::where('category_id', $categoryId)
                ->where('deleted_at', 0)
                ->max('sort_order');

            // 如果没有找到记录，则使用默认值 1，否则使用最大值 + 1
            $nextSortOrder = $maxSortOrder ? $maxSortOrder + 1 : 1;

            // 移动每个课程到新分类
            $movedCourses = [];
            foreach ($courses as $course) {
                // 如果课程已经在目标分类中，则跳过
                if ($course->category_id == $categoryId) {
                    continue;
                }

                // 更新课程分类和排序值
                $course->category_id = $categoryId;
                $course->sort_order = $nextSortOrder++;
                $course->save();

                $movedCourses[] = [
                    'id' => $course->id,
                    'name' => $course->name,
                    'new_category_id' => $categoryId,
                    'new_sort_order' => $course->sort_order
                ];
            }

            DB::commit();

            return [
                'items' => $movedCourses,
                'pagination' => [
                    'total' => count($movedCourses),
                    'current_page' => 1,
                    'per_page' => count($movedCourses),
                    'last_page' => 1
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            if ($e instanceof BizException) {
                throw $e;
            }
            BizException::throws(CourseErrorCode::FAILED, $e->getMessage());
        }
    }
}
