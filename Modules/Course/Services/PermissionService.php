<?php

namespace Modules\Course\Services;

use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\Log;
use Modules\Course\Enums\ErrorCode;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Modules\Course\Models\Permission;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Container\ContainerExceptionInterface;
use Modules\Course\Domain\Repositories\PermissionRepository;

class PermissionService
{
    protected PermissionRepository $repository;

    public function __construct(PermissionRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * 获取权限树
     * 使用缓存优化性能
     *
     * @return array
     * @throws BizException
     */
    public function tree(): array
    {
        try {
            // 使用缓存键
            $cacheKey = 'permission_tree';

            // 尝试从缓存获取数据
            $tree = cache()->get($cacheKey);

            // 如果缓存不存在，则从数据库获取并缓存
            if ($tree === null) {
                $permissions = $this->repository->getAll();
                $tree = $this->buildTree($permissions);

                // 缓存数据，设置过期时间为1小时
                cache()->put($cacheKey, $tree, Permission::TREE_CACHE_TIME);
            }

            return $tree;
        } catch (ContainerExceptionInterface | NotFoundExceptionInterface $e) {
            Log::error('权限树获取失败', ['error' => $e->getMessage()]);
            BizException::throws(ErrorCode::PERMISSION_TREE_GET_FAILED, ErrorCode::PERMISSION_TREE_GET_FAILED->getMessage());
        }
    }

    /**
     * 构建权限树
     * 
     * @param array $permissions 权限列表
     * @param int $parentId 父级ID
     * @return array
     */
    protected function buildTree(array $permissions, int $parentId = 0): array
    {
        $tree = [];

        foreach ($permissions as $permission) {
            if ($permission['parent_id'] == $parentId) {
                $children = $this->buildTree($permissions, $permission['id']);
                if ($children) {
                    $permission['children'] = $children;
                }
                $tree[] = $permission;
            }
        }

        return $tree;
    }

    /**
     * 获取权限信息
     *
     * @param int $id 权限ID
     * @return array
     * @throws BizException
     */
    public function info(int $id): array
    {
        $permission = $this->repository->getInfo($id);
        if (!$permission) {
            BizException::throws(ErrorCode::PERMISSION_NOT_FOUND, ErrorCode::PERMISSION_NOT_FOUND->getMessage());
        }
        return $permission->toArray();
    }

    /**
     * 检查用户是否有指定权限
     * 
     * @param string $permissionCode
     * @return bool
     */
    public function hasPermission(string $permissionCode): bool
    {
        // 获取当前用户
        $user = Auth::user();
        if (!$user) {
            return false;
        }

        // 从缓存获取用户权限
        $userPermissions = $this->getUserPermissions($user->id);

       // 提取code
       $userPermissionCodes = array_column($userPermissions, 'code');

        // 检查权限
        return in_array($permissionCode, $userPermissionCodes);
    }

    
    /**
     * 获取用户所有权限
     * 
     * @param int $userId
     * @return array
     */
    public function getUserPermissions(int $userId): array
    {
        // 使用缓存优化性能
        return Cache::remember("user_permissions:{$userId}", 3600, function () use ($userId) {
            return $this->repository->getUserPermissions($userId);
        });
    }

     /**
     * 获取用户权限树
     * 
     * @param int $userId
     * @return array
     */
    public function getUserPermissionTree(int $userId): array
    {
        return Cache::remember("user_permission_tree:{$userId}", 3600, function () use ($userId) {
            $permissions = $this->repository->getUserPermissions($userId);
            return $this->buildTree($permissions);
        });
    }

    /**
     * 清除用户权限缓存
     * 
     * @param int $userId
     */
    public function clearUserPermissionCache(int $userId): void
    {
        Cache::forget("user_permissions:{$userId}");
        Cache::forget("user_permission_tree:{$userId}");
    }
}
