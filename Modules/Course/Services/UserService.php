<?php

namespace Modules\Course\Services;

use Exception;
use App\Http\Traits\TokenTrait;
use Modules\Course\Models\User;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\Log;
use Modules\Course\Enums\ErrorCode;
use Illuminate\Support\Facades\Auth;
use Modules\Course\Models\SystemLog;
use Illuminate\Support\Facades\Cache;
use Modules\Course\Services\TokenService;
use Modules\Course\Domain\Mail\MailConfig;
use Modules\Course\Domain\Mail\MailLogger;
use Modules\Course\Domain\Mail\MailSender;
use Modules\Course\Domain\Mail\LoggingMailLogger;
use Modules\Course\Services\Utils\NameInitialService;
use Modules\Course\Domain\Repositories\UserRepository;

class UserService
{
    use TokenTrait;

    protected UserRepository $repository;
    protected TokenService $tokenService;
    protected PermissionService $permissionService;

    public function __construct(
        UserRepository $repository,
        TokenService $tokenService,
        PermissionService $permissionService
    ) {
        $this->repository = $repository;
        $this->tokenService = $tokenService;
        $this->permissionService = $permissionService;
    }

    /**
     * 获取用户信息
     * 
     * @param int $user_id
     * @return array
     */
    public function info(int $user_id): array
    {
        return $this->repository->info($user_id);
    }

    /**
     * 获取用户列表
     * 
     * @param array $params
     * @return array
     */
    public function list(array $params): array
    {
        return $this->repository->list($params);
    }

    /**
     * 用户登录
     *
     * @param array $params
     * @return array
     * @throws BizException
     */
    public function login(array $params): array
    {
        // 验证登录凭据
        $credentials = [
            'account' => $params['account'],
            'password' => $params['password']
        ];

        if (!Auth::attempt($credentials)) {
            BizException::throws(ErrorCode::USER_LOGIN_FAILED, ErrorCode::USER_LOGIN_FAILED->getMessage());
        }

        $user = Auth::user();

        // 使用令牌服务创建令牌
        $tokenData = $this->tokenService->createToken($user, [
            'device' => $params['device'] ?? request()->userAgent(),
            'ip' => request()->ip()
        ]);

        // 更新用户最后登录时间
        $this->repository->update($user->id, [
            'last_login_time' => now()->toDateTimeString()
        ]);

        // 记录登录日志
        SystemLog::create([
            'action' => SystemLog::ACTION_LOGIN,
            'target_type' => SystemLog::TARGET_TYPE_USER,
            'target_id' => $user->id,
            'description' => '用户登录成功'
        ]);

        return [
            'user' => $user,
            'token' => $tokenData['token'],
            'token_expired_at' => $tokenData['token_expired_at'],
            'expires_in' => $tokenData['expires_in']
        ];
    }

    /**
     * 用户登出
     * 
     * @return array
     * @throws BizException
     */
    public function logout(): array
    {
        // 检查用户是否已登录
        if (!Auth::check()) {
            Log::warning('用户登出失败：用户未登录');
            BizException::throws(ErrorCode::USER_NOT_LOGIN, ErrorCode::USER_NOT_LOGIN->getMessage());
        }

        $user = Auth::user();

        // 清除认证信息
        Auth::logout();

        // 吊销用户令牌
        $this->tokenService->revokeUserToken($user->id);

        // 记录行为日志
        SystemLog::create([
            'action' => SystemLog::ACTION_LOGOUT,
            'target_type' => SystemLog::TARGET_TYPE_USER,
            'target_id' => $user->id,
            'description' => '用户登出成功',
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        // 记录成功日志
        Log::info('用户登出成功', [
            'user_id' => $user->id,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);

        return [
            'message' => '登出成功',
            'logout_time' => now()->toDateTimeString(),
            'user_id' => $user->id
        ];
    }

    /**
     * 刷新令牌
     * 
     * @return array 新令牌信息
     * @throws BizException
     */
    public function refreshToken(): array
    {
        $token = $this->getTokenFromRequest(request());
        if (empty($token)) {
            BizException::throws(ErrorCode::TOKEN_INVALID, ErrorCode::TOKEN_INVALID->getMessage());
        }

        $refreshedToken = $this->tokenService->refreshToken($token);

        if (!$refreshedToken) {
            BizException::throws(ErrorCode::TOKEN_INVALID, ErrorCode::TOKEN_INVALID->getMessage());
        }

        return $refreshedToken;
    }

    /**
     * 忘记密码
     *
     * @param array $data
     * @return array
     * @throws BizException
     */
    public function forgetPassword(array $data): array
    {
        if (isset($data['account'])) {
            $user = $this->repository->getByAccount($data['account']);
        } elseif (isset($data['email'])) {
            $user = $this->repository->getByEmail($data['email']);
        }

        if (empty($user)) {
            BizException::throws(ErrorCode::USER_NOT_FOUND, ErrorCode::USER_NOT_FOUND->getMessage());
        }

        if (!empty($data['send_email'])) {
            $this->sendResetPasswordEmail($user);
        }

        return [
            'id' => $user->id,
            'email' => $user->email,
            'account' => $user->account,
        ];
    }

    /**
     * 重置密码后强制用户登出
     * 
     * @param User $user 需要登出的用户对象
     * @return void
     */
    private function _resetAndForceLogout(User $user, string $password): void
    {
        $user->password = User::hashPassword($password);
        $user->save();

        if (Auth::check() && Auth::user()->id === $user->id) {
            // 吊销用户令牌
            $this->tokenService->revokeUserToken($user->id);

            // 清除认证信息
            Auth::logout();
        }
    }

    /**
     * 重置密码
     * 如果用户已登录，重置密码后会强制退出登录
     *
     * @param array $data
     * @return void
     * @throws BizException
     */
    public function resetPassword(array $data): void
    {
        $user = $this->repository->getById($data['id']);
        if (empty($user)) {
            BizException::throws(ErrorCode::USER_NOT_FOUND, ErrorCode::USER_NOT_FOUND->getMessage());
        }

        // 新密码和确认密码是否一致
        if ($data['password'] !== $data['confirm_password']) {
            BizException::throws(ErrorCode::USER_PASSWORD_NOT_MATCH, ErrorCode::USER_PASSWORD_NOT_MATCH->getMessage());
        }

        $this->_resetAndForceLogout($user, $data['password']);

        // 撤销重置密码令牌
        if (isset($data['token'])) {
            $this->tokenService->revokeResetPasswordToken($data['token']);
        }

        // 记录行为日志
        SystemLog::create([
            'action' => SystemLog::ACTION_RESET_PASSWORD,
            'target_type' => SystemLog::TARGET_TYPE_USER,
            'target_id' => $user->id,
            'description' => '用户重置密码后强制退出登录'
        ]);
    }

    /**
     * 更新密码
     * 
     * @param array $data
     * @return void
     * @throws BizException
     */
    public function updatePassword(array $data): void
    {
        // 获取用户信息 
        $user = $this->repository->getById($data['id']);
        if (empty($user)) {
            BizException::throws(ErrorCode::USER_NOT_FOUND, ErrorCode::USER_NOT_FOUND->getMessage());
        }

        // 检查旧密码是否正确   
        if (User::hashPassword($data['old_password']) !== $user->password) {
            BizException::throws(ErrorCode::USER_OLD_PASSWORD_INCORRECT, ErrorCode::USER_OLD_PASSWORD_INCORRECT->getMessage());
        }

        //新密码和确认密码是否一致
        if ($data['password'] !== $data['confirm_password']) {
            BizException::throws(ErrorCode::USER_PASSWORD_NOT_MATCH, ErrorCode::USER_PASSWORD_NOT_MATCH->getMessage());
        }

        $this->_resetAndForceLogout($user, $data['password']);

        // 记录行为日志
        SystemLog::create([
            'action' => SystemLog::ACTION_RESET_PASSWORD,
            'target_type' => SystemLog::TARGET_TYPE_USER,
            'target_id' => $user->id,
            'description' => '用户更新密码后强制退出登录'
        ]);
    }

    /**
     * 获取用户权限树
     * 
     * @param int $id
     * @return array
     */
    public function permissionTree(int $id): array
    {
        $user = $this->repository->getById($id);
        if (empty($user)) {
            BizException::throws(ErrorCode::USER_NOT_FOUND, ErrorCode::USER_NOT_FOUND->getMessage());
        }
        return $this->permissionService->getUserPermissionTree($user->id);
    }

    /**
     * 更新用户信息
     * 
     * @param array $data 用户信息数据
     * @return array 更新后的用户信息
     * @throws BizException 当用户不存在时抛出异常
     */
    public function update(array $data): array
    {
        // 检查用户ID是否存在
        if (!isset($data['id'])) {
            BizException::throws(ErrorCode::USER_NOT_FOUND, ErrorCode::USER_NOT_FOUND->getMessage());
        }

        // 获取用户信息
        $user = $this->repository->getById($data['id']);
        if (empty($user)) {
            BizException::throws(ErrorCode::USER_NOT_FOUND, ErrorCode::USER_NOT_FOUND->getMessage());
        }

        // 过滤出允许更新的字段
        $updateData = [];
        foreach (User::ALLOWED_UPDATE_FIELDS as $field) {
            if (isset($data[$field])) {
                $updateData[$field] = $data[$field];
            }
        }

        // 对比新旧数据，过滤掉未变化的字段
        $updateData = array_filter($updateData, function ($value, $key) use ($user) {
            return $value !== $user->$key;
        }, ARRAY_FILTER_USE_BOTH);

        // 处理姓名首字母
        $nameInitialService = app(NameInitialService::class);
        $nameFields = [
            'first_name' => 'first_initial',
            'last_name' => 'last_initial',
            'additional_first_name' => 'additional_first_initial',
            'additional_last_name' => 'additional_last_initial'
        ];

        foreach ($nameFields as $nameField => $initialField) {
            if (isset($updateData[$nameField]) && $updateData[$nameField] !== $user->$nameField) {
                $updateData[$initialField] = $nameInitialService->getSingleInitial($updateData[$nameField]);
            }
        }

        // 如果更新数据为空，直接返回当前用户信息
        if (empty($updateData)) {
            return $this->info($user->id);
        }

        // 更新用户信息
        $this->repository->update($user->id, $updateData);

        // 更新用户信息缓存
        $updatedUser = $this->repository->getById($user->id);
        $this->tokenService->cacheUserInfo($updatedUser);

        // 记录行为日志
        SystemLog::create([
            'action' => SystemLog::ACTION_UPDATE,
            'target_type' => SystemLog::TARGET_TYPE_USER,
            'target_id' => $user->id,
            'description' => '用户信息更新成功'
        ]);

        // 返回更新后的用户信息
        return $this->info($user->id);
    }

    /**
     * 获取课程成员列表
     * 
     * @param array $params
     * @return array
     */
    public function courseMembers(array $params): array
    {
        return $this->repository->getCourseMembers($params);
    }

    /**
     * 导入课程用户
     * 
     * @param array $params
     * @return array
     */
    public function importCourseMembers(array $params): array
    {
        // 记录日志
        Log::info('开始导入课程用户', [
            'course_id' => $params['course_id'] ?? 0,
            'file_name' => $params['file'] ? $params['file']->getClientOriginalName() : ''
        ]);

        try {
            // 1. 从缓存中获取可导入的用户数据
            $checkResult = $this->getImportableUsersFromCache($params);

            // 2. 获取可导入的用户数据
            $importableUsers = array_filter($checkResult['data'], function ($item) {
                return in_array($item['status'], ['new', 'exists']);
            });

            if (empty($importableUsers)) {
                BizException::throws(ErrorCode::COURSE_USER_IMPORT_NOT_FOUND, ErrorCode::COURSE_USER_IMPORT_NOT_FOUND->getMessage());
            }

            // 3. 执行导入
            $importService = app(\Modules\Course\Services\Utils\ImportFileService::class);
            $result = $importService->importCourseUsers($params['course_id'], $importableUsers);

            // 记录成功导入的日志
            if ($result['success']) {
                SystemLog::create([
                    'action' => SystemLog::ACTION_IMPORT,
                    'target_type' => SystemLog::TARGET_TYPE_COURSE_USER,
                    'target_id' => $params['course_id'],
                    'description' => '成功导入课程用户，成功：' . ($result['data']['success'] ?? 0) .
                        '，失败：' . ($result['data']['failed'] ?? 0) .
                        '，跳过：' . ($result['data']['skipped'] ?? 0)
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            // 记录异常日志
            Log::error('导入课程用户失败', [
                'course_id' => $params['course_id'] ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '导入失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 从缓存中获取可导入的用户数据
     * 
     * @param array $params
     * @return array
     */
    private function getImportableUsersFromCache(array $params): array
    {
        $cacheKey = 'course_user_import_' . $params['course_id'];
        $importableUsers = Cache::get($cacheKey);
        if (empty($importableUsers)) {
            BizException::throws(ErrorCode::COURSE_USER_IMPORT_NOT_FOUND, ErrorCode::COURSE_USER_IMPORT_NOT_FOUND->getMessage());
        }
        return $importableUsers;
    }

    /**
     * 发送重置密码邮件
     * 
     * @param User $user 用户对象
     * @return void
     */
    private function sendResetPasswordEmail(User $user): void
    {
        // 生成重置密码令牌
        $token = $this->tokenService->createResetPasswordToken($user);
        
        // 使用静态工厂方法创建邮件配置
        $mailConfig = MailConfig::fromConfig();
        
        // 创建邮件记录器
        $mailLogger = new LoggingMailLogger();
        
        // 手动创建邮件发送器实例
        $mailSender = new MailSender($mailConfig, $mailLogger);

        // 构建邮件内容
        $content = view('emails.reset-password', [
            'user' => $user,
            'token' => $token,
        ])->render();

        // 发送重置密码邮件
        $mailSender->send(
            $user->email,
            '重置密码',
            $content
        );
    }

    /**
     * 导入课程用户检查
     * 
     * @param array $params
     * @return array
     */
    public function importCourseMembersCheck(array $params): array
    {
        // 记录日志
        Log::info('开始检查导入课程用户文件', [
            'course_id' => $params['course_id'] ?? 0,
            'file_type' => $params['file'] ? $params['file']->getClientOriginalExtension() : 'unknown'
        ]);

        try {
            // 获取课程ID
            $courseId = $params['course_id'] ?? 0;

            // 获取上传的文件
            $file = $params['file'] ?? null;

            // 使用导入文件服务处理检查
            $importService = app(\Modules\Course\Services\Utils\ImportFileService::class);
            $result = $importService->checkCourseUserImport($courseId, $file);
            return $result;
        } catch (\Exception $e) {
            // 记录异常日志
            Log::error('检查导入课程用户文件失败', [
                'course_id' => $params['course_id'] ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => '检查失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 验证重置密码令牌
     * 
     * @param array $params
     * @return array
     * @throws BizException
     */
    public function verifyResetPasswordToken(array $params): array
    {
        $tokenData = $this->tokenService->validateResetPasswordToken($params['token']);
        
        return [
            'id' => $tokenData['id'],
            'email' => $tokenData['email']
        ];
    }
}
