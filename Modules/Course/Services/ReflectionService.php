<?php

namespace Modules\Course\Services;

use Illuminate\Http\Request;
use Modules\Course\Models\Role;
use Modules\Course\Models\User;
use Bingo\Exceptions\BizException;
use Modules\Course\Enums\ErrorCode;
use Illuminate\Support\Facades\Auth;
use Modules\Course\Models\CourseUnit;
use Modules\Course\Enums\CourseErrorCode;
use Modules\Course\Models\ReflectionReport;
use Modules\Course\Models\ReflectionSubmissionRecord;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Modules\Course\Domain\Repositories\ReflectionRepository;

/**
 * 反思报告服务类
 */
class ReflectionService
{
    protected $repository;

    /**
     * 构造函数
     *
     * @param ReflectionRepository $repository
     */
    public function __construct(ReflectionRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * 获取课程反思报告
     *
     * @param int $courseId 课程ID
     * @return array
     */
    public function getCourseReflection(int $courseId): array
    {
        $user = Auth::user();
        if (empty($user)) {
            BizException::throws(ErrorCode::USER_NOT_LOGIN, ErrorCode::USER_NOT_LOGIN->getMessage());
        }

        $role = $user->role;
        if (empty($role)) {
            BizException::throws(ErrorCode::USER_NOT_ROLE, ErrorCode::USER_NOT_ROLE->getMessage());
        }

        if ($role->type == Role::TYPE_STUDENT) {
            // 学生只能查看自己的反思报告
            $result = $this->repository->getStudentCourseReflection($user->id, $courseId);
        } else {
            // 教师可以查看所有学生的反思报告
            $result = $this->repository->getCourseReflection($courseId);
        }

        if (empty($result)) {
            BizException::throws(ErrorCode::REFLECTION_NOT_FOUND, ErrorCode::REFLECTION_NOT_FOUND->getMessage());
        }

        return $result;
    }

    /**
     * 获取课程反思报告列表
     *
     * @param int $courseId 课程ID
     * @param array $params 参数
     * @return array
     */
    public function getCourseReflectionList(int $courseId, array $params): array
    {
        return $this->repository->getCourseReflectionList($courseId, $params);
    }

    /**
     * 批量切换反思报告锁定状态
     *
     * @param array $ids 反思报告ID列表
     * @return array
     */
    public function batchToggleLock(array $ids): array
    {
        return $this->repository->batchToggleLock($ids);
    }

    /**
     * 批量下载反思报告
     *
     * @param array $ids 反思报告ID列表
     * @return StreamedResponse
     */
    public function batchDownload(array $ids): StreamedResponse
    {
        // 获取反思报告列表
        $reflections = ReflectionSubmissionRecord::with(['student' => function($query) {
            $query->select('id', 'first_name', 'last_name');
        }])->whereIn('id', $ids)->get();
        if (empty($reflections)) {
            BizException::throws(ErrorCode::REFLECTION_NOT_FOUND, ErrorCode::REFLECTION_NOT_FOUND->getMessage());
        }

        // 过滤出有效的文件URL
        $validFiles = [];
        foreach ($reflections as $reflection) {
            if (!empty($reflection->file_url)) {
                // 移除域名部分，只保留文件路径
                $filePath = str_replace(config('app.url'), '', $reflection->file_url);
                $validFiles[] = [
                    'path' => public_path($filePath),
                    'name' => basename($filePath),
                    'user_name' => $reflection->student->first_name . ' ' . $reflection->student->last_name . '_' . basename($filePath)
                ];
            }
        }

        if (empty($validFiles)) {
            BizException::throws(ErrorCode::REFLECTION_FILE_NOT_FOUND, '没有可下载的文件');
        }

        // 创建内存中的ZIP文件
        $zip = new \ZipArchive();
        $zipData = tempnam(sys_get_temp_dir(), 'zip');
        
        if ($zip->open($zipData, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) !== true) {
            BizException::throws(ErrorCode::SYSTEM_ERROR, '创建ZIP文件失败');
        }

        // 添加文件到ZIP，为每个学生创建独立目录
        foreach ($validFiles as $file) {
            if (file_exists($file['path'])) {
                // 使用学生姓名作为目录名，将文件添加到对应目录下
                $zip->addFile($file['path'], $file['user_name']);
            }
        }

        $zip->close();

        // 读取ZIP文件内容到内存
        $zipContent = file_get_contents($zipData);
        unlink($zipData); // 删除临时文件

        // 返回内存中的ZIP文件
        return response()->streamDownload(function() use ($zipContent) {
            echo $zipContent;
        }, 'reflections.zip', [
            'Content-Type' => 'application/zip',
        ]);
    }

    /**
     * 批量退回草稿状态
     *
     * @param array $ids 反思报告ID列表
     * @return array
     */
    public function batchBackDraft(array $ids): array
    {
        return $this->repository->batchBackDraft($ids);
    }

    /**
     * 批量延期提交
     *
     * @param array $ids 反思报告ID列表
     * @param string $delayDays 延期截止时间
     * @return array
     */
    public function batchDelaySubmit(array $ids, string $delayDays): array
    {
        return $this->repository->batchDelaySubmit($ids, $delayDays);
    }

    /**
     * 获取反思报告详情
     *
     * @param array $params 请求参数
     * @return array 反思报告详情
     */
    public function getReportDetail(array $params): array
    {
        try {
            // 记录请求日志
            \Log::info('获取反思报告详情请求', [
                'params' => $params
            ]);

            // 调用第三方API
            $response = \Illuminate\Support\Facades\Http::post('moodle.com/report/details', $params);

            // 检查响应状态
            if (!$response->successful()) {
                \Log::error('第三方API请求失败', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

                BizException::throws(
                    ErrorCode::SYSTEM_ERROR,
                    '第三方API请求失败: ' . $response->status()
                );
            }

            // 获取响应数据
            $data = $response->json();

            // 记录响应日志
            \Log::info('获取反思报告详情响应', [
                'data' => $data
            ]);

            // 返回第三方API的结果
            return $data;
        } catch (\Exception $e) {
            \Log::error('获取反思报告详情失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(ErrorCode::SYSTEM_ERROR, '获取反思报告详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 提交反思报告
     *
     * @param array $params 请求参数
     * @param \Illuminate\Http\UploadedFile|null $file 上传的文件
     * @return array 提交结果
     */
    public function sendReport(array $params, $file = null): array
    {
        try {
            // 记录请求日志
            \Log::info('提交反思报告请求', [
                'params' => $params,
                'file' => $file ? $file->getClientOriginalName() : null
            ]);

            // 准备请求数据
            $requestData = [
                'courseId' => $params['courseId'] ?? null,
                'assignmentName' => $params['assignmentName'] ?? null,
                'submissionId' => $params['submissionId'] ?? null,
                'cmid' => $params['cmid'] ?? null,
                'FileName' => $params['FileName'] ?? null,
                'FileCheckSum' => $params['FileCheckSum'] ?? null,
                'studentId' => $params['studentId'] ?? null
            ];

            // 创建HTTP请求
            $request = \Illuminate\Support\Facades\Http::asMultipart();

            // 如果有文件，添加到请求中
            if ($file && $file->isValid()) {
                $request = $request->attach(
                    'FilePath',
                    file_get_contents($file->getRealPath()),
                    $file->getClientOriginalName(),
                    ['Content-Type' => $file->getMimeType()]
                );
            }

            // 发送请求到第三方API
            $response = $request->post('moodle.com/report/send', $requestData);

            // 检查响应状态
            if (!$response->successful()) {
                \Log::error('第三方API请求失败', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

                BizException::throws(
                    ErrorCode::SYSTEM_ERROR,
                    '第三方API请求失败: ' . $response->status()
                );
            }

            // 获取响应数据
            $data = $response->json();

            // 记录响应日志
            \Log::info('提交反思报告响应', [
                'data' => $data
            ]);

            // 返回第三方API的结果
            return $data;
        } catch (\Exception $e) {
            \Log::error('提交反思报告失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(ErrorCode::SYSTEM_ERROR, '提交反思报告失败: ' . $e->getMessage());
        }
    }
    /**
     * 对反思报告进行评分
     *
     * @param array $params 评分参数
     * @return array
     */
    public function score(array $params): array
    {
        return $this->repository->score($params);
    }

    /**
     * 添加反思报告提交记录
     *
     * @param array $params 提交参数
     * @return array
     */
    public function addReflection(array $params): array
    {
        // 获取当前登录用户
        $user = Auth::user();
        if (empty($user)) {
            BizException::throws(ErrorCode::USER_NOT_LOGIN, ErrorCode::USER_NOT_LOGIN->getMessage());
        }

        // 设置学生ID为当前用户ID
        $params['student_id'] = $user->id;

        // 确保file_url字段存在
        if (empty($params['file_url'])) {
            BizException::throws(ErrorCode::SYSTEM_ERROR, '文件URL不能为空');
        }

        // 验证反思报告ID是否存在
        $reflection = $this->repository->getReflectionById($params['reflection_id']);
        if (empty($reflection)) {
            BizException::throws(ErrorCode::REFLECTION_NOT_FOUND, '反思报告不存在');
        }

        // 设置上传时间和提交时间为当前时间
        $params['upload_time'] = now();
        $params['submission_time'] = now();

        // 设置创建者ID为当前用户ID
        $params['creator_id'] = $user->id;

        // 设置创建时间为当前时间戳
        $params['created_at'] = time();

        // 设置提交状态为已提交
        $params['submission_status'] = 2; // 已提交状态

        // 调用仓库层保存记录
        return $this->repository->addReflection($params);
    }

    /**
     * 获取学生反思报告
     *
     * @param int $courseId 课程ID
     * @param int $studentId 学生ID
     * @return array
     */
    public function getStudentReflection(int $courseId, int $studentId): array
    {
        if (empty($courseId) || empty($studentId)) {
            BizException::throws(ErrorCode::INVALID_PARAMS, ErrorCode::INVALID_PARAMS->getMessage());
        }

        // 获取课程反思报告信息
        $reflection = ReflectionReport::where('course_id', $courseId)
            ->select('id', 'unit_id', 'title', 'start_date', 'end_date','total_score')
            ->first();
        if (empty($reflection)) {
            BizException::throws(ErrorCode::REFLECTION_NOT_FOUND, ErrorCode::REFLECTION_NOT_FOUND->getMessage());
        }

        // 获取学生的提交记录
        $submissionRecord = ReflectionSubmissionRecord::where('reflection_id', $reflection->id)
            ->where('student_id', $studentId)
            ->first();
        if (empty($submissionRecord)) {
            BizException::throws(ErrorCode::SUBMISSION_NOT_FOUND, ErrorCode::SUBMISSION_NOT_FOUND->getMessage());
        }


        // 获取用户信息
        $user = User::where('id', $studentId)->select('first_name', 'last_name', 'email', 'avatar_url')->first();
        if (empty($user)) {
            BizException::throws(ErrorCode::USER_NOT_FOUND, ErrorCode::USER_NOT_FOUND->getMessage());
        }

        // 获取课程和单元信息
        $unit = CourseUnit::with(['course' => function($query) {
                $query->select('id', 'name');
            }])
            ->where('id', $reflection->unit_id)
            ->select('id', 'title', 'course_id')
            ->first();
            
        // 合并course数据到unit中
        if ($unit && $unit->course) {
            $unit->course_name = $unit->course->name;
            unset($unit->course);
        }

        if (empty($unit)) {
            BizException::throws(ErrorCode::RESOURCE_NOT_FOUND, ErrorCode::RESOURCE_NOT_FOUND->getMessage());
        }

        $result = [
            'reflection' => $reflection->toArray(),
            'submission' => $submissionRecord->toArray(),
            'user' => $user->toArray(),
            'unit' => $unit->toArray()
        ];

        $scoringCriteria = ReflectionReport::getScoringCriteria();
        $scoringCriteriaTitle = ReflectionReport::getScoringCriteriaTitle();
        $result['criteria'] = [];
        foreach ($scoringCriteria as $criterion => $criteria) {
            $result['criteria'][] = [
                'name' => $criterion,
                'title' => $scoringCriteriaTitle[$criterion],
                'positive' => $criteria[1],
                'negative' => $criteria[0]
            ];
        }

        if (!empty($result['submission']) && !empty($result['submission']['score_result'])) {
            $scoreResult = $result['submission']['score_result'] ?? [];
            // 转换评分结果为新的格式
            $formattedScoreResult = [];
            foreach ($scoringCriteria as $criterion => $criteria) {
                $formattedScoreResult[$criterion] = [
                    'is_passed' => $scoreResult[$criterion]['is_passed'] ?? 0,
                    'comment' => $scoreResult[$criterion]['comment'] ?? ''
                ];
            }
            $result['submission']['score_result'] = $formattedScoreResult;
        }

        return $result;
    }
}
