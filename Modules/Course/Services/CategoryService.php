<?php

namespace Modules\Course\Services;

use Modules\Course\Enums\CourseErrorCode;
use Bingo\Exceptions\BizException;
use Modules\Course\Models\QuestionCategory;
use Illuminate\Support\Facades\DB;
use Exception;

class CategoryService
{
    /**
     * 添加题库分类
     *
     * @param array $data 分类数据
     * @return array 处理结果
     */
    public function addCategory(array $data)
    {
        try {
            DB::beginTransaction();

            // 准备分类数据
            $categoryData = [
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'parent_id' => $data['parent_id'] ?? 0,
                'sort_order' => $data['sort_order'] ?? 0,
                'creator_id' => $data['creator_id'] ?? 1,
            ];

            // 如果是顶级分类，设置level为1，否则获取父分类的level并加1
            if (empty($categoryData['parent_id']) || $categoryData['parent_id'] == 0) {
                $categoryData['level'] = 1;
                $categoryData['parent_id'] = 0; // 顶级分类的parent_id设为0
            } else {
                $parentCategory = QuestionCategory::find($categoryData['parent_id']);
                if (!$parentCategory) {
                    BizException::throws(CourseErrorCode::CATEGORY_PARENT_NOT_FOUND);
                }
                $categoryData['level'] = $parentCategory->level + 1;
            }

            // 保存分类
            $category = QuestionCategory::create($categoryData);

            DB::commit();

            return [
                'items' => [
                    'id' => $category->id,
                    'name' => $category->name,
                    'level' => $category->level
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            BizException::throws(CourseErrorCode::CATEGORY_CREATE_FAILED, $e->getMessage());
        }
    }

    /**
     * 获取分类列表
     *
     * @param array $params 查询参数
     * @return array 分类列表
     */
    public function getCategories(array $params = [])
    {
        // 使用 Eloquent 构建查询
        $query = QuestionCategory::where('deleted_at', 0);

        // 按父级ID筛选，使用 when 方法简化条件判断
        $query->when(isset($params['parent_id']), function ($q) use ($params) {
            // 如果parent_id为0或'0'，则查询顶级分类
            if ($params['parent_id'] === 0 || $params['parent_id'] === '0') {
                return $q->where('parent_id', 0);
            } else {
                return $q->where('parent_id', $params['parent_id']);
            }
        });

        // 按名称搜索，使用 when 方法简化条件判断
        $query->when(!empty($params['keyword']), function ($q) use ($params) {
            return $q->where('name', 'like', '%' . $params['keyword'] . '%');
        });

        // 排序
        $query->orderBy('sort_order', 'asc')->orderBy('id', 'asc');

        // 分页
        $perPage = $params['per_page'] ?? 100;
        $categories = $query->paginate($perPage);

        // 获取分类列表
        $categoryItems = $categories->items();

        // 获取所有分类ID
        $categoryIds = collect($categoryItems)->pluck('id')->toArray();

        // 使用 Eloquent 的 selectRaw 方法统计关联数量
        $questionCounts = [];
        if (!empty($categoryIds)) {
            $questionCounts = \Modules\Course\Models\QuestionBank::selectRaw('category_id, COUNT(*) as count')
                ->whereIn('category_id', $categoryIds)
                ->where('deleted_at', 0)
                ->groupBy('category_id')
                ->pluck('count', 'category_id')
                ->toArray();
        }

        // 使用 map 方法直接修改集合
        $items = collect($categoryItems)->map(function ($category) use ($questionCounts) {
            $category['question_num'] = $questionCounts[$category['id']] ?? 0;
            return $category;
        })->toArray();

        // 返回标准化的分页结果
        return [
            'items' => $items,
            'pagination' => [
                'total' => $categories->total(),
                'current_page' => $categories->currentPage(),
                'per_page' => $categories->perPage(),
                'last_page' => $categories->lastPage()
            ]
        ];
    }

    /**
     * 通过父级分类名称获取所有子类（支持模糊查询）
     *
     * @param string $parentName 父级分类名称（全部或部分）
     * @return array 子类列表
     */
    public function getCategoriesByParentName(string $parentName)
    {
        try {
            // 模糊查询父级分类
            $parentCategories = QuestionCategory::where('name', 'like', '%' . $parentName . '%')
                ->where('deleted_at', 0) // 只获取未删除的分类
                ->orderBy('level', 'asc')
                ->orderBy('sort_order', 'asc')
                ->get();

            if ($parentCategories->isEmpty()) {
                return $parentCategories;
            }

            // 获取所有父级分类ID
            $parentIds = $parentCategories->pluck('id')->toArray();

            // 统计每个父级分类下的题目数量
            $parentQuestionCounts = \Modules\Course\Models\QuestionBank::query()
                ->select('category_id', \Illuminate\Support\Facades\DB::raw('COUNT(*) as count'))
                ->whereIn('category_id', $parentIds)
                ->where('deleted_at', 0)
                ->groupBy('category_id')
                ->pluck('count', 'category_id')
                ->toArray();

            $result = [];

            // 对每个匹配的父级分类，获取其所有子类
            foreach ($parentCategories as $parentCategory) {
                $childCategories = $this->getAllChildCategories($parentCategory->id);

                // 获取所有子类ID
                $childIds = collect($childCategories)->pluck('id')->toArray();

                // 统计每个子类下的题目数量
                $childQuestionCounts = [];
                if (!empty($childIds)) {
                    $childQuestionCounts = \Modules\Course\Models\QuestionBank::query()
                        ->select('category_id', \Illuminate\Support\Facades\DB::raw('COUNT(*) as count'))
                        ->whereIn('category_id', $childIds)
                        ->where('deleted_at', 0)
                        ->groupBy('category_id')
                        ->pluck('count', 'category_id')
                        ->toArray();
                }

                // 将题目数量添加到子类数据中
                $childCategoriesWithCount = collect($childCategories)->map(function ($category) use ($childQuestionCounts) {
                    $category['question_num'] = $childQuestionCounts[$category['id']] ?? 0;
                    return $category;
                })->toArray();

                $result[] = [
                    'parent' => [
                        'id' => $parentCategory->id,
                        'name' => $parentCategory->name,
                        'level' => $parentCategory->level,
                        'description' => $parentCategory->description,
                        'question_num' => $parentQuestionCounts[$parentCategory->id] ?? 0
                    ],
                    'children' => $childCategoriesWithCount
                ];
            }

            return ['items'=>$result];
        } catch (Exception $e) {
            BizException::throws(CourseErrorCode::FAILED, $e->getMessage());
        }
    }

    /**
     * 递归获取所有子类
     *
     * @param int $parentId 父级分类ID
     * @return array 子类列表
     */
    private function getAllChildCategories(int $parentId)
    {
        // 获取直接子类
        $directChildren = QuestionCategory::where('parent_id', $parentId)
            ->where('deleted_at', 0) // 只获取未删除的分类
            ->orderBy('sort_order', 'asc')
            ->orderBy('id', 'asc')
            ->get()
            ->toArray();

        $allChildren = [];

        foreach ($directChildren as $child) {
            // 添加当前子类
            $allChildren[] = [
                'id' => $child['id'],
                'name' => $child['name'],
                'parent_id' => $child['parent_id'],
                'level' => $child['level'],
                'sort_order' => $child['sort_order'],
                'description' => $child['description']
            ];

            // 递归获取子类的子类
            $grandChildren = $this->getAllChildCategories($child['id']);
            if (!empty($grandChildren)) {
                $allChildren = array_merge($allChildren, $grandChildren);
            }
        }

        return $allChildren;
    }

    /**
     * 编辑题库分类
     *
     * @param int $id 分类ID
     * @param array $data 分类数据
     * @return array 处理结果
     */
    public function updateCategory(int $id, array $data)
    {
        try {
            DB::beginTransaction();

            // 查找分类
            $category = QuestionCategory::find($id);

            if (!$category) {
                BizException::throws(CourseErrorCode::CATEGORY_NOT_FOUND);
            }

            // 准备更新数据
            $updateData = [
                'name' => $data['name'] ?? $category->name,
                'description' => $data['description'] ?? $category->description,
                'sort_order' => $data['sort_order'] ?? $category->sort_order,
            ];

            // 如果更新了父级分类
            if (isset($data['parent_id']) && $data['parent_id'] != $category->parent_id) {
                // 检查是否将自己设为自己的父级
                if ($data['parent_id'] == $id) {
                    BizException::throws(CourseErrorCode::CATEGORY_SELF_PARENT);
                }

                // 检查是否将分类设为其子分类的子分类
                $childrenIds = $this->getAllChildrenIds($id);
                if (in_array($data['parent_id'], $childrenIds)) {
                    BizException::throws(CourseErrorCode::CATEGORY_CHILD_PARENT);
                }

                // 设置父级分类和层级
                if (empty($data['parent_id']) || $data['parent_id'] == 0) {
                    $updateData['parent_id'] = 0;
                    $updateData['level'] = 1;
                } else {
                    $parentCategory = QuestionCategory::find($data['parent_id']);
                    if (!$parentCategory) {
                        BizException::throws(CourseErrorCode::CATEGORY_PARENT_NOT_FOUND);
                    }
                    $updateData['parent_id'] = $data['parent_id'];
                    $updateData['level'] = $parentCategory->level + 1;
                }

                // 更新所有子分类的层级
                $this->updateChildrenLevel($id, $updateData['level']);
            }

            // 更新分类
            foreach ($updateData as $key => $value) {
                $category->$key = $value;
            }
            $category->save();

            DB::commit();

            return [
                'items' => [
                    'id' => $category->id,
                    'name' => $category->name,
                    'parent_id' => $category->parent_id,
                    'level' => $category->level,
                    'sort_order' => $category->sort_order,
                    'description' => $category->description
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            BizException::throws(CourseErrorCode::CATEGORY_UPDATE_FAILED);
        }
    }

    /**
     * 获取所有子分类ID
     *
     * @param int $categoryId 分类ID
     * @return array 子分类ID数组
     */
    private function getAllChildrenIds(int $categoryId): array
    {
        $childrenIds = [];

        // 获取直接子分类
        $directChildren = QuestionCategory::where('parent_id', $categoryId)
            ->where('deleted_at', 0) // 只获取未删除的分类
            ->get();

        foreach ($directChildren as $child) {
            $childrenIds[] = $child->id;
            // 递归获取子分类的子分类
            $grandChildrenIds = $this->getAllChildrenIds($child->id);
            if (!empty($grandChildrenIds)) {
                $childrenIds = array_merge($childrenIds, $grandChildrenIds);
            }
        }

        return $childrenIds;
    }

    /**
     * 更新所有子分类的层级
     *
     * @param int $categoryId 分类ID
     * @param int $parentLevel 父分类层级
     */
    private function updateChildrenLevel(int $categoryId, int $parentLevel): void
    {
        // 获取直接子分类
        $directChildren = QuestionCategory::where('parent_id', $categoryId)
            ->where('deleted_at', 0) // 只获取未删除的分类
            ->get();

        foreach ($directChildren as $child) {
            // 更新子分类层级
            $newLevel = $parentLevel + 1;
            $child->level = $newLevel;
            $child->save();

            // 递归更新子分类的子分类
            $this->updateChildrenLevel($child->id, $newLevel);
        }
    }

    /**
     * 删除题库分类（软删除）
     *
     * @param int $id 分类ID
     * @return array 处理结果
     */
    public function deleteCategory(int $id)
    {
        try {

            DB::beginTransaction();

            // 查找分类
            $category = QuestionCategory::find($id);

            if (!$category) {
                BizException::throws(CourseErrorCode::CATEGORY_NOT_FOUND);
            }

            // 检查是否已经删除
            if ($category->deleted_at > 0) {
                BizException::throws(CourseErrorCode::CATEGORY_ALREADY_DELETED);
            }

            // 软删除分类
            $category->deleted_at = time();
            $category->save();

            // 递归软删除所有子分类
            $this->recursiveDeleteChildren($id);

            DB::commit();

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            BizException::throws(CourseErrorCode::CATEGORY_DELETE_FAILED,$e->getMessage());
        }
    }

    /**
     * 递归软删除子分类
     *
     * @param int $categoryId 父分类ID
     */
    private function recursiveDeleteChildren(int $categoryId): void
    {
        // 获取直接子分类
        $directChildren = QuestionCategory::where('parent_id', $categoryId)
            ->where('deleted_at', 0) // 只获取未删除的分类
            ->get();

        foreach ($directChildren as $child) {
            // 软删除子分类
            $child->deleted_at = time();
            $child->save();

            // 递归删除子分类的子分类
            $this->recursiveDeleteChildren($child->id);
        }
    }

    /**
     * 批量删除题库分类（软删除）
     *
     * @param array $ids 分类ID数组
     * @return array 处理结果
     */
    public function batchDeleteCategories(array $ids)
    {
        try {
            DB::beginTransaction();

            // 获取所有有效的分类（未删除的）
            $validCategories = QuestionCategory::whereIn('id', $ids)
                ->where('deleted_at', 0)
                ->get();

            if ($validCategories->isEmpty()) {
                DB::rollBack();
                BizException::throws(CourseErrorCode::CATEGORY_NOT_FOUND, '没有找到有效的分类');
            }

            $validIds = $validCategories->pluck('id')->toArray();

            // 获取所有需要删除的分类ID（包括子分类）
            $allIdsToDelete = $this->getAllChildrenIdsBatch($validIds);

            // 合并原始ID和子分类ID，去重
            $allIdsToDelete = array_unique(array_merge($validIds, $allIdsToDelete));

            // 批量软删除所有分类
            $currentTime = time();
            QuestionCategory::whereIn('id', $allIdsToDelete)
                ->where('deleted_at', 0)
                ->update(['deleted_at' => $currentTime]);

            DB::commit();

            return [
                'items' => $validIds
            ];
        } catch (Exception $e) {
            DB::rollBack();
            BizException::throws(CourseErrorCode::CATEGORY_DELETE_FAILED, $e->getMessage());
        }
    }

    /**
     * 批量获取所有子分类ID
     *
     * @param array $parentIds 父分类ID数组
     * @return array 所有子分类ID数组
     */
    private function getAllChildrenIdsBatch(array $parentIds): array
    {
        if (empty($parentIds)) {
            return [];
        }

        $allChildrenIds = [];
        $currentParentIds = $parentIds;

        // 循环查找所有层级的子分类
        while (!empty($currentParentIds)) {
            // 获取当前层级的所有子分类
            $children = QuestionCategory::whereIn('parent_id', $currentParentIds)
                ->where('deleted_at', 0)
                ->pluck('id')
                ->toArray();

            if (empty($children)) {
                break;
            }

            // 添加到结果数组
            $allChildrenIds = array_merge($allChildrenIds, $children);

            // 准备查找下一层级
            $currentParentIds = $children;
        }

        return $allChildrenIds;
    }
}
