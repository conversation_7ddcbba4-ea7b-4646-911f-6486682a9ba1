<?php

namespace Modules\Course\Services;

use Bingo\Exceptions\BizException;
use Modules\Course\Enums\ErrorCode;
use Illuminate\Support\Facades\Auth;
use Modules\Course\Traits\UserPermissionTrait;
use Modules\Course\Domain\Repositories\UserPreferenceRepository;

class UserPreferenceService
{
    use UserPermissionTrait;

    protected $repository;

    public function __construct(UserPreferenceRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * 获取用户偏好
     * 
     * @param array $params
     * @return array
     */
    public function get(array $params): array
    {
        // 验证用户权限
        $this->validateUserPermission($params['user_id']);
        
        $userPreference = $this->repository->getPreference($params['user_id'], $params['key']);
        if (!$userPreference) {
            BizException::throws(ErrorCode::USER_PREFERENCE_NOT_FOUND, ErrorCode::USER_PREFERENCE_NOT_FOUND->getMessage());
        }
        
        return $userPreference->toArray();
    }

    /**
     * 更新用户偏好
     * 
     * @param array $params
     * @return array
     */
    public function update(array $params): array
    {
        // 验证用户权限
        $this->validateUserPermission($params['user_id']);

        $result =  $this->repository->updatePreference($params['user_id'], $params['key'], $params['value'], $params['description'] ?? null);
        return $result->toArray();
    }

    /**
     * 获取用户偏好列表
     * 
     * @param array $params
     * @return array
     */
    public function list(array $params): array
    {
        // 验证用户权限
        $this->validateUserPermission($params['user_id']);

        return $this->repository->getByUserId($params['user_id'])->toArray();
    }
} 