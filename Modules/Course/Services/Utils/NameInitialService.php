<?php

namespace Modules\Course\Services\Utils;

use Overtrue\Pinyin\Pinyin;

/**
 * 姓名首字母服务类
 * 
 * 用于处理用户姓名的首字母获取，支持中英文混合
 */
class NameInitialService
{
    /**
     * @var Pinyin Pinyin实例
     */
    protected $pinyin;

    /**
     * 构造函数
     */
    public function __construct(Pinyin $pinyin)
    {
        $this->pinyin = $pinyin;
    }

    /**
     * 获取姓名的首字母
     * 
     * @param string $firstName 名字
     * @param string $lastName 姓氏
     * @return string 首字母组合
     */
    public function getInitials(string $firstName, string $lastName): string
    {
        // 处理姓氏的首字母
        $lastNameInitial = $this->getSingleInitial($lastName);
        
        // 处理名字的首字母
        $firstNameInitial = $this->getSingleInitial($firstName);
        
        return $lastNameInitial . $firstNameInitial;
    }

    /**
     * 获取单个字符串的首字母
     * 
     * @param string $str 输入字符串
     * @return string 首字母
     */
    public function getSingleInitial(string $str): string
    {
        if (empty($str)) {
            return '';
        }

        // 如果是英文，直接取第一个字母并转为大写
        if (preg_match('/^[a-zA-Z]/', $str)) {
            return strtoupper(substr($str, 0, 1));
        }

        // 如果是中文，使用拼音转换
        $pinyin = $this->pinyin->abbr($str);
        return strtoupper(substr($pinyin, 0, 1));
    }
} 