<?php

namespace Modules\Course\Services\Utils;

use Modules\Course\Models\User;
use Illuminate\Http\UploadedFile;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\Log;
use Modules\Course\Enums\ErrorCode;
use Illuminate\Support\Facades\Cache;
use Modules\Course\Models\CourseUser;
use Modules\Course\Models\Role;

/**
 * 导入文件处理服务
 * 
 * 用于处理CSV和Excel文件的导入检查和解析
 */
class ImportFileService
{
    /**
     * 检查导入课程用户文件数据
     * 
     * @param int $courseId 课程ID
     * @param UploadedFile $file 上传的文件
     * @return array 返回检查结果，包含状态
     * @throws BizException 当文件无效或解析出错时抛出异常
     */
    public function checkCourseUserImport(int $courseId, UploadedFile $file): array
    {
        // 验证文件有效性
        $this->validateFile($file);
        
        // 解析文件数据
        $data = $this->parseFile($file);
        
        // 获取当前课程已有的用户邮箱列表
        $existingCourseUsers = $this->getExistingCourseUsers($courseId);
        
        // 检查解析后的数据
        $result = $this->checkImportData($data, $existingCourseUsers);

        //把有效数据保存到缓存中方便真实导入数据时使用
        $this->saveValidDataToCache($result,$courseId);
        
        // 返回检查结果统计
        return [
            'success' => true,
            'message' => '导入数据检查完成',
            'data' => $result,
            'total' => count($result),
            'valid' => count(array_filter($result, function($item) {
                return in_array($item['status'], ['exists', 'new']);
            })),
            'invalid' => count(array_filter($result, function($item) {
                return $item['status'] === 'error';
            })),
            'warning' => count(array_filter($result, function($item) {
                return $item['status'] === 'warning';
            }))
        ];
    }

    /**
     * 将有效的导入数据保存到缓存中
     * 
     * @param array $validData 有效的导入数据
     * @return void
     */
    private function saveValidDataToCache(array $validData,int $courseId): void
    {
        // 过滤出状态为new和exists的有效数据
        $filteredData = array_filter($validData, function($item) {
            return in_array($item['status'], ['new', 'exists']);
        });

        // 生成唯一的缓存键
        $cacheKey = 'course_user_import_' . $courseId;

        // 将数据保存到缓存中，设置30分钟的过期时间
        Cache::put($cacheKey, $filteredData, 1800);

        // 记录日志
        Log::info('导入数据已缓存', [
            'cache_key' => $cacheKey,
            'data_count' => count($filteredData)
        ]);
    }
    
    /**
     * 导入课程用户
     * 
     * @param int $courseId 课程ID
     * @param array $userData 用户数据数组
     * @return array 导入结果
     */
    public function importCourseUsers(int $courseId, array $userData): array
    {
        $importResults = [
            'total' => count($userData),
            'success' => 0,
            'failed' => 0,
            'skipped' => 0,
            'details' => []
        ];

        $nameInitialService = app(NameInitialService::class);
        
        // 使用事务确保数据一致性
        \DB::beginTransaction();
        try {
            foreach ($userData as $user) {
                // 获取用户数据
                $data = $user['data'] ?? [];
                if ($user['status'] === 'new') {
                    // 创建新用户
                    $newUser = new User();
                    $newUser->email = $data['email'];
                    $newUser->first_name = $data['first_name'] ?? '';
                    $newUser->last_name = $data['last_name'] ?? '';
                    $newUser->first_initial = $nameInitialService->getSingleInitial($data['first_name']);
                    $newUser->last_initial = $nameInitialService->getSingleInitial($data['last_name']);
                    $newUser->account = $data['username'] ?: $this->generateUserAccount($data['email']);
                    $newUser->password = User::hashPassword($data['password']);
                    $newUser->status = 1; // 默认启用
                    $newUser->institution = $data['institution'];
                    $newUser->code = $data['id_number'];
                    $newUser->role_id = $data['role_id'] ?? 0;
                    $newUser->save();
                    
                    // 添加用户到课程
                    $this->addUserToCourse($newUser->id, $courseId);
                    
                    $importResults['success']++;
                    $importResults['details'][] = [
                        'email' => $data['email'],
                        'status' => 'success',
                        'message' => '新用户创建并添加到课程'
                    ];
                } else if ($user['status'] === 'exists') {
                    // 处理已存在的用户
                    $userId = $user['user_id'] ?? 0;
                    
                    // 添加用户到课程
                    $this->addUserToCourse($userId, $courseId);
                    
                    $importResults['success']++;
                    $importResults['details'][] = [
                        'email' => $data['email'],
                        'status' => 'success',
                        'message' => '已将用户添加到课程'
                    ];
                }
            }
            
            // 提交事务
            \DB::commit();
            
            return [
                'success' => $importResults['failed'] === 0,
                'message' => '导入完成',
                'data' => $importResults
            ];
        } catch (\Exception $e) {
            // 回滚事务
            \DB::rollBack();
            
            // 记录错误
            \Log::error('导入课程用户失败', [
                'course_id' => $courseId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '导入失败: ' . $e->getMessage(),
                'data' => $importResults
            ];
        }
    }
    
    /**
     * 验证上传的文件
     * 
     * @param UploadedFile $file 上传的文件
     * @return void
     * @throws BizException 当文件无效时抛出异常
     */
    public function validateFile(UploadedFile $file): void
    {
        if (empty($file) || !$file->isValid()) {
            BizException::throws(ErrorCode::FILE_INVALID, ErrorCode::FILE_INVALID->getMessage());
        }
        
        // 检查文件类型
        $extension = $file->getClientOriginalExtension();
        if (!in_array(strtolower($extension), ['csv', 'xlsx', 'xls'])) {
            BizException::throws(ErrorCode::FILE_TYPE_INVALID, ErrorCode::FILE_TYPE_INVALID->getMessage());
        }
    }
    
    /**
     * 解析上传的文件
     * 
     * @param UploadedFile $file 上传的文件
     * @return array 解析后的数据
     * @throws BizException 当文件解析出错时抛出异常
     */
    public function parseFile(UploadedFile $file): array
    {
        try {
            $extension = strtolower($file->getClientOriginalExtension());
            
            // 根据文件类型选择解析方法
            if ($extension === 'csv') {
                $data = $this->readCsvFile($file->getPathname());
            } else {
                $data = $this->readExcelFile($file->getPathname());
            }
            
            // 检查数据是否为空
            if (empty($data)) {
                BizException::throws(ErrorCode::FILE_EMPTY, ErrorCode::FILE_EMPTY->getMessage());
            }
            
            return $data;
        } catch (\Exception $e) {
            if ($e instanceof BizException) {
                throw $e;
            }
            
            // 捕获其他异常并转换为业务异常
            BizException::throws(ErrorCode::SAVE_FAILED, '解析导入文件时出错: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取当前课程已有的用户邮箱列表
     * 
     * @param int $courseId 课程ID
     * @return array 邮箱列表
     */
    private function getExistingCourseUsers(int $courseId): array
    {
        if (empty($courseId)) {
            return [];
        }
        
        return CourseUser::with('user')
            ->where('course_id', $courseId)
            ->get()
            ->pluck('user.email')
            ->toArray();
    }
    
    /**
     * 检查导入数据
     * 
     * @param array $data 导入的数据
     * @param array $existingCourseUsers 当前课程已有的用户邮箱列表
     * @return array 检查结果
     */
    private function checkImportData(array $data, array $existingCourseUsers): array
    {
        $result = [];
        $emails = [];
        $processedData = [];
        
        // 第一步：收集所有邮箱并处理数据
        foreach ($data as $index => $row) {
            // 跳过第一行标题
            if ($index === 0 && $this->isHeaderRow($row)) {
                continue;
            }
            
            // 提取数据，Excel的列顺序定义
            // 检查行数据是否为空
            if (empty(array_filter($row, function($cell) { return !empty($cell); }))) {
                continue; // 跳过空行
            }
            
            // 提取数据
            $item = [
                'username' => trim($row[0] ?? ''),            // 用户名/账号
                'first_name' => trim($row[1] ?? ''),          // 姓氏
                'last_name' => trim($row[2] ?? ''),           // 名字
                'email' => trim($row[3] ?? ''),               // 邮箱
                'password' => trim($row[4] ?? ''),            // 密码
                'institution' => trim($row[5] ?? ''),         // 机构 
                'id_number' => trim($row[6] ?? ''),           // 学号/工号
                'course' => trim($row[7] ?? ''),              // 课程标识 
                'role' => trim($row[8] ?? '')                 // 角色
            ];
            
            // 检查数据完整性
            $validationResult = $this->validateImportData($item, $index + 1);
            if (!$validationResult['isValid']) {
                $result[] = $validationResult['error'];
                continue;
            }
            
            $emails[] = $item['email'];
            $processedData[$item['email']] = [
                'row' => $index + 1,
                'data' => $item
            ];
        }
        
        // 如果没有有效数据，直接返回
        if (empty($emails)) {
            return $result;
        }

        // 获取角色映射关系
        $roleMap = [];
        $roles = Role::where('status', 1)
            ->select('id', 'name')
            ->get();
            
        foreach ($roles as $role) {
            $roleMap[strtolower($role->name)] = $role->id;
        }
        
        // 处理角色ID
        foreach ($processedData as $email => $info) {
            $roleName = strtolower($info['data']['role']);
            if (!empty($roleName) && isset($roleMap[$roleName])) {
                $processedData[$email]['data']['role_id'] = $roleMap[$roleName];
            } else {
                // 如果角色不存在，设置为默认角色ID
                $processedData[$email]['data']['role_id'] = 0;
            }
        }
        
        // 第二步：分页查询用户信息
        $pageSize = 1000;
        $emailChunks = array_chunk($emails, $pageSize);
        $existingUsers = [];
        
        foreach ($emailChunks as $emailChunk) {
            $chunkUsers = User::whereIn('email', $emailChunk)
                ->select('id', 'email')
                ->get()
                ->keyBy('email');
                
            $existingUsers = array_merge($existingUsers, $chunkUsers->toArray());
        }

        // 第三步：处理每个用户的状态
        foreach ($processedData as $email => $info) {
            $item = $info['data'];
            $rowNumber = $info['row'];
            
            if (isset($existingUsers[$email])) {
                // 用户已存在，检查是否已在课程中
                if (in_array($email, $existingCourseUsers)) {
                    $result[] = [
                        'row' => $rowNumber,
                        'status' => 'warning',
                        'message' => '用户已存在且已在课程中',
                        'data' => $item
                    ];
                } else {
                    $result[] = [
                        'row' => $rowNumber,
                        'user_id' => $existingUsers[$email]['id'],
                        'status' => 'exists',
                        'message' => '用户已存在，可导入到课程',
                        'data' => $item
                    ];
                }
            } else {
                // 用户不存在，需要创建
                $result[] = [
                    'row' => $rowNumber,
                    'status' => 'new',
                    'message' => '用户不存在，将创建新用户',
                    'data' => $item
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * 验证导入数据的完整性
     * 
     * @param array $item 导入的数据项
     * @param int $rowNumber 行号
     * @return array 验证结果，包含isValid和error信息
     */
    private function validateImportData(array $item, int $rowNumber): array
    {
        // 定义必填字段及其错误消息
        $requiredFields = [
            'email' => '邮箱'
        ];
        
        // 可选字段（如果提供则需要非空）
        $optionalFields = [
            'username' => '用户名',
            'first_name' => '姓',
            'last_name' => '名'
        ];
        
        // 检查必填字段
        foreach ($requiredFields as $field => $label) {
            if (empty($item[$field])) {
                return [
                    'isValid' => false,
                    'error' => [
                        'row' => $rowNumber,
                        'status' => 'error',
                        'message' => "{$label}不能为空",
                        'data' => $item
                    ]
                ];
            }
        }
        
        // 检查可选字段 - 如果提供了则不能为空
        foreach ($optionalFields as $field => $label) {
            if (isset($item[$field]) && trim($item[$field]) === '') {
                return [
                    'isValid' => false,
                    'error' => [
                        'row' => $rowNumber,
                        'status' => 'error',
                        'message' => "如果提供{$label}，则不能为空",
                        'data' => $item
                    ]
                ];
            }
        }
        
        // 检查邮箱格式
        if (!filter_var($item['email'], FILTER_VALIDATE_EMAIL)) {
            return [
                'isValid' => false,
                'error' => [
                    'row' => $rowNumber,
                    'email' => $item['email'],
                    'status' => 'error',
                    'message' => '邮箱格式不正确',
                    'data' => $item
                ]
            ];
        }
        
        return ['isValid' => true];
    }
    
    /**
     * 将用户添加到课程
     * 
     * @param int $userId 用户ID
     * @param int $courseId 课程ID
     * @return bool 是否添加成功
     */
    private function addUserToCourse(int $userId, int $courseId): bool
    {
        // 检查记录是否已存在
        $exists = CourseUser::where('user_id', $userId)
            ->where('course_id', $courseId)
            ->exists();
            
        if (!$exists) {
            $courseUser = new CourseUser();
            $courseUser->user_id = $userId;
            $courseUser->course_id = $courseId;
            $courseUser->created_at = now();
            return $courseUser->save();
        }
        
        return true;
    }
    
    /**
     * 读取CSV文件
     * 
     * @param string $filePath 文件路径
     * @return array 文件数据
     */
    private function readCsvFile(string $filePath): array
    {
        $data = [];
        if (($handle = fopen($filePath, "r")) !== false) {
            while (($row = fgetcsv($handle, 1000, ",")) !== false) {
                $data[] = $row;
            }
            fclose($handle);
        }
        return $data;
    }
    
    /**
     * 读取Excel文件
     * 
     * @param string $filePath 文件路径
     * @return array 文件数据
     */
    private function readExcelFile(string $filePath): array
    {
        // 使用PhpSpreadsheet库读取Excel
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($filePath);
        $reader->setReadDataOnly(true);
        $spreadsheet = $reader->load($filePath);
        $worksheet = $spreadsheet->getActiveSheet();
        
        $data = [];
        foreach ($worksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            
            $rowData = [];
            foreach ($cellIterator as $cell) {
                $rowData[] = $cell->getValue();
            }
            $data[] = $rowData;
        }
        
        return $data;
    }
    
    /**
     * 判断是否为标题行
     * 
     * @param array $row 行数据
     * @return bool 是否为标题行
     */
    private function isHeaderRow(array $row): bool
    {
        // 添加中英文字段标识
        $headerKeywords = [
            // 邮箱相关
            'email', 'mail', '邮箱', '邮件', 'e-mail', 'email address',
            // 姓名相关
            'first name', 'firstname', 'first', '姓', '名字', 'given name',
            'last name', 'lastname', 'last', '名', '姓氏', 'family name', 'surname',
            'name', 'fullname', 'full name', '姓名', '全名',
            // 用户名相关
            'username', 'account', 'login', '用户名', '账号', '账户',
            // 密码相关
            'password', 'pwd', 'pass', '密码',
            // 机构相关
            'institution', 'org', 'organization', '机构', '组织', '单位',
            // 学号/工号相关
            'id', 'number', 'id number', 'student id', 'staff id', '学号', '工号', '编号',
            // 课程相关
            'course', 'class', '课程', '班级',
            // 角色相关
            'role', 'position', '角色', '职位'
        ];
        
        // 常见的标题行特征
        $titlePatterns = [
            '/^\s*required\s*$/i',     // "Required" 标记
            '/^\s*\*\s*$/i',           // 星号标记必填
            '/^\s*\d+\s*$/i',          // 纯数字编号
            '/^#\s*\w+/i',             // #开头的列名
        ];
        
        // 统计匹配的关键词数量
        $matchCount = 0;
        foreach ($row as $cell) {
            if (!is_string($cell)) {
                continue;
            }
            
            $cell = strtolower(trim($cell));
            
            // 检查是否是常见标题行特征
            foreach ($titlePatterns as $pattern) {
                if (preg_match($pattern, $cell)) {
                    $matchCount++;
                    break;
                }
            }
            
            // 检查是否包含关键词
            foreach ($headerKeywords as $keyword) {
                if (stripos($cell, $keyword) !== false) {
                    $matchCount++;
                    break;
                }
            }
        }
        
        // 如果匹配数量超过阈值，认为是标题行
        // 根据实际数据调整阈值，这里设置为至少2个匹配
        return $matchCount >= 2;
    }
    
    /**
     * 生成随机密码
     * 
     * @param int $length 密码长度
     * @return string 随机密码
     */
    private function generateRandomPassword(int $length = 8): string
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()';
        $password = '';
        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[random_int(0, strlen($chars) - 1)];
        }
        return $password;
    }
    
    /**
     * 从邮箱生成用户账号
     * 
     * @param string $email 用户邮箱
     * @return string 用户账号
     */
    private function generateUserAccount(string $email): string
    {
        // 取邮箱前缀作为账号基础
        $account = explode('@', $email)[0];
        
        // 检查账号是否已存在，如果存在则添加随机数字
        $exists = User::where('account', $account)->exists();
        if ($exists) {
            $account .= random_int(100, 999);
        }
        
        return $account;
    }
    
    /**
     * 创建导入用户Excel模板
     * 
     * @return string 临时文件路径
     */
    public function createImportTemplate(): string
    {
        // 创建新的PhpSpreadsheet对象
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // 设置标题行
        $headers = [
            'username (用户名)*', 
            'first_name (姓)*', 
            'last_name (名)*', 
            'email (邮箱)*', 
            'password (密码)', 
            'institution (机构)', 
            'id_number (学号/工号)', 
            'course (课程)', 
            'role (角色)'
        ];
        
        // 设置表头
        $col = 1;
        foreach ($headers as $header) {
            $sheet->setCellValueByColumnAndRow($col++, 1, $header);
        }
        
        // 添加示例数据
        $example = [
            'student1',
            'Zhang',
            'San',
            '<EMAIL>',
            '123456',
            'Example University',
            '20210001',
            'Math 101',
            'student'
        ];
        
        // 设置示例行
        $col = 1;
        foreach ($example as $value) {
            $sheet->setCellValueByColumnAndRow($col++, 2, $value);
        }
        
        // 设置表头样式
        $headerStyle = [
            'font' => [
                'bold' => true,
                'color' => ['rgb' => '000000'],
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'CCCCCC'],
            ],
        ];
        
        $sheet->getStyle('A1:I1')->applyFromArray($headerStyle);
        
        // 自动调整列宽
        foreach (range('A', 'I') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // 创建临时文件
        $tempFile = tempnam(sys_get_temp_dir(), 'user_import_template_');
        $tempFile .= '.xlsx';
        
        // 保存到临时文件
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save($tempFile);
        
        return $tempFile;
    }
} 