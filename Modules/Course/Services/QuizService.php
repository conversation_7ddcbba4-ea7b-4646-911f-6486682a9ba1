<?php

namespace Modules\Course\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Bingo\Exceptions\BizException;
use Carbon\Carbon;
use Modules\Course\Enums\CourseErrorCode;

class QuizService
{
    /**
     * 生成试卷并记录答卷信息，或返回进行中的试卷
     *
     * @param int $quizId 题库ID
     * @param int $studentId 学生ID
     * @return array
     */
    public function generateQuiz(int $quizId, int $studentId): array
    {
        try {
            // 首先检查该学生是否已有进行中(status=1)的试卷
            $existingAnswer = DB::table('questions_answer')
                ->where('quiz_id', $quizId)
                ->where('student_id', $studentId)
                ->where('status', 1) // 只查找进行中的试卷
                ->orderBy('id', 'desc')
                ->first();

            // 如果有进行中的试卷，返回该试卷数据
            if ($existingAnswer) {
                $questions = json_decode($existingAnswer->question, true);
                $userAnswers = json_decode($existingAnswer->user_answer ?? '[]', true);

                return [
                    'id' => $existingAnswer->id,
                    'quiz_id' => $quizId,
                    'student_id' => $studentId,
                    'course_id' => $existingAnswer->course_id ?? 0, // 添加课程ID，如果不存在则默认为0
                    'total_score' => $existingAnswer->total_score,
                    'status' => $existingAnswer->status,
                    'status_text' => '进行中',
                    'start_time' => $existingAnswer->start_time,
                    'questions' => $questions,
                    'user_answer' => $userAnswers,
                    'is_existing' => true // 标记这是已存在的试卷
                ];
            }

            // 从quiz表获取题库信息
            $quiz = DB::table('quiz')
                ->where('id', $quizId)
                ->first();

            if (!$quiz) {
                // 使用通用的 NOT_FOUND 错误码，默认消息为"题库不存在"
                BizException::throws(CourseErrorCode::NOT_FOUND, T('Course::error.quiz_not_found', '题库不存在'));
            }

            // 获取题目类别ID和题目数量
            $questionCategoryId = $quiz->question_category_id;
            $questionNum = $quiz->question_num;

            // 从题库中随机获取指定数量的题目
            $questions = DB::table('question_bank')
                ->where('category_id', $questionCategoryId)
                ->inRandomOrder()
                ->limit($questionNum)
                ->get();

            if ($questions->isEmpty()) {
                // 使用题目不存在的错误码，默认消息为"该题库下没有题目"
                BizException::throws(CourseErrorCode::QUESTION_NOT_FOUND, T('Course::error.quiz_no_questions', '该题库下没有题目'));
            }

            // 处理题目列表，对is_range=2的题目随机排序答案
            $totalScore = 0;
            $questionData = [];

            foreach ($questions as $question) {
                $questionItem = (array) $question;

                // 如果题目需要随机排序答案(is_range=2)
                if ($question->is_range == 2) {
                    $answers = json_decode($question->answer, true);
                    if (is_array($answers)) {
                        shuffle($answers);
                        $questionItem['answer'] = json_encode($answers);
                    }
                }

                $totalScore += $question->score;
                $questionData[] = $questionItem;
            }

            // 当前时间
            $now = Carbon::now();

            // 获取课程ID（如果quiz关联了课程）
            $courseId = 0;
            if (isset($quiz->course_id)) {
                $courseId = $quiz->course_id;
            } elseif (isset($quiz->unit_id) && $quiz->unit_id > 0) {
                // 如果quiz有unit_id，尝试从unit获取course_id
                $unit = DB::table('course_unit')
                    ->where('id', $quiz->unit_id)
                    ->where('deleted_at', 0)
                    ->first();

                if ($unit && isset($unit->course_id)) {
                    $courseId = $unit->course_id;
                }
            }

            // 记录到questions_answer表
            $answerRecord = [
                'quiz_id' => $quizId,
                'student_id' => $studentId,
                'course_id' => $courseId, // 添加课程ID
                'total_score' => $totalScore,
                'start_time' => $now->toDateTimeString(),
                'question' => json_encode($questionData),
                'status' => 1, // 1-进行中
                'user_answer' => '[]', // 空的用户答案
                'grade_answer' => '[]', // 添加空的评分答案
                'created_at' => $now->timestamp
            ];

            $recordId = DB::table('questions_answer')->insertGetId($answerRecord);

            // 返回试卷数据
            return [
                'id' => $recordId,
                'quiz_id' => $quizId,
                'student_id' => $studentId,
                'course_id' => $courseId, // 添加课程ID
                'total_score' => $totalScore,
                'status' => 1, // 进行中
                'status_text' => '进行中',
                'start_time' => $now->toDateTimeString(),
                'questions' => $questionData,
                'user_answers' => [], // 空的用户答案
                'is_existing' => false // 标记这是新生成的试卷
            ];

        } catch (\Exception $e) {
            Log::error('生成试卷失败', [
                'quiz_id' => $quizId,
                'student_id' => $studentId,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            // 使用通用的失败错误码，默认消息为"生成试卷失败"
            BizException::throws(CourseErrorCode::FAILED, T('Course::error.quiz_generate_failed', '生成试卷失败: ') . $e->getMessage());
        }
    }

    /**
     * 提交或保存答卷并评分
     *
     * @param int $answerId 答卷ID
     * @param int $studentId 学生ID
     * @param array $answers 学生提交的答案
     * @param int $status 操作状态：1-进行中，2-已完成
     * @return array
     */
    public function submitQuizAnswer(int $answerId, int $studentId, array $answers, int $status = 2): array
    {
        try {
            // 获取答卷记录
            $answerRecord = DB::table('questions_answer')
                ->where('id', $answerId)
                ->where('student_id', $studentId)
                ->first();

            if (!$answerRecord) {
                BizException::throws(CourseErrorCode::NOT_FOUND, T('Course::error.answer_not_found', '答卷不存在或您没有权限操作'));
            }

            // 检查试卷状态
            if ($answerRecord->status == 2) {
                BizException::throws(CourseErrorCode::FAILED, T('Course::error.answer_already_submitted', '该试卷已提交，不能重复提交'));
            }

            // 获取题目内容
            $questions = json_decode($answerRecord->question, true);

            // 创建题目ID到题目的映射，方便后续查找
            $questionMap = [];
            foreach ($questions as $question) {
                $questionMap[$question['id']] = $question;
            }

            // 当前时间
            $now = Carbon::now();

            // 准备更新数据
            $updateData = [
                'updated_at' => $now->timestamp,
                'status' => $status
            ];

            // 评分结果变量初始化
            $gradeAnswers = [];
            $totalScore = 0;
            $maxScore = 0;

            // 保存用户答案 - 仅包含答案数据，不包含action字段
            $updateData['user_answer'] = json_encode($answers);

            // 计算用时（秒）
            $startTime = Carbon::parse($answerRecord->start_time);
            $endTime = $now;
            // 使用abs()确保返回正数值
            $durationSeconds = abs($endTime->diffInSeconds($startTime));
            $updateData['duration'] = $durationSeconds;

            // 如果是提交操作(status=2)，进行评分
            if ($status == 2) {
                // 处理每个答案
                foreach ($answers as $userAnswer) {
                    // 验证答案格式
                    if (!isset($userAnswer['question_id']) || !isset($userAnswer['selected_option_ids'])) {
                        continue; // 跳过格式不正确的答案
                    }

                    $questionId = $userAnswer['question_id'];
                    $selectedOptionIds = $userAnswer['selected_option_ids'];

                    // 确保选项是数组
                    if (!is_array($selectedOptionIds)) {
                        $selectedOptionIds = [$selectedOptionIds];
                    }

                    // 如果题目不存在，跳过
                    if (!isset($questionMap[$questionId])) {
                        continue;
                    }

                    $question = $questionMap[$questionId];

                    // 获取正确答案
                    $correctOptions = [];
                    $answerData = json_decode($question['answer'], true);

                    if (is_array($answerData)) {
                        foreach ($answerData as $option) {
                            if (isset($option['answer_score']) && $option['answer_score'] > 0) {
                                $correctOptions[] = $option['id'];
                            }
                        }
                    }

                    // 计算分数
                    $questionScore = $question['score'] ?? 0;
                    $maxScore += $questionScore;

                    // 判断答案是否正确（需要完全匹配）
                    $isCorrect = count($selectedOptionIds) == count($correctOptions) &&
                                count(array_diff($selectedOptionIds, $correctOptions)) == 0;

                    $score = $isCorrect ? $questionScore : 0;
                    $totalScore += $score;

                    // 记录评分结果
                    $gradeAnswers[] = [
                        'question_id' => $questionId,
                        'is_correct' => $isCorrect,
                        'score' => $score,
                        'max_score' => $questionScore,
                        'selected_option_ids' => $selectedOptionIds,
                        'correct_option_ids' => $correctOptions,
                        'comment' => '',
                        'log' => [
                            [
                                'time' => date('Y-m-d H:i:s'),
                                'score' => $score,
                                'status' => '完成',
                                'action' => '自动評分：' . $score . '分',
                            ]
                        ]
                    ];
                }

                // 记录评分结果和提交时间
                $updateData['grade_answer'] = json_encode($gradeAnswers);
                $updateData['end_time'] = $now->toDateTimeString();
                $updateData['actual_score'] = $totalScore;
            }

            // 更新答卷
            DB::table('questions_answer')
                ->where('id', $answerId)
                ->update($updateData);

            // 重新查询完整的答卷记录（确保获取所有字段）
            $updatedRecord = DB::table('questions_answer')
                ->where('id', $answerId)
                ->first();

            // 构建包含所有字段的结果数组
            $result = (array)$updatedRecord;

            // 排除不需要返回的字段
            unset($result['created_at']);
            unset($result['updated_at']);
            unset($result['deleted_at']);

            // 解码JSON字段
            $result['question'] = json_decode($result['question'], true);
            $result['user_answer'] = json_decode($result['user_answer'], true);

            if (!empty($result['grade_answer'])) {
                $result['grade_answer'] = json_decode($result['grade_answer'], true);
            }

            // 添加通过率
            $result['pass_rate'] = $maxScore > 0 ? round(($totalScore / $maxScore) * 100, 2) : 0;

            // 添加状态文本描述
            $result['status_text'] = ($result['status'] == 1) ? '进行中' : '已完成';

            return $result;

        } catch (\Exception $e) {
            Log::error('提交答卷失败', [
                'answer_id' => $answerId,
                'student_id' => $studentId,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, T('Course::error.submit_answer_failed', '提交答卷失败: ') . $e->getMessage());
        }
    }

    /**
     * 获取答卷明细
     *
     * @param int $answerId 答卷ID
     * @param int $studentId 学生ID
     * @return array
     */
    public function getAnswerDetail(int $answerId, int $studentId): array
    {
        try {
            // 使用 Eloquent ORM 查询答卷记录
            $record = \Modules\Course\Models\QuestionsAnswer::where('id', $answerId)
                ->where('student_id', $studentId) // 确保只能查看自己的答卷
                ->first();

            if (!$record) {
                BizException::throws(CourseErrorCode::NOT_FOUND, T('Course::error.answer_not_found', '答卷不存在或您没有权限查看'));
            }

            // 转换为数组并排除不需要的字段
            $result = $record->toArray();
            unset($result['created_at'], $result['updated_at'], $result['deleted_at']);

            // 解析JSON字段
            $result['questions'] = json_decode($result['question'], true);
            $result['user_answer'] = json_decode($result['user_answer'] ?? '[]', true);
            $result['grade_answer'] = json_decode($result['grade_answer'] ?? '[]', true);

            // 使用更优雅的方式转换状态为描述性文本
            $statusMap = [
                1 => '进行中',
                2 => '已完成'
            ];
            $result['status_text'] = $statusMap[$record->status] ?? '未知';
            unset($result['question']);

            return $result;

        } catch (\Exception $e) {
            Log::error('获取答卷明细失败', [
                'answer_id' => $answerId,
                'student_id' => $studentId,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '获取答卷明细失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取测验明细
     *
     * @param int $quizId 测验ID
     * @return array
     */
    public function getQuizDetail(int $quizId): array
    {
        try {
            // 查询测验记录
            $quiz = DB::table('quiz')
                ->where('id', $quizId)
                ->where('deleted_at', 0) // 确保未被删除
                ->first();

            if (!$quiz) {
                BizException::throws(CourseErrorCode::NOT_FOUND, T('Course::error.quiz_not_found', '测验不存在'));
            }

            // 处理记录
            $result = (array)$quiz;

            // 排除不需要的字段
            unset($result['created_at']);
            unset($result['updated_at']);
            unset($result['deleted_at']);

            // 获取关联的题目分类信息
            if (isset($result['question_category_id']) && $result['question_category_id'] > 0) {
                $category = DB::table('question_category')
                    ->where('id', $result['question_category_id'])
                    ->where('deleted_at', 0)
                    ->first();

                if ($category) {
                    $result['category_name'] = $category->name;
                    $result['category_description'] = $category->description;
                }
            }

            // 获取所属单元信息
            if (isset($result['unit_id']) && $result['unit_id'] > 0) {
                $unit = DB::table('course_unit')
                    ->where('id', $result['unit_id'])
                    ->where('deleted_at', 0)
                    ->first();

                if ($unit) {
                    $result['unit_title'] = $unit->title;

                    // 获取所属课程信息
                    if (isset($unit->course_id) && $unit->course_id > 0) {
                        $course = DB::table('course')
                            ->where('id', $unit->course_id)
                            ->where('deleted_at', 0)
                            ->first();

                        if ($course) {
                            $result['course_id'] = $course->id;
                            $result['course_name'] = $course->name;
                        }
                    }
                }
            }

            // 获取当前用户ID
            $studentId = Auth::user()->id;

            // 查询该学生在该测验下的所有答卷记录
            $answers = DB::table('questions_answer')
                ->where('quiz_id', $quizId)
                ->where('student_id', $studentId)
                ->where('deleted_at', 0)
                ->get();

            // 初始化完成状态、最高分和已完成答题数量
            $completionStatus = 1; // 默认为未完成
            $highestScore = 0;
            $completedAnswersCount = 0; // 已完成答题数量

            if ($answers && count($answers) > 0) {
                foreach ($answers as $answer) {
                    // 统计状态为2（已完成）的答题数量
                    if ($answer->status == 2) {
                        $completedAnswersCount++;
                    }

                    // 如果有任何答卷的 actual_score > 0，则标记为已完成
                    if ($answer->actual_score > 0) {
                        $completionStatus = 2; // 已完成

                        // 更新最高分
                        if ($answer->actual_score > $highestScore) {
                            $highestScore = $answer->actual_score;
                        }
                    }
                }
            }

            // 添加完成状态、最高分和已完成答题数量到结果中
            $result['completion_status'] = $completionStatus;
            $result['highest_score'] = $highestScore;
            $result['completed_answers_count'] = $completedAnswersCount;

            // 统计完成该quiz的答卷数量
            $completedAnswersTotal = DB::table('questions_answer')
                ->where('quiz_id', $quizId)
                ->where('status', 2) // 状态为已完成
                ->where('deleted_at', 0)
                ->count();

            // 添加完成该quiz的答卷数量到结果中
            $result['completed_students_count'] = $completedAnswersTotal;

            return $result;

        } catch (\Exception $e) {
            Log::error('获取测验明细失败', [
                'quiz_id' => $quizId,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '获取测验明细失败: ' . $e->getMessage());
        }
    }

    /**
     * 搜索答卷列表
     *
     * @param int $quizId 测验ID
     * @param array $params 搜索参数
     * @return array
     */
    public function searchAnswerList(int $quizId, array $params): array
    {
        try {
            // 获取搜索参数
            $isAnswer = isset($params['is_answer']) ? (int)$params['is_answer'] : 1; // 默认查询已答过的
            $status = isset($params['status']) ? (int)$params['status'] : null; // 默认不筛选状态
            $isShowHigh = isset($params['is_showHight']) ? filter_var($params['is_showHight'], FILTER_VALIDATE_BOOLEAN) : false; // 默认显示全部
            $additionalLastName = $params['additional_last_name'] ?? null;
            $additionalFirstName = $params['additional_first_name'] ?? null;
            $perPage = isset($params['per_page']) ? (int)$params['per_page'] : 10; // 默认每页10条
            $page = isset($params['current_page']) ? (int)$params['current_page'] : 1; // 默认第1页

            // 获取测验信息
            $quiz = DB::table('quiz')
                ->where('id', $quizId)
                ->where('deleted_at', 0)
                ->first();

            if (!$quiz) {
                BizException::throws(CourseErrorCode::NOT_FOUND, T('Course::error.quiz_not_found', '测验不存在'));
            }

            // 获取课程ID
            $courseId = null;
            if (isset($quiz->course_id) && $quiz->course_id > 0) {
                $courseId = $quiz->course_id;
            } else if (isset($quiz->unit_id) && $quiz->unit_id > 0) {
                // 如果测验没有直接关联课程ID，则通过单元ID查找课程ID
                $unit = DB::table('course_unit')
                    ->where('id', $quiz->unit_id)
                    ->where('deleted_at', 0)
                    ->first();

                if ($unit && isset($unit->course_id)) {
                    $courseId = $unit->course_id;
                }
            }

            if (!$courseId) {
                BizException::throws(CourseErrorCode::NOT_FOUND, '无法确定测验所属课程');
            }

            $result = [];

            // 根据is_answer参数决定查询逻辑
            if ($isAnswer == 1) {
                // 查询已答过的学生
                $query = DB::table('questions_answer')
                    ->join('user', 'questions_answer.student_id', '=', 'user.id')
                    ->where('questions_answer.quiz_id', $quizId)
                    ->where('questions_answer.deleted_at', 0)
                    ->where('user.deleted_at', 0);

                // 只查询已完成的答卷（status = 2）
                $query->where('questions_answer.status', 2);

                // 如果需要筛选学生姓名首字母
                if ($additionalLastName) {
                    $query->where('user.additional_last_name', $additionalLastName);
                }

                if ($additionalFirstName) {
                    $query->where('user.additional_first_name', $additionalFirstName);
                }

                // 如果只显示最高分记录
                if ($isShowHigh) {
                    // 先获取每个学生的最高分记录ID
                    $highestScoreIds = DB::table('questions_answer')
                        ->select('student_id', DB::raw('MAX(actual_score) as max_score'))
                        ->where('quiz_id', $quizId)
                        ->where('deleted_at', 0)
                        ->groupBy('student_id')
                        ->get();

                    $studentHighestScores = [];
                    foreach ($highestScoreIds as $record) {
                        $studentHighestScores[$record->student_id] = $record->max_score;
                    }

                    // 获取对应最高分的答卷ID
                    $highestAnswerIds = [];
                    foreach ($studentHighestScores as $studentId => $maxScore) {
                        $highestAnswer = DB::table('questions_answer')
                            ->where('quiz_id', $quizId)
                            ->where('student_id', $studentId)
                            ->where('actual_score', $maxScore)
                            ->where('deleted_at', 0)
                            ->orderBy('id', 'desc') // 如果有多条相同分数，取最新的
                            ->first();

                        if ($highestAnswer) {
                            $highestAnswerIds[] = $highestAnswer->id;
                        }
                    }

                    // 只查询最高分记录
                    $query->whereIn('questions_answer.id', $highestAnswerIds);
                }

                // 查询总数
                $total = $query->count();

                // 分页查询
                $answers = $query->select(
                    'questions_answer.*',
                    'user.first_name',
                    'user.last_name',
                    'user.email',
                    'user.code',
                    'user.additional_first_name',
                    'user.additional_last_name'
                )
                ->orderBy('questions_answer.id', 'desc')
                ->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get();

                // 处理结果
                foreach ($answers as $answer) {
                    // 解析JSON字段
                    $questionData = json_decode($answer->question, true);
                    $userAnswerData = json_decode($answer->user_answer ?? '[]', true);
                    $gradeAnswerData = json_decode($answer->grade_answer ?? '[]', true);

                    // 构建每个题目的得分情况
                    $questionScores = [];
                    if (!empty($gradeAnswerData)) {
                        foreach ($gradeAnswerData as $grade) {
                            $questionId = $grade['question_id'] ?? null;
                            if ($questionId) {
                                $questionScores[] = [
                                    'question_id' => $questionId,
                                    'score' => $grade['score'] ?? 0,
                                    'max_score' => $grade['max_score'] ?? 0,
                                    'is_correct' => $grade['is_correct'] ?? false
                                ];
                            }
                        }
                    }

                    // 如果question_scores为空，则从edu_quiz表获取题目数量并生成默认元素
                    if (empty($questionScores)) {
                        // 获取quiz信息
                        $quiz = DB::table('quiz')
                            ->where('id', $answer->quiz_id)
                            ->where('deleted_at', 0)
                            ->first();

                        if ($quiz && isset($quiz->question_num) && $quiz->question_num > 0) {
                            // 直接根据题目数量生成默认题目
                            for ($i = 1; $i <= $quiz->question_num; $i++) {
                                $questionScores[] = [
                                    'question_id' => $i,
                                    'score' => "0.00",
                                    'max_score' => "0.00",
                                    'is_correct' => false
                                ];
                            }
                        }
                    }

                    // 状态文本
                    $statusText = '未知';
                    switch ($answer->status) {
                        case 1:
                            $statusText = '进行中';
                            break;
                        case 2:
                            $statusText = '已完成';
                            break;
                    }

                    // 计算答题时长
                    $duration = $answer->duration ?? 0;
                    $durationText = '';
                    if ($duration > 0) {
                        $minutes = floor($duration / 60);
                        $seconds = $duration % 60;
                        $durationText = "{$minutes}分{$seconds}秒";
                    }

                    // 构建结果项
                    $resultItem = [
                        'id' => $answer->id,
                        'student_id' => $answer->student_id,
                        'first_name' => $answer->first_name,
                        'last_name' => $answer->last_name,
                        'additional_first_name' => $answer->additional_first_name,
                        'additional_last_name' => $answer->additional_last_name,
                        'code' => $answer->code,
                        'email' => $answer->email,
                        'status' => $answer->status,
                        'status_text' => $statusText,
                        'start_time' => $answer->start_time,
                        'end_time' => $answer->end_time,
                        'duration' => $duration,
                        'duration_text' => $durationText,
                        'total_score' => $answer->total_score,
                        'actual_score' => $answer->actual_score,
                        'question_scores' => $questionScores
                    ];

                    $result[] = $resultItem;
                }
            } else {
                // 查询未答过的学生
                // 1. 获取已报名该课程的所有学生
                $enrolledStudents = DB::table('course_user')
                    ->join('user', 'course_user.user_id', '=', 'user.id')
                    ->where('course_user.course_id', $courseId)
                    ->where('course_user.deleted_at', 0)
                    ->where('user.deleted_at', 0);

                // 如果需要筛选学生姓名首字母
                if ($additionalLastName) {
                    $enrolledStudents->where('user.additional_last_name', $additionalLastName);
                }

                if ($additionalFirstName) {
                    $enrolledStudents->where('user.additional_first_name', $additionalFirstName);
                }

                // 2. 获取已完成该测验的学生ID（只包括状态为已完成的答卷）
                $answeredStudentIds = DB::table('questions_answer')
                    ->where('quiz_id', $quizId)
                    ->where('status', 2) // 只考虑已完成的答卷
                    ->where('deleted_at', 0)
                    ->distinct()
                    ->pluck('student_id')
                    ->toArray();

                // 3. 排除已答过的学生
                if (!empty($answeredStudentIds)) {
                    $enrolledStudents->whereNotIn('user.id', $answeredStudentIds);
                }

                // 查询总数
                $total = $enrolledStudents->count();

                // 分页查询
                $students = $enrolledStudents->select(
                    'user.id as student_id',
                    'user.first_name',
                    'user.last_name',
                    'user.email',
                    'user.code',
                    'user.additional_first_name',
                    'user.additional_last_name'
                )
                ->orderBy('user.id', 'desc')
                ->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get();

                // 处理结果
                foreach ($students as $student) {
                    // 获取quiz信息，为未作答学生生成默认的question_scores
                    $defaultQuestionScores = [];
                    $quiz = DB::table('quiz')
                        ->where('id', $quizId)
                        ->where('deleted_at', 0)
                        ->first();

                    if ($quiz && isset($quiz->question_num) && $quiz->question_num > 0) {
                        // 使用默认题目ID，直接根据题目数量生成
                        for ($i = 1; $i <= $quiz->question_num; $i++) {
                            $defaultQuestionScores[] = [
                                'question_id' => $i,
                                'score' => "0.00",
                                'max_score' => "0.00",
                                'is_correct' => false
                            ];
                        }
                    }

                    // 构建结果项
                    $resultItem = [
                        'id' => null, // 没有答卷ID
                        'student_id' => $student->student_id,
                        'first_name' => $student->first_name,
                        'last_name' => $student->last_name,
                        'additional_first_name' => $student->additional_first_name,
                        'additional_last_name' => $student->additional_last_name,
                        'code' => $student->code,
                        'email' => $student->email,
                        'status' => null, // 没有状态
                        'status_text' => '未作答',
                        'start_time' => null,
                        'end_time' => null,
                        'duration' => null,
                        'duration_text' => '',
                        'total_score' => 0,
                        'actual_score' => 0,
                        'question_scores' => $defaultQuestionScores
                    ];

                    $result[] = $resultItem;
                }
            }

            // 计算平均分统计
            $averageScores = [];
            $totalStudents = count($result);

            if ($totalStudents > 0 && $isAnswer == 1) {
                // 获取题目数量（从第一个有效的答卷中获取）
                $questionCount = 0;
                foreach ($result as $item) {
                    if (isset($item['question_scores']) && is_array($item['question_scores']) && !empty($item['question_scores'])) {
                        $questionCount = count($item['question_scores']);
                        break;
                    }
                }

                // 如果没有找到有效的题目数量，从quiz表获取
                if ($questionCount == 0) {
                    $quiz = DB::table('quiz')
                        ->where('id', $quizId)
                        ->where('deleted_at', 0)
                        ->first();
                    if ($quiz && isset($quiz->question_num)) {
                        $questionCount = $quiz->question_num;
                    }
                }

                // 初始化每个题目位置的总分和计数
                $questionTotals = [];
                $questionCounts = [];
                for ($i = 0; $i < $questionCount; $i++) {
                    $questionTotals[$i] = 0;
                    $questionCounts[$i] = 0;
                }

                // 计算总分和总人数
                $totalActualScore = 0;
                $totalMaxScore = 0;
                $validStudentCount = 0;

                foreach ($result as $item) {
                    // 只统计已完成的答卷
                    if (isset($item['status']) && $item['status'] == 2) {
                        $totalActualScore += $item['actual_score'];
                        $totalMaxScore += $item['total_score'];
                        $validStudentCount++;

                        // 按照题目在数组中的位置统计得分
                        if (isset($item['question_scores']) && is_array($item['question_scores'])) {
                            foreach ($item['question_scores'] as $index => $qScore) {
                                if (isset($qScore['score']) && $index < $questionCount) {
                                    $questionTotals[$index] += floatval($qScore['score']);
                                    $questionCounts[$index]++;
                                }
                            }
                        }
                    }
                }

                // 计算总平均分
                $overallAverage = $validStudentCount > 0 ? round($totalActualScore / $validStudentCount, 2) : 0;
                $maxPossibleScore = $validStudentCount > 0 ? round($totalMaxScore / $validStudentCount, 2) : 0;

                // 计算每个题目位置的平均分
                $questionAverages = [];
                for ($i = 0; $i < $questionCount; $i++) {
                    $avg = $questionCounts[$i] > 0 ? round($questionTotals[$i] / $questionCounts[$i], 2) : 0;
                    $questionAverages[] = [
                        'question_index' => $i + 1, // 题目序号（从1开始）
                        'average_score' => $avg
                    ];
                }
                $averageScores['questions'] = $questionAverages;

                // 添加总平均分
                $averageScores['overall'] = [
                    'average' => $overallAverage,
                    'max_possible' => $maxPossibleScore,
                    'valid_count' => $validStudentCount
                ];
            }

            // 返回结果
            return [
                'items' => $result,
                'average_scores' => $averageScores,
                'pagination' => [
                    'total' => $total,
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'last_page' => ceil($total / $perPage)
                ]
            ];

        } catch (\Exception $e) {
            Log::error('搜索答卷列表失败', [
                'quiz_id' => $quizId,
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '搜索答卷列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 导出答卷列表为CSV
     *
     * @param int $quizId 测验ID
     * @param array $params 搜索参数
     * @return string CSV内容
     */
    public function exportAnswerListToCsv(int $quizId, array $params): string
    {
        try {
            // 获取答卷列表数据（不分页，获取全部）
            $params['per_page'] = 1000; // 设置一个较大的值，确保获取所有数据
            $params['current_page'] = 1;

            // 调用搜索方法获取数据
            $searchResult = $this->searchAnswerList($quizId, $params);
            $items = $searchResult['items'] ?? [];
            $averageScores = $searchResult['average_scores'] ?? [];

            // 如果没有数据，返回空字符串
            if (empty($items)) {
                return '';
            }

            // 获取测验信息
            $quiz = DB::table('quiz')
                ->where('id', $quizId)
                ->where('deleted_at', 0)
                ->first();

            if (!$quiz) {
                BizException::throws(CourseErrorCode::NOT_FOUND, T('Course::error.quiz_not_found', '测验不存在'));
            }

            // 获取题目信息
            $questionIds = [];
            $questionCount = 0;

            // 获取第一个有效答卷的题目数量
            foreach ($items as $item) {
                if (isset($item['question']) && is_array($item['question']) && !empty($item['question'])) {
                    $questionCount = count($item['question']);
                    break;
                }
            }

            // 如果没有找到有效的题目数量，则从question_scores中获取
            if ($questionCount == 0) {
                foreach ($items as $item) {
                    if (isset($item['question_scores']) && is_array($item['question_scores'])) {
                        foreach ($item['question_scores'] as $qKey => $qScore) {
                            $qId = str_replace('Q:', '', $qKey);
                            if (!in_array($qId, $questionIds)) {
                                $questionIds[] = $qId;
                            }
                        }
                    }
                }
                $questionCount = count($questionIds);
            } else {
                // 如果有题目数量，则创建一个从1到questionCount的数组
                $questionIds = range(1, $questionCount);
            }

            // 排序题目ID
            sort($questionIds, SORT_NUMERIC);

            // 创建题目ID到序号的映射
            $questionIdToIndex = [];
            for ($i = 0; $i < count($questionIds); $i++) {
                $questionIdToIndex[$questionIds[$i]] = $i + 1;
            }

            // 准备CSV头部
            $headers = [
                '姓氏',
                '名字',
                '编号',
                '电子邮件信箱',
                '作答状态',
                '开始於',
                '完成时间',
                '花费时间',
                '分数/10.00'
            ];

            // 添加题目列
            for ($i = 1; $i <= $questionCount; $i++) {
                $headers[] = "Q{$i}";
            }

            // 准备CSV内容
            $csvContent = '';

            // 添加CSV头部
            $csvContent .= implode(',', array_map(function($header) {
                return '"' . str_replace('"', '""', $header) . '"';
            }, $headers)) . "\n";

            // 添加数据行
            foreach ($items as $item) {
                $row = [
                    isset($item['additional_last_name']) ? $item['additional_last_name'] : '',
                    isset($item['additional_first_name']) ? $item['additional_first_name'] : '',
                    isset($item['code']) ? $item['code'] : '',
                    isset($item['email']) ? $item['email'] : '',
                    isset($item['status_text']) ? $item['status_text'] : '',
                    isset($item['start_time']) ? $item['start_time'] : '',
                    isset($item['end_time']) ? $item['end_time'] : '',
                    isset($item['duration_text']) ? $item['duration_text'] : '',
                    isset($item['actual_score']) ? $item['actual_score'] : '0'
                ];

                // 添加题目得分
                for ($i = 1; $i <= $questionCount; $i++) {
                    $score = '0';

                    // 查找对应的实际题目ID
                    foreach ($item['question_scores'] ?? [] as $qScore) {
                        if (isset($qScore['question_id']) && isset($questionIdToIndex[$qScore['question_id']]) &&
                            $questionIdToIndex[$qScore['question_id']] == $i) {
                            $score = isset($qScore['score']) ? $qScore['score'] : '0';
                            break;
                        }
                    }

                    $row[] = $score;
                }

                // 添加行到CSV
                $csvContent .= implode(',', array_map(function($cell) {
                    return '"' . str_replace('"', '""', $cell) . '"';
                }, $row)) . "\n";
            }

            // 添加平均分行
            if (!empty($averageScores)) {
                $avgRow = [
                    '总平均',
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    isset($averageScores['overall']['average']) ? $averageScores['overall']['average'] : '0'
                ];

                // 添加题目平均分
                for ($i = 1; $i <= $questionCount; $i++) {
                    $avgScore = '0';

                    // 查找对应的实际题目ID
                    if (isset($averageScores['questions'])) {
                        foreach ($averageScores['questions'] as $qScore) {
                            if (isset($qScore['question_id']) && isset($questionIdToIndex[$qScore['question_id']]) &&
                                $questionIdToIndex[$qScore['question_id']] == $i) {
                                $avgScore = $qScore['average_score'];
                                break;
                            }
                        }
                    }

                    $avgRow[] = $avgScore;
                }

                // 添加平均分行到CSV
                $csvContent .= implode(',', array_map(function($cell) {
                    return '"' . str_replace('"', '""', $cell) . '"';
                }, $avgRow)) . "\n";
            }

            return $csvContent;

        } catch (\Exception $e) {
            Log::error('导出答卷列表失败', [
                'quiz_id' => $quizId,
                'params' => $params,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '导出答卷列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取单个题目详情
     *
     * @param int $answerId 答卷ID
     * @param int $questionId 题目ID
     * @return array
     */
    public function getSingleQuestionDetail(int $answerId, int $questionId): array
    {
        try {
            // 查询答卷记录
            $answerRecord = DB::table('questions_answer')
                ->where('id', $answerId)
                ->where('deleted_at', 0)
                ->first();

            if (!$answerRecord) {
                BizException::throws(CourseErrorCode::NOT_FOUND, T('Course::error.answer_not_found', '答卷不存在'));
            }

            // 获取学生信息
            $student = DB::table('user')
                ->where('id', $answerRecord->student_id)
                ->where('deleted_at', 0)
                ->first();

            // 获取quiz信息
            $quiz = DB::table('quiz')
                ->where('id', $answerRecord->quiz_id)
                ->where('deleted_at', 0)
                ->first();

            // 解析题目JSON
            $questions = json_decode($answerRecord->question, true);
            if (!is_array($questions)) {
                BizException::throws(CourseErrorCode::FAILED, T('Course::error.question_format_invalid', '题目格式无效'));
            }

            // 查找指定ID的题目
            $questionData = null;
            foreach ($questions as $question) {
                if (isset($question['id']) && $question['id'] == $questionId) {
                    $questionData = $question;
                    break;
                }
            }

            if (!$questionData) {
                BizException::throws(CourseErrorCode::NOT_FOUND, T('Course::error.question_not_found', '题目不存在'));
            }

            // 解析用户答案JSON
            $userAnswers = json_decode($answerRecord->user_answer ?? '[]', true);
            $userAnswer = null;
            if (is_array($userAnswers)) {
                foreach ($userAnswers as $answer) {
                    if (isset($answer['question_id']) && $answer['question_id'] == $questionId) {
                        $userAnswer = $answer;
                        break;
                    }
                }
            }

            // 解析评分JSON
            $gradeAnswers = json_decode($answerRecord->grade_answer ?? '[]', true);
            $gradeAnswer = null;
            if (is_array($gradeAnswers)) {
                foreach ($gradeAnswers as $grade) {
                    if (isset($grade['question_id']) && $grade['question_id'] == $questionId) {
                        $gradeAnswer = $grade;
                        break;
                    }
                }
            }

            // 计算答题时长文本
            $durationText = '';
            if ($answerRecord->duration && $answerRecord->duration > 0) {
                $minutes = floor($answerRecord->duration / 60);
                $seconds = $answerRecord->duration % 60;
                $durationText = "{$minutes}分{$seconds}秒";
            }

            // 构建结果
            $result = [
                'answer_id' => $answerId, // 答卷记录ID
                'question_id' => $questionId, // 题目ID
                'question' => $questionData, // 题目详细信息，包含题目内容、选项、分值等
                'user_answer' => $userAnswer, // 用户答案信息，包含选择的选项ID
                'grade_answer' => $gradeAnswer, // 评分信息，包含得分、满分、是否正确等
                'quiz_id' => $answerRecord->quiz_id, // 测验ID
                'student_id' => $answerRecord->student_id, // 学生ID
                'status' => $answerRecord->status, // 答卷状态：1-进行中，2-已完成
                'status_text' => $answerRecord->status == 1 ? '进行中' : '已完成', // 答卷状态文本描述
                'start_time' => $answerRecord->start_time, // 答题开始时间
                'end_time' => $answerRecord->end_time, // 答题结束时间
                'duration' => $answerRecord->duration, // 答题时长（秒）
                'duration_text' => $durationText, // 答题时长文本（X分Y秒格式）
                // 学生信息
                'student_name' => $student ? ($student->first_name . ' ' . $student->last_name) : '', // 学生英文姓名
                'student_code' => $student ? $student->code : '', // 学生编号
                'student_email' => $student ? $student->email : '', // 学生邮箱
                'additional_first_name' => $student ? $student->additional_first_name : '', // 学生中文名字
                'additional_last_name' => $student ? $student->additional_last_name : '', // 学生中文姓氏
                // Quiz信息
                'quiz_title' => $quiz ? $quiz->title : '', // 测验标题
                'quiz_description' => $quiz ? $quiz->description : '', // 测验描述
                'quiz_total_score' => $quiz ? $quiz->total_score : 0, // 测验总分
                'quiz_pass_score' => $quiz ? $quiz->pass_score : 0, // 测验及格分
                'quiz_question_num' => $quiz ? $quiz->question_num : 0 // 测验题目总数
            ];

            return $result;

        } catch (\Exception $e) {
            Log::error('获取单个题目详情失败', [
                'answer_id' => $answerId,
                'question_id' => $questionId,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '获取单个题目详情失败: ' . $e->getMessage());
        }
    }

    /**
     * 修改题目判分
     *
     * @param int $answerId 答卷ID
     * @param int $questionId 题目ID
     * @param float|null $score 新分数
     * @param string|null $comment 评语
     * @return array
     */
    public function editQuestionScore(int $answerId, int $questionId, $score = null, $comment = null): array
    {
        try {
            // 验证参数：score和comment不能同时为空
            if ($score === null && ($comment === null || $comment === '')) {
                BizException::throws(CourseErrorCode::FAILED, 'score和comment不能同时为空，至少需要上传一个');
            }

            // 查询答卷记录，确保状态为已完成
            $answerRecord = DB::table('questions_answer')
                ->where('id', $answerId)
                ->where('status', 2) // 只允许修改已完成的答卷
                ->where('deleted_at', 0)
                ->first();

            if (!$answerRecord) {
                BizException::throws(CourseErrorCode::NOT_FOUND, T('Course::error.answer_not_found', '答卷不存在或未完成'));
            }

            // 解析评分JSON
            $gradeAnswers = json_decode($answerRecord->grade_answer ?? '[]', true);
            if (!is_array($gradeAnswers)) {
                BizException::throws(CourseErrorCode::FAILED, T('Course::error.grade_format_invalid', '评分数据格式无效'));
            }

            // 查找指定题目
            $questionFound = false;
            $questionIndex = -1;
            $oldScore = 0;
            $maxScore = 0;

            foreach ($gradeAnswers as $index => $gradeAnswer) {
                if (isset($gradeAnswer['question_id']) && $gradeAnswer['question_id'] == $questionId) {
                    $oldScore = floatval($gradeAnswer['score'] ?? 0);
                    $maxScore = floatval($gradeAnswer['max_score'] ?? 0);
                    $questionFound = true;
                    $questionIndex = $index;
                    break;
                }
            }

            if (!$questionFound) {
                BizException::throws(CourseErrorCode::NOT_FOUND, T('Course::error.question_not_found', '题目不存在'));
            }

            // 获取当前用户信息
            $currentUser = Auth::user();
            $teacherInfo = $currentUser ? ($currentUser->first_name . ' ' . $currentUser->last_name) : '系统';

            $newScore = $oldScore;
            $scoreDiff = 0;
            $actionText = '';

            // 处理分数修改
            if ($score !== null) {
                $newScore = floatval($score);

                // 验证分数不能超过最大分数
                if ($newScore > $maxScore) {
                    BizException::throws(CourseErrorCode::FAILED, "分数不能超过题目最大分数 {$maxScore}");
                }

                // 验证分数不能为负数
                if ($newScore < 0) {
                    BizException::throws(CourseErrorCode::FAILED, "分数不能为负数");
                }

                $scoreDiff = $newScore - $oldScore;

                // 更新题目分数和正确性
                $gradeAnswers[$questionIndex]['score'] = (string)$newScore;
                $gradeAnswers[$questionIndex]['is_correct'] = $newScore > 0;

                // 确保log数组存在
                if (!isset($gradeAnswers[$questionIndex]['log']) || !is_array($gradeAnswers[$questionIndex]['log'])) {
                    $gradeAnswers[$questionIndex]['log'] = [];
                }

                // 生成动作文本
                if ($scoreDiff > 0) {
                    $actionText = "人工評分：+{$scoreDiff}分 評分人：{$teacherInfo}";
                } elseif ($scoreDiff < 0) {
                    $actionText = "人工評分：{$scoreDiff}分 評分人：{$teacherInfo}";
                } else {
                    $actionText = "人工評分：{$newScore}分 評分人：{$teacherInfo}";
                }

                // 添加日志记录到题目的log中
                $gradeAnswers[$questionIndex]['log'][] = [
                    'time' => date('Y-m-d H:i:s'),
                    'score' => (string)$newScore,
                    'status' => '已完成',
                    'action' => $actionText
                ];
            }

            // 处理评语修改
            if ($comment !== null) {
                $gradeAnswers[$questionIndex]['comment'] = $comment;
            }

            // 重新计算总分
            $newTotalScore = 0;
            foreach ($gradeAnswers as $gradeAnswer) {
                $newTotalScore += floatval($gradeAnswer['score'] ?? 0);
            }

            // 更新数据库
            $updateData = [
                'grade_answer' => json_encode($gradeAnswers),
                'actual_score' => $newTotalScore,
                'updated_at' => time()
            ];

            DB::table('questions_answer')
                ->where('id', $answerId)
                ->update($updateData);

            return [
                'answer_id' => $answerId,
                'question_id' => $questionId,
                'old_score' => $oldScore,
                'new_score' => $newScore,
                'score_diff' => $scoreDiff,
                'new_total_score' => $newTotalScore,
                'comment' => $comment,
                'teacher' => $teacherInfo,
                'action' => $actionText,
                'updated_at' => date('Y-m-d H:i:s')
            ];

        } catch (\Exception $e) {
            Log::error('修改题目判分失败', [
                'answer_id' => $answerId,
                'question_id' => $questionId,
                'score' => $score,
                'comment' => $comment,
                'error' => $e->getMessage()
            ]);

            if ($e instanceof BizException) {
                throw $e;
            }

            BizException::throws(CourseErrorCode::FAILED, '修改题目判分失败: ' . $e->getMessage());
        }
    }
}