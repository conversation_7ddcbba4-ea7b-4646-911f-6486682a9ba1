<?php

namespace Modules\Course\Services;

use Illuminate\Support\Str;
use Modules\Course\Models\Quiz;
use Illuminate\Http\UploadedFile;
use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\Log;
use Modules\Course\Enums\ErrorCode;
use Modules\Course\Models\UnitFile;
use Illuminate\Support\Facades\Auth;
use Modules\Course\Models\SystemLog;
use Illuminate\Support\Facades\DB;
use Modules\Course\Models\VideoResource;
use Modules\Course\Models\FolderResource;
use Modules\Course\Domain\Repositories\ResourceRepository;
use Modules\Course\Models\ReflectionReport;

/**
 * 资源服务类
 *
 * 负责处理课程资源的上传、下载和管理相关业务逻辑
 */
class ResourceService
{
    /**
     * 资源仓库
     *
     * @var ResourceRepository
     */
    protected $repository;

    /**
     * 构造函数
     *
     * @param ResourceRepository $repository
     */
    public function __construct(ResourceRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * 上传资源文件
     *
     * @param array $data 请求数据
     * @param \Illuminate\Http\UploadedFile $file 上传的文件
     * @return array
     * @throws \Exception
     */
    public function upload(array $data, UploadedFile $file)
    {
        // 根据资源类型决定上传路径
        $resourceType = $data['type'] ?? 'file';

        // 定义文件存储路径 - 直接使用public目录
        $path = 'uploads/' .  date('Ymd') . '/' . $resourceType;

        // 确保目录存在
        if (!is_dir(public_path($path))) {
            mkdir(public_path($path), 0755, true);
        }

        // 生成唯一文件名或使用自定义文件名
        $filename = $data['filename'] ?? null;
        if (!$filename) {
            $ext = $file->getClientOriginalExtension();
            $filename = Str::random(20) . '_' . time() . '.' . $ext;
        }

        // 直接将文件移动到public目录
        $file->move(public_path($path), $filename);

        // 生成可直接访问的URL
        $url = url($path . '/' . $filename);
        $filePath = $path . '/' . $filename;

        // 在这个地方添加如果上传的是视频，返回视频播放时长
        $result = [
            'type' => $resourceType,
            'url' => $url,
            'title' => $data['title'] ?? $file->getClientOriginalName(),
            'file_type' => $file->getClientOriginalExtension(),
            'original_name' => $file->getClientOriginalName(),
            'storage_driver' => 'public',
            'path' => $filePath,
        ];

        // 如果上传的是视频，使用FFmpeg获取视频播放时长
        $fileExtension = strtolower($file->getClientOriginalExtension());
        $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp', 'mpg', 'mpeg'];

        if (in_array($fileExtension, $videoExtensions)) {
            $fullPath = public_path($filePath);
            $duration = $this->getVideoDurationWithFFmpeg($fullPath);

            if ($duration !== null) {
                // 减去2秒冗余量，取整数，最小为0
                $adjustedDuration = max(0, floor($duration - 2));
                $result['duration'] = $adjustedDuration; // 视频时长（秒）
                $result['duration_formatted'] = $this->formatVideoDuration($adjustedDuration); // 格式化时长
            }
        }

        // 处理上传结果
        return $result;
    }

    /**
     * 获取视频资源信息
     *
     * @param int $id
     * @param array $params
     * @return array
     */
    public function videoInfo(int $id, array $params): array
    {
        $result = $this->repository->videoInfo($id);
        if (empty($result)) {
            return [];
        }

        $withProgress = $params['with_progress'] ?? false;

        if ($withProgress) {
            $result['progress'] = $this->repository->videoProgress($id, $params['student_id'], $params['unit_id']);
        }
        return $result;
    }

    /**
     * 获取文件资源信息
     *
     * @param int $id
     * @return array
     */
    public function fileInfo(int $id): array
    {
        $result = $this->repository->fileInfo($id);
        return $result;
    }

    /**
     * 保存视频进度
     *
     * @param int $id 视频ID
     * @param array $data 进度数据
     * @return array
     * @throws BizException
     */
    public function saveVideoProgress(int $id, array $data): array
    {
        // 检查视频是否存在
        $video = $this->repository->videoInfo($id);
        if (empty($video)) {
            BizException::throws(ErrorCode::VIDEO_NOT_FOUND, ErrorCode::VIDEO_NOT_FOUND->getMessage());
        }

        // 验证进度数据的合理性
        if ($data['position'] > $data['duration']) {
            BizException::throws(ErrorCode::VIDEO_PROGRESS_EXCEPTION, ErrorCode::VIDEO_PROGRESS_EXCEPTION->getMessage());
        }

        // 计算观看进度百分比
        $data['progress_percent'] = $data['duration'] > 0
            ? round(($data['position'] / $data['duration']) * 100)
            : 0;

        // 判断是否完成观看（通常认为看到90%以上可视为完成）
        $data['is_completed'] = $data['progress_percent'] >= 90 ? 1 : 0;

        // 添加更新时间
        $data['updated_at'] = now();
        // 添加创建者ID
        $data['creator_id'] = Auth::user()->id ?? 0;

        try {
            // 保存进度数据
            $result = $this->repository->saveVideoProgress($id, $data);
            return $result;
        } catch (\Exception $e) {
            BizException::throws(ErrorCode::VIDEO_SAVE_FAILED, ErrorCode::VIDEO_SAVE_FAILED->getMessage());
        }
    }

    /**
     * 保存视频资源
     *
     * @param array $data
     * @return array
     */
    public function saveVideoResource(array $data): array
    {
        try {
            $id = $data['id'] ?? null;
            $now = time();

            // 如果存在ID则更新,不存在则新增
            if ($id) {
                // 检查视频资源是否存在
                $video = VideoResource::find($id);
                if (empty($video)) {
                    BizException::throws(ErrorCode::RESOURCE_NOT_FOUND, ErrorCode::RESOURCE_NOT_FOUND->getMessage());
                }

                // 只更新不为null且发生变化的字段
                $updateData = [];
                foreach ($data as $key => $value) {
                    if ($value !== null && $video[$key] !== $value) {
                        $updateData[$key] = $value;
                    }
                }

                // 如果没有需要更新的字段,直接返回原数据
                if (empty($updateData)) {
                    return $video->toArray();
                }

                // 添加更新时间
                $updateData['updated_at'] = $now;

                // 更新视频资源
                VideoResource::where('id', $id)->update($updateData);

                // 记录行为日志
                SystemLog::create([
                    'action' => SystemLog::ACTION_UPDATE,
                    'target_type' => SystemLog::TARGET_TYPE_VIDEO,
                    'target_id' => $id,
                    'description' => '更新视频资源'
                ]);

                $video = VideoResource::find($id);
                return $video->toArray();
            } else {
                // 新增视频资源
                $data['created_at'] = $now;
                $data['updated_at'] = $now;
                $data['creator_id'] = Auth::user()->id ?? 0;

                // 创建视频资源
                $video = VideoResource::create($data);

                // 记录行为日志
                SystemLog::create([
                    'action' => SystemLog::ACTION_CREATE,
                    'target_type' => SystemLog::TARGET_TYPE_VIDEO,
                    'target_id' => $video->id,
                    'description' => '创建视频资源'
                ]);

                return $video->toArray();
            }
        } catch (\Exception $e) {
            Log::error('视频资源保存失败: ' . $e->getMessage());
            BizException::throws(ErrorCode::VIDEO_SAVE_FAILED, ErrorCode::VIDEO_SAVE_FAILED->getMessage());
        }
    }

    /**
     * 保存文件资源
     *
     * @param array $data
     * @return array
     */
    public function saveFileResource(array $data): array
    {
        try {
            $id = $data['id'] ?? null;
            $now = time();

            // 如果存在ID则更新,不存在则新增
            if ($id) {
                // 检查文件资源是否存在
                $fileResource = UnitFile::find($id);
                if (empty($fileResource)) {
                    BizException::throws(ErrorCode::FILE_RESOURCE_NOT_FOUND, ErrorCode::FILE_RESOURCE_NOT_FOUND->getMessage());
                }

                // 只更新不为null且发生变化的字段
                $updateData = [];
                foreach ($data as $key => $value) {
                    if ($value !== null && $fileResource[$key] !== $value) {
                        $updateData[$key] = $value;
                    }
                }

                // 如果没有需要更新的字段,直接返回原数据
                if (empty($updateData)) {
                    return $fileResource->toArray();
                }

                // 添加更新时间
                $updateData['updated_at'] = $now;

                // 更新文件资源
                $result = UnitFile::where('id', $id)->update($updateData);

                // 记录行为日志
                SystemLog::create([
                    'action' => SystemLog::ACTION_UPDATE,
                    'target_type' => SystemLog::TARGET_TYPE_FILE,
                    'target_id' => $id,
                    'description' => '更新文件资源'
                ]);

                $fileResource = UnitFile::find($id);

                return $fileResource->toArray();
            } else {
                // 新增文件资源
                $data['created_at'] = $now;
                $data['updated_at'] = $now;
                $data['creator_id'] = Auth::user()->id ?? 0;

                // 创建文件资源
                $fileResource = UnitFile::create($data);

                // 记录行为日志
                SystemLog::create([
                    'action' => SystemLog::ACTION_CREATE,
                    'target_type' => SystemLog::TARGET_TYPE_FILE,
                    'target_id' => $fileResource->id,
                    'description' => '创建文件资源'
                ]);

                return $fileResource->toArray();
            }
        } catch (\Exception $e) {
            Log::error('文件资源保存失败: ' . $e->getMessage());
            BizException::throws(ErrorCode::FILE_SAVE_FAILED, ErrorCode::FILE_SAVE_FAILED->getMessage());
        }
    }


    /**
     * 保存文件资源
     *
     * @param array $data
     * @return array
     */
    /**
     * 保存测验资源
     *
     * @param array $data 测验数据
     * @return array 保存后的测验数据
     */
    public function saveQuiz(array $data): array
    {
        try {
            // 获取id，如果不存在则为null
            $id = $data['id'] ?? null;
            // 获取当前时间戳
            $now = time();

            if ($id) {
                // 更新测验资源
                $updateData = $data;
                $updateData['updated_at'] = $now;

                Quiz::where('id', $id)->update($updateData);

                // 记录更新日志
                SystemLog::create([
                    'action' => SystemLog::ACTION_UPDATE,
                    'target_type' => SystemLog::TARGET_TYPE_QUIZ,
                    'target_id' => $id,
                    'description' => '更新测验资源'
                ]);

                $quiz = Quiz::find($id);
                return $quiz->toArray();
            } else {
                // 新增测验资源
                $data['created_at'] = $now;
                $data['updated_at'] = $now;
                $data['creator_id'] = Auth::user()->id ?? 0;

                $quiz = Quiz::create($data);

                // 记录创建日志
                SystemLog::create([
                    'action' => SystemLog::ACTION_CREATE,
                    'target_type' => SystemLog::TARGET_TYPE_QUIZ,
                    'target_id' => $quiz->id,
                    'description' => '创建测验资源'
                ]);

                return $quiz->toArray();
            }
        } catch (\Exception $e) {
            Log::error('测验保存失败: ' . $e->getMessage());
            BizException::throws(ErrorCode::QUIZ_SAVE_FAILED, ErrorCode::QUIZ_SAVE_FAILED->getMessage());
        }
    }

    /**
     * 保存反思报告
     *
     * @param array $data
     * @return array
     */
    public function saveReflection(array $data): array
    {
        try {
            // 获取id，如果不存在则为null
            $id = $data['id'] ?? null;
            // 获取当前时间戳
            $now = time();

            if ($id) {
                // 更新反思报告
                $updateData = $data;
                $updateData['updated_at'] = $now;

                ReflectionReport::where('id', $id)->update($updateData);

                // 记录更新日志
                SystemLog::create([
                    'action' => SystemLog::ACTION_UPDATE,
                    'target_type' => SystemLog::TARGET_TYPE_REFLECTION,
                    'target_id' => $id,
                    'description' => '更新反思报告'
                ]);

                $reflection = ReflectionReport::find($id);
                return $reflection->toArray();
            } else {
                // 新增反思报告
                $data['created_at'] = $now;
                $data['updated_at'] = $now;
                $data['creator_id'] = Auth::user()->id ?? 0;

                $reflection = ReflectionReport::create($data);

                // 记录创建日志
                SystemLog::create([
                    'action' => SystemLog::ACTION_CREATE,
                    'target_type' => SystemLog::TARGET_TYPE_REFLECTION,
                    'target_id' => $reflection->id,
                    'description' => '创建反思报告'
                ]);

                return $reflection->toArray();
            }
        } catch (\Exception $e) {
            Log::error('反思报告保存失败: ' . $e->getMessage());
            BizException::throws(ErrorCode::REFLECTION_SAVE_FAILED, ErrorCode::REFLECTION_SAVE_FAILED->getMessage());
        }
    }

    /**
     * 使用FFmpeg获取视频时长
     *
     * @param string $filePath 视频文件完整路径
     * @return float|null 视频时长（秒），获取失败返回null
     */
    private function getVideoDurationWithFFmpeg(string $filePath): ?float
    {
        try {
            // 检查文件是否存在
            if (!file_exists($filePath)) {
                return null;
            }

            // 检查FFmpeg是否可用
            if (!$this->checkFFmpegAvailable()) {
                return null;
            }

            // 使用ffprobe获取视频时长
            $command = sprintf(
                'ffprobe -v quiet -show_entries format=duration -of csv="p=0" %s 2>/dev/null',
                escapeshellarg($filePath)
            );

            $output = shell_exec($command);

            if ($output && is_numeric(trim($output))) {
                return (float) trim($output);
            }

            return null;
        } catch (\Exception $e) {
            // 记录错误但不抛出异常，避免影响文件上传
            error_log("FFmpeg获取视频时长失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 检查FFmpeg是否可用
     *
     * @return bool
     */
    private function checkFFmpegAvailable(): bool
    {
        // 检查ffprobe命令是否存在
        $output = shell_exec('which ffprobe 2>/dev/null');
        return !empty($output);
    }

    /**
     * 格式化视频时长
     *
     * @param int $seconds 时长（秒，整数）
     * @return string 格式化后的时长
     */
    private function formatVideoDuration(int $seconds): string
    {
        $hours = intval($seconds / 3600);
        $minutes = intval(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
        } else {
            return sprintf('%02d:%02d', $minutes, $remainingSeconds);
        }
    }

    /**
     * 根据类型删除资源
     * 
     * @param int $id 资源ID
     * @param string $type 资源类型
     * @return array
     * @throws BizException
     */
    public function delByType(int $id, string $type): array
    {
        try {
            $model = null;
            $logType = '';

            switch ($type) {
                case 'video':
                    $model = VideoResource::find($id);
                    $logType = SystemLog::TARGET_TYPE_VIDEO;
                    break;
                case 'file':
                    $model = UnitFile::find($id);
                    $logType = SystemLog::TARGET_TYPE_FILE;
                    break;
                case 'folder':
                    $model = FolderResource::find($id);
                    $logType = SystemLog::TARGET_TYPE_FOLDER;
                    break;
                case 'quiz':
                    $model = Quiz::find($id);
                    $logType = SystemLog::TARGET_TYPE_QUIZ;
                    break;
                case 'reflection':
                    $model = ReflectionReport::find($id);
                    $logType = SystemLog::TARGET_TYPE_REFLECTION;
                    break;
                default:
                    throw new BizException(ErrorCode::INVALID_RESOURCE_TYPE);
            }

            if (!$model) {
                throw new BizException(ErrorCode::RESOURCE_NOT_FOUND);
            }

            // 软删除资源
            $model->deleted_at = now();
            $result = $model->save();

            if (!$result) {
                throw new BizException(ErrorCode::RESOURCE_DELETE_FAILED);
            }

            // 记录删除日志
            SystemLog::create([
                'action' => SystemLog::ACTION_DELETE,
                'target_type' => $logType,
                'target_id' => $id,
                'description' => '删除' . $type . '资源'
            ]);

            return [
                'success' => true,
                'message' => T('Course::message.resource_deleted_success')
            ];

        } catch (\Exception $e) {
            Log::error('Delete resource failed', [
                'id' => $id,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            throw new BizException(ErrorCode::RESOURCE_DELETE_FAILED);
        }
    }
}
