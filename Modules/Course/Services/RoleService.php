<?php

namespace Modules\Course\Services;

use Modules\Course\Models\Role;
use Modules\Course\Models\User;
use Bingo\Exceptions\BizException;
use Modules\Course\Enums\ErrorCode;
use Modules\Course\Models\SystemLog;
use Modules\Course\Models\CourseUser;
use Modules\Course\Domain\Repositories\RoleRepository;

class RoleService
{
    protected RoleRepository $repository;
    protected PermissionService $permissionService;

    public function __construct(RoleRepository $repository, PermissionService $permissionService)
    {
        $this->repository = $repository;
        $this->permissionService = $permissionService;
    }

    /**
     * 获取角色列表
     * 
     * @param array $params
     * @return array
     */
    public function list(array $params): array
    {
        return $this->repository->list($params);
    }

    /**
     * 获取角色信息
     *
     * @param int $id
     * @return array
     * @throws BizException
     */
    public function info(int $id): array
    {
        $result = $this->repository->info($id);
        if (empty($result)) {
            BizException::throws(ErrorCode::ROLE_NOT_FOUND, ErrorCode::ROLE_NOT_FOUND->getMessage());
        }
        return $result->toArray();
    }

    /**
     * 创建或更新角色
     * 
     * @param array $params
     * @return array
     * @throws BizException
     */
    public function save(array $params): array
    {
        try {
            // 开启事务
            return \DB::transaction(function () use ($params) {
                // 保存角色基本信息
                $role = $this->repository->save($params);

                // 处理角色权限
                if (isset($params['permissions'])) {
                    // 按需更新角色权限，性能更好
                    $this->repository->updateRolePermissions($role['id'], $params['permissions']);
                }

                // 记录行为日志
                SystemLog::create([
                    'action' => !empty($params['id']) ? SystemLog::ACTION_UPDATE : SystemLog::ACTION_CREATE,
                    'target_type' => SystemLog::TARGET_TYPE_ROLE,
                    'target_id' => $role['id'],
                    'description' => !empty($params['id']) ? '角色更新' : '角色创建'
                ]);

                return $role;
            });
        } catch (\Exception $e) {
            \Log::error('保存角色失败', ['error' => $e->getMessage(), 'params' => $params]);
            BizException::throws(ErrorCode::ROLE_SAVE_FAILED, ErrorCode::ROLE_SAVE_FAILED->getMessage());
        }
    }

    /**
     * 删除角色
     * 
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        return \DB::transaction(function () use ($id) {
            // 先删除角色关联的权限
            $this->repository->deleteRolePermissions($id);

            // 再删除角色
            $ok = $this->repository->delete($id);

            if ($ok) {
                // 记录行为日志
                SystemLog::create([
                    'action' => SystemLog::ACTION_DELETE,
                    'target_type' => SystemLog::TARGET_TYPE_ROLE,
                    'target_id' => $id,
                    'description' => '角色删除'
                ]);
            }
            return $ok;
        });
    }

    /**
     * 开关角色状态
     * 
     * @param int $id
     * @return bool
     */
    public function toggle(int $id): bool
    {
        $ok = $this->repository->toggle($id);
        if ($ok) {
            // 记录行为日志
            SystemLog::create([
                'action' => SystemLog::ACTION_UPDATE,
                'target_type' => SystemLog::TARGET_TYPE_ROLE,
                'target_id' => $id,
                'description' => '角色状态开关'
            ]);
        }
        return $ok;
    }


    /**
     * 根据课程ID获取角色列表
     * 
     * @param int $courseId 课程ID
     * @param int $roleType 角色类型
     * @return array 角色列表
     */
    public function getRoleListByCourseId(int $courseId, int $roleType = 0): array
    {
        // 获取课程中的所有用户
        $courseUsers = CourseUser::where('course_id', $courseId)
            ->select('user_id')
            ->get();
        if (empty($courseUsers)) {
            return [];
        }

        // 获取所有用户ID
        $userIds = $courseUsers->pluck('user_id')->unique()->toArray();
        if (empty($userIds)) {
            return [];
        }

        //查询用户信息
        $users = User::whereIn('id', $userIds)
            ->select('id', 'role_id')
            ->get();
        if (empty($users)) {
            return [];
        }

        // 获取所有角色ID
        $roleIds = $users->pluck('role_id')->unique()->toArray();
        if (empty($roleIds)) {
            return [];
        }

        // 获取角色信息并关联用户信息
        $query = Role::whereIn('id', $roleIds)
            ->select('id', 'name', 'description');

        // 根据角色类型查询
        if (!empty($roleType)) {
            $query->where('type', $roleType);
        }

        // 关联用户信息
        $roles = $query->with(['users' => function ($query) use ($userIds) {
            $query->whereIn('id', $userIds)
                ->select('id', 'role_id', 'first_name', 'last_name', 'avatar_url');
        }])
            ->get()
            ->toArray();

        return $roles;
    }
}
