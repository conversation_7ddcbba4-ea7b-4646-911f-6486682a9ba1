<?php

namespace Modules\Course\Services;

use Bingo\Exceptions\BizException;
use Illuminate\Support\Facades\DB;
use Modules\Course\Enums\ErrorCode;
use Modules\Course\Models\FolderResource;

/**
 * 文件夹资源服务类
 * 
 * 负责处理文件夹资源相关的业务逻辑
 */
class FolderService
{
    /**
     * 获取单元的文件夹树结构
     * 
     * @param int $rootDirectoryId 根目录ID
     * @return array 树状结构的文件夹资源
     */
    public function getFolderTree(int $rootDirectoryId)
    {
        // 首先获取根目录，确保它存在
        $rootFolder = FolderResource::find($rootDirectoryId);
        if (!$rootFolder) {
            return [];
        }
        
        // 获取所有文件夹资源
        $allFolders = FolderResource::orderBy('sort_order', 'asc')->get()->toArray();
        if (empty($allFolders)) {
            return [];
        }
        
        // 构建完整的资源树
        return $this->buildResourceTree($allFolders, $rootDirectoryId);
    }
    
    /**
     * 递归构建资源树
     *
     * @param array $resources 所有资源列表
     * @param int $rootId 根资源ID
     * @return array 构建好的资源树
     */
    private function buildResourceTree(array $resources, int $rootId)
    {
        // 按照parent_id对资源进行分组
        $resourceMap = [];
        $rootNode = null;
        
        // 第一步：构建资源映射和查找根节点
        foreach ($resources as $resource) {
            // 找到根节点
            if ($resource['id'] == $rootId) {
                $rootNode = $resource;
                continue;
            }
            
            // 按照parent_id分组资源
            $parentId = $resource['parent_id'];
            if (!isset($resourceMap[$parentId])) {
                $resourceMap[$parentId] = [];
            }
            $resourceMap[$parentId][] = $resource;
        }
        
        // 如果没有找到根节点，返回空数组
        if (!$rootNode) {
            return [];
        }
        
        // 第二步：递归构建树结构
        $result = $this->buildTreeNode($rootNode, $resourceMap);
        
        return $result;
    }
    
    /**
     * 递归构建单个节点及其子节点
     *
     * @param array $node 当前节点
     * @param array $resourceMap 按parent_id分组的资源映射
     * @return array 构建好的节点及其子节点
     */
    private function buildTreeNode(array $node, array $resourceMap)
    {
        // 复制节点数据，避免修改原始数据
        $result = $node;
        
        // 查找当前节点的子节点
        $nodeId = $node['id'];
        if (isset($resourceMap[$nodeId]) && !empty($resourceMap[$nodeId])) {
            $children = [];
            
            // 处理所有子节点
            foreach ($resourceMap[$nodeId] as $childNode) {
                $children[] = $this->buildTreeNode($childNode, $resourceMap);
            }
            
            // 对子节点按sort_order排序
            usort($children, function($a, $b) {
                return $a['sort_order'] <=> $b['sort_order'];
            });
            
            $result['children'] = $children;
        }
        
        // 根据资源类型处理字段
        if ($result['resource_type'] === FolderResource::TYPE_FOLDER) {
            // 如果是文件夹类型，移除文件相关字段
            unset($result['file_url']);
            unset($result['file_type']);
        }
        
        // 移除不需要的字段
        unset($result['parent_id']);
        unset($result['tree_id']);
        
        return $result;
    }
    
    /**
     * 保存文件夹资源
     * 
     * @param array $data 文件夹资源数据
     * @param int $maxDepth 最大嵌套层级，默认为20
     * @return array 保存后的文件夹资源
     * @throws BizException 当保存失败时抛出异常
     */
    public function save(array $data, int $maxDepth = 20)
    {
        // 检查data的嵌套层级是否超过maxDepth
        if ($this->checkNestedDepth($data, 0, $maxDepth)) {
            throw new \Exception("超过最大嵌套层级限制: {$maxDepth}");
        }

        // 使用事务确保数据一致性
        return DB::transaction(function () use ($data, $maxDepth) {
            try {
                // 收集需要创建和更新的资源
                $toCreate = [];
                $toUpdate = [];
                
                // 保存父文件夹
                if (isset($data['id'])) {
                    $folder = FolderResource::findOrFail($data['id']);
                    // 更新文件夹基本信息，排除children字段
                    $folderData = array_diff_key($data, ['children' => []]);
                    
                    // 如果parent_id为null或0，说明是根资源，设置tree_id为null
                    if (empty($folderData['parent_id'])) {
                        $folderData['tree_id'] = null;
                    }
                    
                    $folder->update($folderData);
                } else {
                    // 初始化为文件夹类型
                    $data['resource_type'] = FolderResource::TYPE_FOLDER;
                    
                    // 如果parent_id为null或0，说明是根资源，设置tree_id为null
                    if (empty($data['parent_id'])) {
                        $data['tree_id'] = null;
                    }
                    
                    $folder = FolderResource::create($data);
                }
                
                // 处理子资源
                if (isset($data['children']) && is_array($data['children'])) {
                    // 确定根资源ID - 如果当前资源是根资源(tree_id为null)，则使用其自身ID作为子资源的tree_id
                    // 否则保持其自身的tree_id
                    $rootId = $folder->tree_id === null ? $folder->id : $folder->tree_id;
                    
                    // 预处理所有子资源，收集批量操作数据
                    $this->processChildren($folder, $data['children'], 1, $maxDepth, $toCreate, $toUpdate, $rootId);
                    
                    // 批量创建新资源
                    if (!empty($toCreate)) {
                        // 分批处理以避免过大的单个查询
                        foreach (array_chunk($toCreate, 100) as $chunk) {
                            FolderResource::insert($chunk);
                        }
                    }
                    
                    // 批量更新现有资源
                    if (!empty($toUpdate)) {
                        foreach ($toUpdate as $id => $updateData) {
                            // 使用whereIn批量更新相同结构的数据
                            FolderResource::where('id', $id)->update($updateData);
                        }
                    }
                }
                
                return $folder->toArray();
            } catch (\Exception $e) {
                \Log::error('保存文件夹资源失败: ' . $e->getMessage(), ['data' => $data]);
                BizException::throws(ErrorCode::SAVE_FAILED, ErrorCode::SAVE_FAILED->getMessage());
            }
        });
    }
    
    /**
     * 递归处理子资源
     * 
     * @param FolderResource $parent 父资源
     * @param array $children 子资源数据
     * @param int $currentDepth 当前嵌套深度
     * @param int $maxDepth 最大嵌套深度
     * @param array &$toCreate 要创建的资源集合
     * @param array &$toUpdate 要更新的资源集合
     * @param int $rootId 根资源ID
     * @throws \Exception 当超过最大嵌套深度时抛出异常
     * @return array 处理的资源ID列表
     */
    private function processChildren(FolderResource $parent, array $children, int $currentDepth, int $maxDepth, array &$toCreate, array &$toUpdate, int $rootId)
    {
        // 检查嵌套层级
        if ($currentDepth > $maxDepth) {
            throw new \Exception("超过最大嵌套层级限制: {$maxDepth}");
        }
        
        $processedIds = [];
        $now = now()->timestamp;
        
        // 获取现有的子资源ID与类型的映射
        $existingChildren = FolderResource::where('parent_id', $parent->id)
            ->select(['id', 'resource_type'])
            ->get()
            ->keyBy('id')
            ->toArray();
        
        foreach ($children as $index => $childData) {
            // 设置通用字段
            $childData['parent_id'] = $parent->id;
            $childData['unit_id'] = $parent->unit_id;
            $childData['sort_order'] = $childData['sort_order'] ?? $index;
            
            // 设置tree_id为根资源的ID
            $childData['tree_id'] = $rootId;
            
            // 需要递归处理的子资源数据 - 暂存后移除，避免不必要的数据库存储
            $nestedChildren = $childData['children'] ?? null;
            unset($childData['children']);
            
            // 准备创建或更新数据
            if (isset($childData['id'])) {
                // 资源已存在
                $childId = $childData['id'];
                $childData['updated_at'] = $now;
                
                // 记录到更新列表
                $toUpdate[$childId] = $childData;
                $processedIds[] = $childId;
                
                // 获取资源类型
                $resourceType = $existingChildren[$childId]['resource_type'] ?? null;
                
                // 递归处理子资源
                if ($resourceType === FolderResource::TYPE_FOLDER && $nestedChildren) {
                    // 由于需要父级对象，先获取实例
                    $child = FolderResource::find($childId);
                    $this->processChildren($child, $nestedChildren, $currentDepth + 1, $maxDepth, $toCreate, $toUpdate, $rootId);
                }
            } else {
                // 新资源
                $childData['created_at'] = $now;
                $childData['updated_at'] = $now;
                
                // 记录到创建列表
                $toCreate[] = $childData;
                
                // 对于文件夹类型，如果有子资源，需要先创建父资源获取ID后再处理子资源
                // 这部分无法完全批量化，因为子资源需要父级ID
                if ($childData['resource_type'] === FolderResource::TYPE_FOLDER && $nestedChildren) {
                    // 创建父文件夹
                    $child = FolderResource::create($childData);
                    $processedIds[] = $child->id;
                    
                    // 从创建列表中移除，因为已单独创建
                    array_pop($toCreate);
                    
                    // 递归处理子资源
                    $this->processChildren($child, $nestedChildren, $currentDepth + 1, $maxDepth, $toCreate, $toUpdate, $rootId);
                }
            }
        }
        
        return $processedIds;
    }

    /**
     * 下载文件夹资源
     * 
     * @param int $id 文件夹资源ID
     * @return array 下载资源信息
     * @throws \Exception 当文件夹资源不存在时抛出异常
     */
    public function download(int $id)
    {

        $folder = FolderResource::find($id);
        if (empty($folder)) {
            BizException::throws(ErrorCode::RESOURCE_NOT_FOUND, ErrorCode::RESOURCE_NOT_FOUND->getMessage());
        }

        //检查是否已经存在压缩文件
        if (!empty($folder->file_url)) {
            return [
                'file_url' => $folder->file_url
            ];
        }

        // 获取资源树
        $folderTree = $this->getFolderTree($id);
        if (empty($folderTree)) {
            BizException::throws(ErrorCode::RESOURCE_NOT_FOUND, ErrorCode::RESOURCE_NOT_FOUND->getMessage());
        }
        
        // 在public目录下创建zip文件夹
        $tempPath = 'uploads/' . date('Ymd').'/zip';
        $tempDir = public_path($tempPath);
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        // 创建ZIP文件
        $zipFile = $tempDir . '/'. $folderTree['title'] .'.zip';
        $zipfileUrl = asset($tempPath . '/'. $folderTree['title'] .'.zip');
        $zip = new \ZipArchive();
        if ($zip->open($zipFile, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) !== true) {
            throw new \Exception('无法创建ZIP文件');
        }
        // 将根文件夹添加到ZIP
        $this->addFolderToZip($zip, $folderTree, '');

        // 关闭ZIP文件
        $zip->close();

        // 更新根资源的file_url为ZIP文件路径
        FolderResource::where('id', $id)->update(['file_url' => $zipfileUrl]);

        return [
            'file_url' => $zipfileUrl
        ];
    }
    
    /**
     * 递归将文件夹内容添加到ZIP文件
     * 
     * @param \ZipArchive $zip ZIP文档对象
     * @param FolderResource $folder 文件夹资源
     * @param string $zipPath ZIP内部路径
     * @return void
     */
    private function addFolderToZip(\ZipArchive $zip, array $folder, string $zipPath)
    {   
        // 获取当前目录的所有子资源
        $children = $folder['children'] ?? [];
        
        // 当前文件夹路径
        $currentPath = $zipPath . ($zipPath ? '/' : '') . $folder['title'];
        
        // 如果不是根目录，创建文件夹
        if ($currentPath) {
            $zip->addEmptyDir($currentPath);
        }
        
        foreach ($children as $child) {
            if ($child['resource_type'] === FolderResource::TYPE_FOLDER) {
                // 递归处理子文件夹
                $this->addFolderToZip($zip, $child, $currentPath);
            } else {
                // 处理文件
                $fileUrl = $child['file_url'];
                
                // 检查文件是否存在
                // 处理文件路径，移除域名部分
                $fileUrl = preg_replace('/^https?:\/\/[^\/]+/', '', $fileUrl);
                
                // 检查文件是否存在
                if (file_exists(public_path($fileUrl))) {
                    // 本地文件
                    // 获取文件扩展名
                    $extension = pathinfo($child['file_url'], PATHINFO_EXTENSION);
                    // 构建完整的文件名(包含扩展名)
                    $fileName = $currentPath . '/' . $child['title'] . '.' . $extension;
                    // 添加文件到ZIP
                    $zip->addFile(public_path($fileUrl), $fileName);
                } else {
                    throw new \Exception('文件不存在: ' . $fileUrl);
                }
            }
        }
    }
    
    /**
     * 临时文件列表，用于清理
     * 
     * @var array
     */
    private $tempFiles = [];
    
    /**
     * 析构函数，清理临时文件
     */
    public function __destruct()
    {
        // 清理临时文件
        foreach ($this->tempFiles as $file) {
            if (file_exists($file)) {
                unlink($file);
            }
        }
    }

    /**
     * 检查数据的嵌套深度是否超过最大限制
     * 
     * @param array $data 要检查的数据
     * @param int $currentDepth 当前深度
     * @param int $maxDepth 最大允许深度
     * @return bool 如果超过最大深度则返回true
     */
    private function checkNestedDepth(array $data, int $currentDepth, int $maxDepth): bool
    {
        if ($currentDepth > $maxDepth) {
            return true;
        }
        
        if (isset($data['children']) && is_array($data['children'])) {
            foreach ($data['children'] as $child) {
                if ($this->checkNestedDepth($child, $currentDepth + 1, $maxDepth)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * 获取所有根目录资源
     * 
     * @param array $params 请求参数
     * @return array 根目录资源列表
     */
    public function rootList(array $params)
    {

        $page = (int)($params['page'] ?? 1);
        $limit = (int)($params['limit'] ?? 10);
        $unitId = (int)($params['unit_id'] ?? 0);

        $query = FolderResource::whereNull('parent_id');
        if ($unitId > 0) {
            $query->where('unit_id', $unitId);
        }
        
        $query->orderBy('id', 'desc');
        $query->offset(($page - 1) * $limit)->limit($limit);
        $list = $query->get()->toArray();
        return $list;
        
    }
} 