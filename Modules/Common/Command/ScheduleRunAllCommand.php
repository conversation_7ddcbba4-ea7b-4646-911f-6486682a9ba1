<?php

namespace Modules\Common\Command;

use Bingo\BingoStart;
use Bingo\Core\Util\ArrayUtil;
use Bingo\Core\Util\FileUtil;
use Bingo\Core\Util\ShellUtil;
use Bingo\Core\Util\TimeUtil;
use bingostart\Common\Util\CacheUtil;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ScheduleRunAllCommand extends Command
{
    protected $signature = 'bingo:schedule-run-all {php} {dir}';

    public function handle(): void
    {
        $php = $this->argument('php');
        $dir = $this->argument('dir');
        $hash = md5($php.':'.$dir);
        $projects = CacheUtil::remember(
            "Common:ScheduleRunAll:Projects:".$hash,
            3600,
            function () use ($dir) {
                $projects = FileUtil::listFiles($dir);
                $projects = array_filter($projects, function ($p) {
                    return $p['isDir']
                        && @file_exists($p['pathname'].'/artisan')
                        && @file_exists($p['pathname'].'/.env')
                        && ! Str::startsWith($p['filename'], '_delete.')
                        && @file_exists($p['pathname'].'/vendor/bingostart-'.BingoStart::env());
                });
                shuffle($projects);
                return ArrayUtil::keepItemsKeys($projects, ['pathname']);
            }
        );
        foreach ($projects as $project) {
            $start = TimeUtil::millitime();
            $command = "$php {$project['pathname']}/artisan schedule:run";
            Log::info("Common.ScheduleRunAllCommand.Run - $command");
            $result = ShellUtil::runInNewProcess($command, false);
            $result = str_replace([
                "\r"
            ], "", $result);
            $result = str_replace("\n", " ", $result);
            $result = str_replace("Running scheduled command: Closure", "√", $result);
            $result = str_replace("No scheduled commands are ready to run.", "〇", $result);
            $ms = TimeUtil::millitime() - $start;
            Log::info("Common.ScheduleRunAllCommand.Result - $result - $ms ms");
        }
    }
}
