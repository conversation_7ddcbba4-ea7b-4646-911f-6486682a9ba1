<?php

namespace Modules\Common\Admin\Controllers;

use Exception;
use Modules\Common\Repository\Options\Factory;
use Modules\Common\Services\OptionsService;

class OptionController
{
    private OptionsService $optionsService;
    public function __construct(OptionsService $optionsService)
    {
        $this->optionsService = $optionsService;
    }

    /**
     * 通过传递的不同name获取获取选项
     * @param $name
     * @param Factory $factory
     * @return array
     * @throws Exception
     */
    public function index($name, Factory $factory): array
    {
        return $factory->make($name)->get();
    }

    /**
     * 获取所有启用的模块
     * @return array
     */
    public function getModules(): array
    {
        return $this->optionsService->getAllEnabledModules();
    }

    /**
     * 获取所有启用的模块配置
     * @return array
     */

    public function getModulesConfig(): array
    {
        return $this->optionsService->getAllEnabledModulesConfig();
    }
}
