<?php

namespace Modules\Common\Admin\Controllers;

use <PERSON><PERSON>\Module\ModuleBaseController;
use Modules\System\Domain\Renewal;

class DashboardController extends ModuleBaseController
{
    public function index(): array
    {
        $renewal = app(Renewal::class);
        $renewalInfo = $renewal->getRenewal();

        $UsefulTool = ['Google Analytics', 'Search Console', 'Image Purchase', 'Compression Tool', 'Pop-Up Messages'];

        foreach ($UsefulTool as $k => $v) {
            $UsefulTools[] = [
                'title' => $v,
                'icon' => '联络后端约定',
                'url' => '#',
                'sort' => ($k + 1)
            ];
        }

        return [
            'Support' => [
                'avatar' => $renewalInfo['data']['cs_person']['cs_thumbnail'] ?? '',
                'username' => $renewalInfo['data']['cs_person']['cs_name'] ?? '',
                'is_online' => true,
                'email' => $renewalInfo['data']['cs_person']['cs_email'] ?? '',
                'whatsapp' => $renewalInfo['data']['cs_person']['cs_whatsapp'] ?? '',
                'cs_phone' => $renewalInfo['data']['cs_person']['cs_phone'] ?? '',
                'cs_time' => $renewalInfo['data']['cs_person']['cs_time'] ?? '',
            ],
            'UsefulTools' => $UsefulTools,
        ];
    }
}
