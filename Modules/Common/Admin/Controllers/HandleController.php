<?php

namespace Modules\Common\Admin\Controllers;

use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;
use Crypt;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Modules\Common\Support\Upload\Uploader;

class HandleController
{
    public function action()
    {
        try {
            $data = request()?->all();
            $validator = validator($data, [
                'action' => 'required|string',
                'class' => 'required|string',
                'params' => 'array',
                'data' => 'array',
            ]);
            if ($validator->fails()) {
                abort(400, $validator->errors()->first());
            }
            $class = Crypt::decryptString($data['class']);
            $action = $data['action'];
            $params = $data['params'];
            $data = $data['data'];
            return (new $class())->$action($params, $data);
        } catch (Exception $e) {
            BizException::throws(Code::FAILED, $e->getMessage());
        }
    }

    public function uploadImage(Request $request, Uploader $uploader): array
    {
        try {
            validator($request->all(), [
                'file' => 'mimes:'.config('bingo.upload.mimes', 'jpeg,bmp,png,gif,jpg')
            ]);
            $res = $uploader->upload($request->file('file'));
            return [
                'value' => $res['path'],
                'filename' => $res['originalName'],
                'url' => $res['path'],
                'link' => $res['path'],
            ];
        } catch (Exception $e) {
            BizException::throws(Code::FAILED, $e->getMessage());
        }
    }

    public function uploadFile(Request $request): array
    {
        try {
            validator($request->all(), [
                'file' => 'mimes:'.config('bingo.upload.file_mimes', '')
            ]);
            return $this->upload($request);

        } catch (Exception $e) {
            BizException::throws(Code::FAILED, $e->getMessage());
        }
    }


    protected function upload(Request $request)
    {
        try {
            $file = $request->file('file');
            $type = $request->file('type');
            $path = $request->input('path', 'images');
            $uniqueName = $request->boolean('unique_name', config('bingo.upload.uniqueName', false));
            $disk = config('bingo.upload.disk');
            $name = $file->getClientOriginalName();
            if ($uniqueName) {
                $path = $file->store($path, $disk);
            } else {
                $path = $file->storeAs($path, $name, $disk);
            }
            abort_if(! $path, 400, '上传失败');

            $url = Storage::disk($disk)->url($path);

            return [
                'value' => $path,
                'filename' => $name,
                'url' => $url,
                'link' => $url,
            ];
        } catch (Exception $e) {
            BizException::throws(Code::FAILED, $e->getMessage());
        }

    }
}
