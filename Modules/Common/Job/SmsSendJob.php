<?php

namespace Modules\Common\Job;

use Bingo\Core\Job\BaseJob;
use Bingo\Core\Util\SerializeUtil;
use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;
use Modules\Common\Log\Logger;
use Modules\Common\Provider\SmsSender\SmsSenderProvider;

class SmsSendJob extends BaseJob
{
    public string $phone;
    public $template;
    public $templateData;

    public static function create($phone, $template, $templateData): void
    {
        $job = new static();
        $job->phone = $phone;
        $job->template = $template;
        $job->templateData = $templateData;
        app('Illuminate\Contracts\Bus\Dispatcher')->dispatch($job);
    }

    public function handle(): void
    {
        $logData = $this->phone.' - '.$this->template.' - '.SerializeUtil::jsonEncode($this->templateData, JSON_UNESCAPED_UNICODE);
        Logger::info('Sms', 'Start', $logData);
        $provider = app()->config->get('SmsSenderProvider');
        try {
            BizException::throwsIfEmpty('短信发送未设置', $provider);
            $ret = SmsSenderProvider::get($provider)->send($this->phone, $this->template, $this->templateData);
            BizException::throwsIfResponseError($ret, Code::FAILED);
            Logger::info('Sms', 'End', $this->phone.' - '.SerializeUtil::jsonEncode($ret, JSON_UNESCAPED_UNICODE));
        } catch (BizException $e) {
            Logger::error('Sms', 'Error', $this->phone.' - '.$e->getMessage());
        }
    }
}
