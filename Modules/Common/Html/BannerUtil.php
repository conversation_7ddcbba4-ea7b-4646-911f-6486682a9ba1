<?php

namespace Modules\Common\Html;

use Modules\Common\Provider\Banner\BannerProvider;

class BannerUtil
{
    public static function getBannerData($bannerCode): array|string
    {
        return self::handle($bannerCode, 'raw');
    }

    public static function renderBanner($bannerCode, $viewPath = null): array|string
    {
        return self::handle($bannerCode, 'view', $viewPath);
    }

    public static function handle($bannerCode, $identifier, $viewPath = null): array|string
    {
        $provider = BannerProvider::get();

        return $provider->display($bannerCode, $identifier, $viewPath);
    }
}
