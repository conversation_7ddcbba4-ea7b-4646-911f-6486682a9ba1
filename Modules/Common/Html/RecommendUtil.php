<?php

namespace Modules\Common\Html;

use Modules\Common\Provider\Recommend\RecommendProvider;

class RecommendUtil
{
    public static function renderRecommend($recommendCode, $algorithmType, $count, $viewPath = null): array|string
    {
        return self::handle($recommendCode, 'view', $algorithmType, $count, $viewPath);
    }

    public static function handle($recommendCode, $identifier, $algorithmType, $count, $viewPath = null): array|string
    {
        $provider = RecommendProvider::get();
        return $provider->display($recommendCode, $identifier, $algorithmType, $count, $viewPath);
    }
}
