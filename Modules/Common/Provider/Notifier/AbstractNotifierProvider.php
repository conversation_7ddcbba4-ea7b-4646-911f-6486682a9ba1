<?php

namespace Modules\Common\Provider\Notifier;

/**
 * 消息通知抽象类，用于实现不同的消息通知方式
 * Class AbstractNotifierProvider
 * @package Modules\Common\Provider\Notifier
 */
abstract class AbstractNotifierProvider
{
    public function name(): null
    {
        return null;
    }

    public function title(): null
    {
        return null;
    }

    abstract public function notify($biz, $title, $content, $param = []);
}
