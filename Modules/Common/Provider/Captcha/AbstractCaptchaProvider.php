<?php

namespace Modules\Common\Provider\Captcha;

/**
 * Class AbstractCaptchaProvider
 * @package Modules\Common\Provider\Captcha
 */
abstract class AbstractCaptchaProvider
{
    protected array $param = [];

    public function setParam($key, $value): void
    {
        $this->param[$key] = $value;
    }

    abstract public function name();

    abstract public function title();

    abstract public function render();

    abstract public function validate();
}
