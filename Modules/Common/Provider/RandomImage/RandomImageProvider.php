<?php

namespace Modules\Common\Provider\RandomImage;

class RandomImageProvider
{
    /**
     * @return AbstractRandomImageProvider|null
     */
    public static function get(): ?AbstractRandomImageProvider
    {
        static $instance = null;
        if (null === $instance) {
            $driver = config('RandomImageProvider');
            if (empty($driver)) {
                $driver = DefaultRandomImageProvider::class;
            }
            $instance = app($driver);
        }
        return $instance;
    }

    public static function getImage($biz = '', $param = [])
    {
        return self::get()->get(array_merge(['biz' => $biz], $param));
    }
}
