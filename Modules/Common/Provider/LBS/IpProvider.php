<?php

namespace Modules\Common\Provider\LBS;

use Modules\Common\Provider\ProviderTrait;

class IpProvider
{
    use ProviderTrait;

    /**
     * @return AbstractIpProvider[]
     */
    public static function all(): array
    {
        return self::listAll();
    }

    /**
     * @param $name
     * @return AbstractIpProvider
     */
    public static function get($name): AbstractIpProvider
    {
        return self::getByName($name);
    }

    public static function first(): ?AbstractIpProvider
    {
        foreach (self::all() as $provider) {
            return $provider;
        }
        return null;
    }

    public static function firstResponse($ip): ?IpLocationResponse
    {
        $provider = self::first();
        return $provider?->getLocation($ip);
    }

    public static function firstResponseKey($ip, $keys = ['province']): string
    {
        $res = self::firstResponse($ip);
        if (empty($res)) {
            return '';
        }
        $result = [];
        foreach ($keys as $key) {
            $result[] = $res->{$key};
        }
        return join('', $result);
    }
}
