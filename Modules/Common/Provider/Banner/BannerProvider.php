<?php

namespace Modules\Common\Provider\Banner;

use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;

class BannerProvider
{
    private static array $factories = [];

    public static function register($factory): void
    {
        self::$factories[] = $factory;
    }
    public static function get(): AbstractBannerProvider
    {
        foreach (self::$factories as $factory) {
            $provider = call_user_func($factory);
            if ($provider instanceof AbstractBannerProvider) {
                return $provider;
            }
        }
        BizException::throws(Code::FAILED, '', ['view' => 'bingo::core.msg.500','viewData' => ['foo' => 'bar']]);
    }
}
