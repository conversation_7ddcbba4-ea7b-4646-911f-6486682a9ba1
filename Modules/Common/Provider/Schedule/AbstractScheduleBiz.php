<?php

namespace Modules\Common\Provider\Schedule;

/**
 * Class AbstractScheduleProvider
 * @package Modules\Common\Provider\Schedule
 */
abstract class AbstractScheduleBiz
{
    abstract public function cron();

    public function name(): string
    {
        return 'default';
    }

    abstract public function title();

    abstract public function run();

    protected function cronEveryMinute(): string
    {
        return "* * * * *";
    }

    protected function cronEvery10Minute(): string
    {
        return '*/10 * * * * *';
    }

    protected function cronEvery30Minute(): string
    {
        return '0,30 * * * * *';
    }

    /**
     * 每天整点定时
     * @param $hour int 0~23
     */
    protected function cronEveryDayHour24(int $hour): string
    {
        return "0 $hour * * * *";
    }

    /**
     * 每天安时分定制
     * @param $hour int 0-23
     * @param $minute int 0-59
     */
    protected function cronEveryDayHour24Minute(int $hour, int $minute): string
    {
        return "$minute $hour * * * *";
    }

    protected function cronEveryHour(): string
    {
        return '0 * * * * *';
    }

    protected function cronEveryDay(): string
    {
        return '0 0 * * * *';
    }
}
