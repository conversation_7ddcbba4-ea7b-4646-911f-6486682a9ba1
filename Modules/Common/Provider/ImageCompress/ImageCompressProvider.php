<?php

namespace Modules\Common\Provider\ImageCompress;

use Modules\Common\Provider\ProviderTrait;

class ImageCompressProvider
{
    use ProviderTrait;

    /**
     * @return AbstractImageCompressProvider[]
     */
    public static function all(): array
    {
        return self::listAll();
    }

    /**
     * @param $name
     * @return AbstractImageCompressProvider
     */
    public static function get($name): AbstractImageCompressProvider
    {
        return self::getByName($name);
    }

    public static function first(): ?AbstractImageCompressProvider
    {
        foreach (self::all() as $item) {
            return $item;
        }
        return null;
    }
}
