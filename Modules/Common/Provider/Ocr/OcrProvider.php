<?php

namespace Modules\Common\Provider\Ocr;

use Modules\Common\Provider\ProviderTrait;

class OcrProvider
{
    use ProviderTrait;

    /**
     * @return AbstractOcrProvider[]
     */
    public static function all(): array
    {
        return self::listAll();
    }

    /**
     * @param $name
     * @return AbstractOcrProvider
     */
    public static function get($name): AbstractOcrProvider
    {
        return self::getByName($name);
    }

    public static function first(): ?AbstractOcrProvider
    {
        foreach (self::all() as $provider) {
            return $provider;
        }
        return null;
    }

}
