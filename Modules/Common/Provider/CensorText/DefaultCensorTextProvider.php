<?php

namespace Modules\Common\Provider\CensorText;

use Bingo\Core\Input\Response;

class DefaultCensorTextProvider extends AbstractCensorTextProvider
{
    public function name(): string
    {
        return 'default';
    }

    public function title(): string
    {
        return '无检测';
    }

    public function verify($content, $param = []): array
    {
        return Response::generateSuccessData([
            'pass' => false,
        ]);
    }

}
