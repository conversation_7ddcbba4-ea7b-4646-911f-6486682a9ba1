<?php

namespace Modules\Common\Provider\CensorText;

class CensorTextProvider
{
    /**
     * @var AbstractCensorTextProvider[]
     */
    private static array $instances = [
        DefaultCensorTextProvider::class,
    ];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    /**
     * @return AbstractCensorTextProvider[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    public static function nameTitleMap(): array
    {
        return array_build(self::all(), function ($k, $provider) {
            return [
                $provider->name(),
                $provider->title(),
            ];
        });
    }

    /**
     * @param $name
     * @return AbstractCensorTextProvider|null
     */
    public static function get($name): ?AbstractCensorTextProvider
    {
        foreach (self::all() as $item) {
            if ($item->name() == $name) {
                return $item;
            }
        }
        return null;
    }
}
