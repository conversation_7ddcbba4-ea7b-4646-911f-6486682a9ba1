<?php

namespace Modules\Common\Provider\Recommend;

use Modules\Common\Provider\ProviderTrait;
use Bingo\Exceptions\BizException;
use Bingo\Enums\Code;

/**
 * @method static AbstractRecommendProvider[] listAll()
 * @method static AbstractRecommendProvider getByName($name)
 */
class RecommendProvider
{
    use ProviderTrait;
    private static array $factories = [];

    public static function register(callable $factory): void
    {
        self::$factories[] = $factory;
    }

    public static function get(): AbstractRecommendProvider
    {
        foreach (self::$factories as $factory) {
            $provider = call_user_func($factory);
            if ($provider instanceof AbstractRecommendProvider) {
                return $provider;
            }
        }
        BizException::throws(Code::FAILED, '', ['view' => 'bingo::core.msg.500','viewData' => ['foo' => 'bar']]);
    }
}
