<?php

namespace Modules\Common\Provider\MailSender;

interface MailSenderInterface
{
    /**
     * 发送模板邮件
     */
    public function sendTemplate(string $email, string $template, ?string $emailUserName = null, array $option = [], int $delay = 0): bool;

    /**
     * 发送HTML邮件
     */
    public function sendHtml(string $email, string $subject, string $html, ?string $emailUserName = null, array $option = [], int $delay = 0): bool;

    /**
     * 发送模块邮件
     */
    public function sendModule(string $module, string $template, string $email, string $subject, array $templateData = [], ?string $emailUserName = null, array $option = [], int $delay = 0): bool;
}
