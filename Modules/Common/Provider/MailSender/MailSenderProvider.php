<?php

namespace Modules\Common\Provider\MailSender;

use Bingo\Enums\Code;
use Bingo\Exceptions\BizException;

/**
 * Class MailSenderProvider
 * @package Modules\Common\Provider\MailSender
 * @since 1.7.0
 */
class MailSenderProvider
{
    /**
     * @var AbstractMailSenderProvider[]
     */
    private static array $instances = [
    ];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    /**
     * @return AbstractMailSenderProvider[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
            if (! (self::$instances[$k] instanceof AbstractMailSenderProvider)) {
                throw new \RuntimeException("Expected instance of AbstractMailSenderProvider, got ".get_class(self::$instances[$k]));
            }
        }
        return self::$instances;
    }

    /**
     * @param $name
     * @return AbstractMailSenderProvider
     * @throws BizException
     */
    public static function get($name): AbstractMailSenderProvider
    {
        foreach (self::all() as $item) {
            if ($item->name() == $name) {
                return $item;
            }
        }
        BizException::throws(Code::FAILED, '没有找到MailSenderProvider');
    }

    /**
     * @return bool
     */
    public static function hasProvider(): bool
    {
        $provider = app()->config->get('EmailSenderProvider');
        return ! empty($provider);
    }
}
