<?php

namespace Modules\Common\Provider\ContentVerify;

use Bingo\Core\Config\BingoConfig;
use Illuminate\Support\Str;
use Bingo\Core\Assets\AssetsUtil;
use Bingo\Core\Input\Response;
use Bingo\Core\Util\HtmlUtil;
use Modules\Common\Provider\CensorImage\CensorImageProvider;
use Modules\Common\Provider\CensorText\CensorTextProvider;
use Modules\Common\Provider\Notifier\NotifierProvider;

abstract class AbstractContentVerifyBiz
{
    /**
     * 业务唯一标识
     * @return string
     * @example post
     */
    abstract public function name(): string;

    /**
     * 业务标题
     * @return string
     * @example 文章
     */
    abstract public function title(): string;

    /**
     * 是否自动审核完成
     * @param $param
     * @return boolean
     */
    public function verifyAutoProcess($param): bool
    {
        return false;
    }

    /**
     * 待审核数量
     * @return int
     * @example return ModelUtil::count('post', ['status' => PostStatus::VERIFYING])
     */
    abstract public function verifyCount(): int;

    /**
     * 审核权限规则（用户首页是否显示待审核数量的权限判断）
     * @return string
     * @example '\Modules\Post\Admin\Controller\PostController@verifyList'
     */
    abstract public function verifyRule(): string;


    /**
     * 启用自动表单审核，返回 false 表示不适用表单审核
     * @return false
     */
    public function useFormVerify(): false
    {
        return false;
    }


    /**
     * 后台审核路径
     * @return string
     */
    public function verifyUrl(): string
    {
        return action($this->verifyRule());
    }

    /**
     * 自动审核成功是否通知
     * @return bool
     */
    public function verifyAutoProcessedNotify(): bool
    {
        return true;
    }

    public function run($param, $title = null, $body = null): void
    {
        if (null === $body) {
            $body = [
                '内容' => $title,
            ];
            $shortTitle = Str::substr(HtmlUtil::text2html($title), 0, 100);
        } else {
            $shortTitle = $title;
        }
        $shortTitle = HtmlUtil::text($shortTitle);
        $shortTitle = $this->title().($shortTitle ? '('.$shortTitle.')' : '');
        if ($this->verifyAutoProcess($param)) {
            if ($this->verifyAutoProcessedNotify()) {
                NotifierProvider::notify($this->name(), '[自动审核]'.$shortTitle, $body, $param);
            }
            return;
        }
        NotifierProvider::notifyNoneLoginOperateProcessUrl(
            $this->name(),
            '[审核]'.$shortTitle,
            $body,
            $this->useFormVerify() ? 'content_verify/'.$this->name() : null,
            $param
        );
    }

    protected function parseRichHtml($content): array
    {
        $ret = HtmlUtil::extractTextAndImages($content);
        $images = [];
        $text = $ret['text'];
        foreach ($ret['images'] as $image) {
            $images[] = AssetsUtil::fixFull($image);
        }
        return [
            $text, $images
        ];
    }

    protected function censorRichHtmlSuccess($censorProviderKeyPrefix, $content): bool
    {
        list($text, $images) = $this->parseRichHtml($content);
        if (! empty($text)) {
            $provider = CensorTextProvider::get(bingostart_config($censorProviderKeyPrefix.'_Text', BingoConfig::DEFAULT_LANG, 'default'));
            if ($provider) {
                $ret = $provider->verify($text);
                if (Response::isError($ret)) {
                    return false;
                }
                if (! $ret['data']['pass']) {
                    return false;
                }
            }
        }
        if (! empty($images)) {
            $provider = CensorImageProvider::get(bingostart_config($censorProviderKeyPrefix.'_Image', BingoConfig::DEFAULT_LANG, 'default'));
            if ($provider) {
                foreach ($images as $image) {
                    $ret = $provider->verify($image);
                    if (Response::isError($ret)) {
                        return false;
                    }
                    if (! $ret['data']['pass']) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

}
