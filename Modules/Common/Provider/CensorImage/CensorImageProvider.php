<?php

namespace Modules\Common\Provider\CensorImage;

class CensorImageProvider
{
    /**
     * @var AbstractCensorImageProvider[]
     */
    private static array $instances = [
        DefaultCensorImageProvider::class,
    ];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    /**
     * @return AbstractCensorImageProvider[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    public static function nameTitleMap(): array
    {
        return array_build(self::all(), function ($k, $provider) {
            return [
                $provider->name(),
                $provider->title(),
            ];
        });
    }

    /**
     * @param $name
     * @return AbstractCensorImageProvider|null
     */
    public static function get($name): ?AbstractCensorImageProvider
    {
        foreach (self::all() as $item) {
            if ($item->name() == $name) {
                return $item;
            }
        }
        return null;
    }
}
