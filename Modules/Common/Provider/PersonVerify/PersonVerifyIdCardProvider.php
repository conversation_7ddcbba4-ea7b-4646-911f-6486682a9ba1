<?php

namespace Modules\Common\Provider\PersonVerify;

use Modules\Common\Provider\ProviderTrait;

class PersonVerifyIdCardProvider
{
    use ProviderTrait;

    /**
     * @return AbstractPersonVerifyIdCardProvider[]
     */
    public static function all()
    {
        return self::listAll();
    }

    /**
     * @param $name
     * @return AbstractPersonVerifyIdCardProvider
     */
    public static function get($name)
    {
        return self::getByName($name);
    }

    public static function first()
    {
        foreach (self::all() as $provider) {
            return $provider;
        }

    }

    public static function firstResponse($name, $idCardNumber, $param = []): ?PersonVerifyIdCardResponse
    {
        $provider = self::first();
        return $provider?->verify($name, $idCardNumber, $param);
    }
}
