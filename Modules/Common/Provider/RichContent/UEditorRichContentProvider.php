<?php

namespace Modules\Common\Provider\RichContent;

use Bingo\Core\Util\HtmlUtil;
use Illuminate\Support\Facades\View;

class UEditorRichContentProvider extends AbstractRichContentProvider
{
    public const NAME = 'htmlUEditor';

    public function name(): string
    {
        return self::NAME;
    }

    public function title(): string
    {
        return 'UEditor富文本';
    }

    public function render($name, $value, $param = []): string
    {
        return View::make('module::Common.View.widget.richContent.htmlUeditor', [
            'name' => $name,
            'value' => $value,
            'param' => $param,
        ])->render();
    }

    public function toHtml($value, $htmlInterceptors = null)
    {
        $value = HtmlUtil::filter($value);
        return parent::toHtml($value, $htmlInterceptors);
    }


}
