<?php

namespace Modules\Common\Provider\IDManager;

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class IDManager
{
    /**
     * @var AbstractIDManager[]
     */
    private static array $instances = [];

    public static function register($provider): void
    {
        self::$instances[] = $provider;
    }

    /**
     * @return AbstractIDManager[]
     */
    public static function all(): array
    {
        foreach (self::$instances as $k => $v) {
            if ($v instanceof \Closure) {
                self::$instances[$k] = call_user_func($v);
            } elseif (is_string($v)) {
                self::$instances[$k] = app($v);
            }
        }
        return self::$instances;
    }

    /**
     * @param $name
     * @return AbstractIDManager|null
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public static function get($name): ?AbstractIDManager
    {
        $name = bingostart_config($name, $name);
        foreach (self::all() as $manager) {
            if ($manager->name() == $name) {
                return $manager;
            }
        }
        return null;
    }
}
