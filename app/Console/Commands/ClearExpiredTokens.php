<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Modules\Course\Services\TokenService;

class ClearExpiredTokens extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'tokens:clear-expired';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '清理已过期的令牌';

    /**
     * TokenService 实例
     * 
     * @var TokenService
     */
    protected TokenService $tokenService;

    /**
     * 构造函数
     * 
     * @param TokenService $tokenService
     */
    public function __construct(TokenService $tokenService)
    {
        parent::__construct();
        $this->tokenService = $tokenService;
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始清理过期令牌...');
        
        // 扫描Redis中的所有令牌
        $pattern = TokenService::CACHE_PREFIX . '*';
        $keysDeleted = 0;
        
        $this->info('正在扫描Redis缓存中的令牌...');
        
        // 使用SCAN而不是KEYS来避免阻塞Redis
        $iterator = null;
        do {
            // 获取匹配的键
            $keys = Redis::scan($iterator, ['match' => $pattern, 'count' => 100]);
            
            if (!empty($keys)) {
                foreach ($keys as $key) {
                    // 获取令牌数据
                    $data = Redis::get($key);
                    if ($data) {
                        $userData = json_decode($data, true);
                        
                        // 检查是否过期
                        if (isset($userData['token_expired_at']) && now()->gt($userData['token_expired_at'])) {
                            // 提取token值（去除前缀）
                            $token = str_replace(TokenService::CACHE_PREFIX, '', $key);
                            
                            // 使用TokenService吊销令牌
                            $this->tokenService->revokeToken($token);
                            $keysDeleted++;
                            
                            $this->line("已清理令牌: {$token}");
                        }
                    }
                }
            }
        } while ($iterator > 0);
        
        if ($keysDeleted > 0) {
            $this->info("从Redis缓存中删除了 {$keysDeleted} 个过期令牌");
            Log::info("从Redis缓存中删除了 {$keysDeleted} 个过期令牌");
        } else {
            $this->info('Redis缓存中没有过期令牌');
        }
        
        $this->info('令牌清理完成');
        return 0;
    }
} 