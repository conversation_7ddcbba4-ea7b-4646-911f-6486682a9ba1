<?php

namespace App\Http\Traits;

use Illuminate\Http\Request;

/**
 * TokenTrait
 * 
 * 用于处理令牌相关的通用方法
 */
trait TokenTrait
{
    /**
     * 从请求中获取令牌
     * 
     * @param Request $request 请求对象
     * @return string|null 返回令牌字符串，如果没有找到则返回null
     */
    protected function getTokenFromRequest(Request $request): ?string
    {
        // 从请求头中获取令牌
        $token = $request->header('Authorization');
        
        if ($token) {
            // 移除Bearer前缀
            return str_replace('Bearer ', '', $token);
        }
        
        // 从请求参数中获取令牌
        $token = $request->input('token');
        if ($token) {
            return $token;
        }
        
        // 从Cookie中获取令牌
        $token = $request->cookie('token');
        if ($token) {
            return $token;
        }
        
        return null;
    }
} 