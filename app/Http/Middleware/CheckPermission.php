<?php
// app/Http/Middleware/CheckPermission.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Modules\Course\Services\PermissionService;
use Modules\Course\Enums\ErrorCode;

class CheckPermission
{
    /**
     * 权限服务
     * 
     * @var PermissionService
     */
    protected $permissionService;

    /**
     * 构造函数
     * 
     * @param PermissionService $permissionService
     */
    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * 权限检查中间件
     * 
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // TODO 暂时跳过权限检查
        return $next($request);

        // 获取当前路由对应的权限代码
        $permissionCode = $request->route()->getName();
        
        // 如果没有配置权限代码，则跳过权限检查
        if (!$permissionCode) {
            return $next($request);
        }

        // 检查用户是否有权限
        if (!$this->permissionService->hasPermission($permissionCode)) {
            return response()->json([
                'code' => ErrorCode::PERMISSION_DENIED,
                'message' => ErrorCode::PERMISSION_DENIED->getMessage()
            ], 403);
        }

        return $next($request);
    }
} 