<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Modules\Course\Models\User;
use Modules\Course\Services\TokenService;
use App\Http\Traits\TokenTrait;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

/**
 * Token认证中间件
 * 
 * 用于API请求的认证，验证请求中的令牌是否有效
 * 支持自动续期token功能
 */
class TokenAuthentication
{
    use TokenTrait;

    /**
     * Token服务实例
     * 
     * @var TokenService
     */
    protected TokenService $tokenService;

    /**
     * 构造函数
     * 
     * @param TokenService $tokenService Token服务实例
     */
    public function __construct(TokenService $tokenService)
    {
        $this->tokenService = $tokenService;
    }

    /**
     * 处理传入的请求
     *
     * @param Request $request 请求对象
     * @param Closure $next 下一个中间件
     * @return mixed 响应
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function handle(Request $request, Closure $next): mixed
    {
        // 获取请求中的令牌
        $token = $this->getTokenFromRequest($request);
        
        // 如果没有提供令牌，返回未授权响应
        if (!$token) {
            Log::info('用户未提供令牌');
            return response()->json([
                'code' => 401,
                'message' => T('Course::validation.login_required')
            ], 401);
        }
        
        // 验证令牌
        $tokenData = $this->tokenService->validateToken($token);
        
        // 如果令牌无效或已过期
        if (!$tokenData) {
            return response()->json([
                'code' => 401,
                'message' => T('Course::validation.login_expired')
            ], 401);
        }

        // 检查是否需要自动续期token
        $shouldRefresh = $this->shouldRefreshToken($tokenData);
        if ($shouldRefresh) {
            try {
                // 尝试刷新token
                $newTokenData = $this->tokenService->refreshToken($token);
                
                if ($newTokenData) {
                    // 将新token添加到响应头
                    $response = $next($request);
                    $response->headers->set('X-New-Token', $newTokenData['token']);
                    $response->headers->set('X-New-Token-Expired-At', $newTokenData['token_expired_at']);
                    return $response;
                }
            } catch (Exception $e) {
                Log::error('Token自动续期失败', [
                    'token' => $token,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        // 查找用户并设置到请求中
        $user = User::find($tokenData['id']);
        if (!$user) {
            // 用户不存在（可能已被删除），清除令牌
            $this->tokenService->revokeToken($token);
            
            Log::warning('令牌对应的用户不存在', [
                'user_id' => $tokenData['id'],
                'token' => $token
            ]);
            
            return response()->json([
                'code' => 401,
                'message' => T('Course::validation.login_required')
            ], 401);
        }
        
        // 将用户和令牌数据绑定到请求，以便控制器使用
        $request->merge(['auth_user' => $user]);
        $request->merge(['auth_token' => $tokenData]);
        
        // 自动登录用户（如果需要使用Auth facade）
        Auth::login($user);
        
        Log::info('令牌验证成功', [
            'user_id' => $user->id,
            'token' => $token,
        ]);
        
        return $next($request);
    }

    /**
     * 检查是否需要刷新token
     * 
     * @param array $tokenData token数据
     * @return bool
     */
    private function shouldRefreshToken(array $tokenData): bool
    {
        // 检查是否开启了自动续期功能
        $autoRefreshEnabled = env('TOKEN_AUTO_REFRESH', false);
        
        if (!$autoRefreshEnabled) {
            return false;
        }
        
        // 如果token还有5分钟过期，就需要刷新
        $expireTime = strtotime($tokenData['token_expired_at']);
        $now = time();
        $fiveMinutes = 5 * 60;
        
        return ($expireTime - $now) <= $fiveMinutes;
    }
} 