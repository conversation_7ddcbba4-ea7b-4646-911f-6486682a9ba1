########################### 授权管理 ###########################
### 登录
# @name login
POST http://localhost:8000/api/course/auth/login
Content-Type: application/json

{
    "account": "admin",
    "password": "123456"
}

### 保存login响应中的token，确保执行完登录请求后再执行后续请求
@apiToken = cfb2b5354f96a6a0cd388c2be449f871
# @apiToken = {{login.response.body.data.token}}


### 刷新令牌 - 确保先执行登录请求获取token
# @name refreshToken
POST http://localhost:8000/api/course/user/refresh-token
Authorization: Bearer {{apiToken}}
Content-Type: application/json

### 更新apiToken为刷新后的token，确保执行完刷新令牌请求后再执行后续请求
# @apiToken = {{refreshToken.response.body.data.token}}

### 忘记密码
POST http://localhost:8000/api/course/auth/forget-password
Content-Type: application/json

{
    "account": "admin"
}

### 重置密码
POST http://localhost:8000/api/course/auth/reset-password
Content-Type: application/json

{
    "id": 1,
    "password": "123456",
    "password_confirmation": "123456"
}

########################### 用户管理 ###########################

### 获取用户信息
GET http://localhost:8000/api/course/user/1/info
Authorization: Bearer {{apiToken}}

### 获取用户列表
GET http://localhost:8000/api/course/user/list
Authorization: Bearer {{apiToken}}

### 退出登录
POST http://localhost:8000/api/course/user/logout
Authorization: Bearer {{apiToken}}

### 获取登录用户权限树
GET http://localhost:8000/api/course/user/1/permission-tree
Authorization: Bearer {{apiToken}}

### 更新用户信息
POST http://localhost:8000/api/course/user/update
Authorization: Bearer {{apiToken}}
Content-Type: application/json

{
    "id": 6,
    "show_email_type": 1,      // 邮箱显示类型,1为所有用户可见,2为不显示,3为仅管理员可见
    "moodlenet_account": "test",      // Moodle网络账号
    "city_address": "北京",           // 城市地址
    "country": "中国",                // 国家
    "timezone": "Asia/Shanghai",      // 时区
    "introduction": "测试用户简介",    // 个人简介
    "avatar_url": "https://www.baidu.com/img/flexible/logo/pc/result.png", // 头像URL
    "avatar_description": "测试头像",  // 头像描述
    "interests": "编程,阅读",         // 兴趣爱好
    "phone_number": "***********",    // 电话号码
    "mobile_number": "***********",   // 手机号码
    "detailed_address": "北京市海淀区", // 详细地址
    "department": "电气工程学院",           // 科系
    "additional_last_name": "张",     // 附加姓氏
    "additional_first_name": "三",    // 附加名字
    "additional_middle_name": "",     // 附加中间名
    "additional_alias": "张三",       // 附加别名
    "status": 1                       // 状态
}

### 获取课程成员列表
GET http://localhost:8000/api/course/user/course-members
Authorization: Bearer {{apiToken}}
Content-Type: application/json

{
    "course_id": 1
}

########################### 角色管理 ###########################

### 获取角色列表
GET http://localhost:8000/api/course/role/list
Authorization: Bearer {{apiToken}}

### 获取角色信息
GET http://localhost:8000/api/course/role/1/info
Authorization: Bearer {{apiToken}}

### 保存角色信息(新增/更新)
POST http://localhost:8000/api/course/role/save
Authorization: Bearer {{apiToken}}
Content-Type: application/json

{
    "name": "测试角色",
    "description": "测试角色描述",
    "status": 1,
    "permissions": [1, 2, 3] // 权限ID列表
}

### {
###     "id": 1, // 角色ID,提供id时为更新,否则为新增
###     "name": "管理员", // 角色名称
###     "description": "管理员角色", // 角色描述
###     "status": 1 // 角色状态，1为启用，0为禁用
### }

### 删除角色
DELETE http://localhost:8000/api/course/role/1
Authorization: Bearer {{apiToken}}

### 开关角色
POST http://localhost:8000/api/course/role/1/toggle
Authorization: Bearer {{apiToken}}

### 获取角色权限树
GET http://localhost:8000/api/course/role/1/permission-tree
Authorization: Bearer {{apiToken}}

######################################### 权限管理 #################################

### 获取权限树
GET http://localhost:8000/api/course/permission/tree
Authorization: Bearer {{apiToken}}

### 获取权限信息
GET http://localhost:8000/api/course/permission/1/info
Authorization: Bearer {{apiToken}}

######################################### 用户偏好管理 #################################

### 获取用户偏好
GET http://localhost:8000/api/course/user-preference/get
Authorization: Bearer {{apiToken}}
Content-Type: application/json

{
    "user_id": 1,
    "key": "language"
}

### 更新用户偏好
POST http://localhost:8000/api/course/user-preference/update
Authorization: Bearer {{apiToken}}
Content-Type: application/json

{
    "user_id": 1,
    "key": "language",
    "value": "zh_CN"
}

### 获取用户偏好列表
GET http://localhost:8000/api/course/user-preference/list
Authorization: Bearer {{apiToken}}  

{
    "user_id": 1
}

############################################ 单元管理 ############################################

### 获取单元列表
GET http://localhost:8000/api/course/1/unit/list
Authorization: Bearer {{apiToken}}
Content-Type: application/json

{
    "course_id": 1
}

### 获取单元信息
GET http://localhost:8000/api/course/unit/1/info
Authorization: Bearer {{apiToken}}

### 批量保存单元
POST http://localhost:8000/api/course/unit/batch-save
Authorization: Bearer {{apiToken}}
Content-Type: application/json

### 保存逻辑：units数组中，id为空时，新增单元，id不为空时，更新单元
### resources数组中，id为空时，新增资源，id不为空时，更新资源
{
  "course_id": 4,
  "units": [
    {
      "id":20,
      "title_cn": "单元一",
      "title_en": "Unit One",
      "title": "单元一/Unit One",
      "description": "这是单元一的描述",
      "icon_url": "https://example.com/icons/unit1.png",
      "sort_order": 1,
      "resources": [
        {
          "type": "file",
          "title": "PDF文档",
          "description": "这是一个PDF文档",
          "sort_order": 1,
          "file_type": "pdf",
          "file_size": 1024,
          "url": "https://example.com/files/document.pdf"
        },
        {
          "type": "video",
          "title": "介绍视频",
          "description": "这是一个介绍视频",
          "sort_order": 2,
          "duration": 300,
          "allow_fast_forward": false,
          "url": "https://example.com/videos/intro.mp4"
        },
        {
          "type": "folder",
          "title": "资源文件夹",
          "description": "包含多个资源的文件夹",
          "sort_order": 4,
          "resource_type": "folder",
          "parent_id": null
        },
        {
          "type": "folder",
          "title": "文件夹中的文件",
          "description": "文件夹中的文件示例",
          "sort_order": 5,
          "resource_type": "file",
          "parent_id": 1,
          "file_type": "word",
          "url": "https://example.com/files/document.docx"
        }
      ]
    },
    {
      "title_cn": "单元二",
      "title_en": "Unit Two",
      "title": "单元二/Unit Two",
      "description": "这是单元二的描述",
      "icon_url": "https://example.com/icons/unit2.png",
      "sort_order": 2,
      "resources": [
        {
          "type": "file",
          "title": "Excel表格",
          "description": "这是一个Excel表格",
          "sort_order": 1,
          "file_type": "excel",
          "file_size": 512,
          "url": "https://example.com/files/data.xlsx"
        },
        {
          "type": "video",
          "title": "教学视频",
          "description": "这是一个教学视频",
          "sort_order": 2,
          "duration": 600,
          "allow_fast_forward": true,
          "url": "https://example.com/videos/lesson.mp4"
        }
      ]
    }
  ]
}

######################################### 资源管理 #################################

### 上传资源文件
POST http://localhost:8000/api/course/resource/upload
Authorization: Bearer {{apiToken}}
Content-Type: multipart/form-data

{
    "file": "test.txt", // 文件资源
    "type": "file", // 文件类型
    "title": "测试文件" // 文件标题
}

### 获取视频资源
GET http://localhost:8000/api/course/resource/video/1/info
Authorization: Bearer {{apiToken}}

### 获取文件资源
GET http://localhost:8000/api/course/resource/file/1/info
Authorization: Bearer {{apiToken}}

########################################### 文件夹资源管理 #################################

### 获取单元文件夹树结构
GET http://localhost:8000/api/course/resource/folder/tree
Authorization: Bearer {{apiToken}}
Content-Type: application/json

{
    "unit_id": 1
}

### 保存文件夹资源
POST http://localhost:8000/api/course/resource/folder/save
Authorization: Bearer {{apiToken}}
Content-Type: application/json

{
    "unit_id": 1,
    "name": "测试文件夹",
    "description": "测试文件夹描述",
    "status": 1
}

### 下载文件夹资源
GET http://localhost:8000/api/course/resource/folder/1/download
Authorization: Bearer {{apiToken}}

### 获取视频资源进度
GET http://lms.bingo-test.com/api/course/resource/video/1/info
Authorization: Bearer {{apiToken}}
Content-Type: application/json

{
    "with_progress": true,
    "student_id": 1,
    "unit_id": 1
}





















