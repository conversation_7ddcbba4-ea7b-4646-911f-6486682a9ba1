# 学习管理系统ER图

```mermaid
erDiagram
    bingo_user ||--o{ bingo_course_user : "参与课程"
    bingo_user }|--|{ bingo_role_permission : "拥有权限"
    bingo_role ||--o{ bingo_user : "分配给"
    bingo_role }|--|{ bingo_role_permission : "包含权限"
    bingo_permission ||--o{ bingo_role_permission : "被分配给角色"
    bingo_permission ||--o{ bingo_permission : "父子关系"
    
    bingo_course_category ||--o{ bingo_course : "包含"
    bingo_course ||--o{ bingo_course_user : "包含学生和教师"
    bingo_course ||--o{ bingo_course_unit : "包含单元"
    bingo_course ||--o{ bingo_reflection_report : "包含反思报告"
    bingo_course ||--o{ bingo_reflection_submission_settings : "包含提交设置"
    
    bingo_course_unit ||--o{ bingo_video_resource : "包含视频资源"
    bingo_course_unit ||--o{ bingo_unit_file : "包含文件资源"
    bingo_course_unit ||--o{ bingo_student_unit_progress : "有学习进度"
    bingo_course_unit ||--o{ bingo_question_bank : "包含题目"
    
    bingo_video_resource ||--o{ bingo_student_video_watching : "有观看记录"
    
    bingo_question_category ||--o{ bingo_question_bank : "分类题目"
    bingo_question_bank ||--o{ bingo_quiz_questions : "包含于测验"
    
    bingo_student_quiz_attempt ||--o{ bingo_quiz_questions : "包含测验题目"
    
    bingo_reflection_criteria ||--o{ bingo_reflection_grading : "用于评分"
    bingo_reflection_report ||--o{ bingo_reflection_grading : "包含评分"
    bingo_reflection_submission_settings ||--|| bingo_course : "关联课程"
    
    bingo_user {
        int id PK "用户ID"
        varchar account "登录账号"
        varchar password "密码"
        int role_id FK "角色ID"
        varchar email "电子邮箱"
        int status "状态"
    }
    
    bingo_role {
        int id PK "角色ID"
        varchar name "角色名称"
        varchar code "角色编码"
        text description "角色描述"
    }
    
    bingo_permission {
        int id PK "权限ID"
        varchar name "权限名称"
        varchar code "权限编码" 
        varchar module "所属模块"
        int parent_id FK "父权限ID"
    }
    
    bingo_role_permission {
        int id PK "关联ID"
        int role_id FK "角色ID"
        int permission_id FK "权限ID"
    }
    
    bingo_course_category {
        int id PK "分类ID"
        varchar name "分类名称"
        int parent_id FK "父分类ID"
    }
    
    bingo_course {
        int id PK "课程ID"
        varchar course_code "课程代码"
        varchar title_cn "中文标题"
        varchar title_en "英文标题"
        int category_id FK "分类ID"
        int status "课程状态"
    }
    
    bingo_course_user {
        int id PK "记录ID"
        int user_id FK "用户ID"
        int course_id FK "课程ID"
    }
    
    bingo_course_unit {
        int id PK "单元ID"
        int course_id FK "课程ID"
        tinyint unit_number "单元编号"
        varchar title_cn "中文标题"
        varchar title_en "英文标题"
    }
    
    bingo_video_resource {
        int id PK "视频资源ID"
        int unit_id FK "单元ID"
        varchar title "视频标题"
        varchar video_url "视频URL"
        int duration "视频时长"
    }
    
    bingo_unit_file {
        int id PK "文件ID"
        int unit_id FK "单元ID"
        varchar title "文件标题"
        varchar file_url "文件URL"
        varchar file_type "文件类型"
    }
    
    bingo_question_category {
        int id PK "分类ID"
        varchar name "分类名称"
        int parent_id FK "父分类ID"
    }
    
    bingo_question_bank {
        int id PK "题目ID"
        varchar code "题目编号"
        int category_id FK "分类ID"
        int unit_id FK "单元ID"
        text question_text "题目内容"
        tinyint correct_option "正确选项"
    }
    
    bingo_student_unit_progress {
        int id PK "进度记录ID"
        int student_id FK "学生ID"
        int course_id FK "课程ID"
        int unit_id FK "单元ID"
        int status "状态"
    }
    
    bingo_student_video_watching {
        int id PK "观看记录ID"
        int student_id FK "学生ID"
        int video_id FK "视频ID"
        int course_id FK "课程ID"
        int last_position "观看位置"
        decimal completion_percentage "完成百分比"
    }
    
    bingo_student_quiz_attempt {
        int id PK "测验记录ID"
        int student_id FK "学生ID"
        int course_id FK "课程ID"
        int unit_id FK "单元ID"
        smallint attempt_number "尝试次数"
        tinyint score "得分"
    }
    
    bingo_quiz_questions {
        int id PK "关联ID"
        int unit_id FK "单元ID"
        int student_id FK "学生ID"
        int question_id FK "题目ID"
        int quiz_record_id FK "测验记录ID"
    }
    
    bingo_reflection_criteria {
        int id PK "评分标准ID"
        varchar criteria_name "评分项名称"
        text description "评分项描述"
    }
    
    bingo_reflection_grading {
        int id PK "评分记录ID"
        int reflection_id FK "反思报告ID"
        int criteria_id FK "评分标准ID"
        tinyint is_passed "是否通过"
        decimal score_percentage "标准占分"
    }
    
    bingo_reflection_report {
        int id PK "反思报告ID"
        int student_id FK "学生ID"
        int course_id FK "课程ID"
        varchar title "报告标题"
        text content "报告内容"
        int submission_status "提交状态"
        int overall_status "整体状态"
        decimal score "得分"
    }
    
    bingo_reflection_submission_settings {
        int id PK "设置ID"
        int course_id FK "课程ID"
        datetime start_date "开始日期"
        datetime due_date "截止日期"
        decimal similarity_threshold "相似度阈值"
    }
    
    bingo_system_log {
        int id PK "日志ID"
        int user_id FK "用户ID"
        varchar action "操作类型"
        varchar target_type "目标类型"
        int target_id "目标ID"
    }
``` 