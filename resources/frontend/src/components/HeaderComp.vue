<template>
    <header>
        <div class="container-fluid">
            <div class="logo-nav">
                <RouterLink to="/" class="logo-wrap">
                    <img class="logo" src="@/assets/logo.png" alt="Basic, Advanced and/or Thematic Courses for Teacher" />
                </RouterLink>
                <nav>
                    <ul>
                        <li>
                            <el-dropdown :teleported="false">
                                <span class="el-dropdown-link">
                                    {{ $t('header.nav.nav1') }}
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu v-loading="courseListLoading">
                                        <el-dropdown-item v-for="courseItem in courseList" @click="goPage(`/course/${courseItem.id}`)">{{ courseItem.name }}</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </li>
                        <li>
                            <RouterLink to="/mod/book">{{ $t('header.nav.nav2') }}</RouterLink>
                        </li>
                        <li>
                            <RouterLink to="/mod/page">{{ $t('header.nav.nav3') }}</RouterLink>
                        </li>
                        <li>
                            <el-dropdown :teleported="false">
                                <span class="el-dropdown-link">
                                    {{ langList[locale] }}
                                    <el-icon class="el-icon--right">
                                        <arrow-down />
                                    </el-icon>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="switchLangHandle('en')">English</el-dropdown-item>
                                        <el-dropdown-item @click="switchLangHandle('zh-hk')">正體中文</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </li>
                    </ul>
                </nav>
            </div>
            <div class="menu-toggle">
                <span class="show-menu"><i class="fa fa-bars"></i></span>
            </div>
            <div class="header-tools">
                <!-- 通知 -->
                <el-popover placement="bottom-end" :width="380" :teleported="false">
                    <template #reference>
                        <el-icon size="20" color="#fff"><Bell /></el-icon>
                        <!-- <el-tooltip effect="dark" content="切換通知選單" placement="bottom">
                        </el-tooltip> -->
                    </template>

                    <div class="popover-head">
                        <span>通知</span>
                        <div class="tools-list">
                            <el-icon size="14" color="#e63946"><Select /></el-icon>
                            <el-icon size="14" color="#e63946" @click="goPage('/message/notificationPreferences')"><Setting /></el-icon>
                        </div>
                    </div>
                    <div class="popover-body">
                        <el-empty description="你沒有收到通知" image=" " />
                    </div>
                    <div class="popover-foot" @click="goPage('/message/notifications')">查看全部</div>
                </el-popover>
                <el-tooltip effect="dark" content="切換訊息選單" placement="bottom">
                    <el-icon size="20" color="#fff" @click="msgDrawer = true"><ChatRound /></el-icon>
                </el-tooltip>
                <el-tooltip effect="dark" :content="`Tai Man ${userinfo?.first_name}${userinfo?.last_name}`" placement="bottom">
                    <div class="tool-login" @click="userDialog = true">
                        <el-icon size="20" color="#fff"><User /></el-icon>
                        <span>Tai Man {{ userinfo?.first_name }}{{ userinfo?.last_name }}</span>
                    </div>
                </el-tooltip>
            </div>
        </div>

        <!-- 讯息列表 -->
        <div class="drawer-box" :class="{ open: msgDrawer }">
            <div class="drawer-head">
                <el-icon size="16" color="#4c4c4c" @click="msgDrawer = false"><CircleCloseFilled /></el-icon>
            </div>
            <div class="drawer-body">
                <MessageList :openChatSearch="openChatSearchDrawer" :openMsgSet="openMsgSetDrawer" :openAddressBook="openAddressBookDrawer" :openChat="openChatDrawer" />
            </div>
            <div class="drawer-foot">
                <span @click="goPage('/message')">查看全部</span>
            </div>
        </div>

        <!-- 讯息列表设置 -->
        <div class="drawer-box" :class="{ open: msgSetDrawerStore.msgSetDrawer }">
            <div class="drawer-head">
                <el-icon size="16" color="#4c4c4c" @click="closeMsgSetDrawer"><CircleCloseFilled /></el-icon>
            </div>
            <div class="drawer-body">
                <div class="back-box">
                    <div class="cursor back-info" @click="closeMsgSetDrawer">
                        <el-icon size="16" color="#4c4c4c"><ArrowLeftBold /></el-icon>
                        <span>设定</span>
                    </div>
                </div>

                <el-form :model="setFormData" label-position="top">
                    <el-form-item label="隐私">
                        <p class="tips">您可以限制谁能发送讯息给您</p>
                        <el-radio-group v-model="setFormData.privacy">
                            <el-radio value="1">限通讯录内</el-radio>
                            <el-radio value="2">我的通讯录和我的课程中所有人</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="通知提醒的偏好">
                        <div class="switch-box">
                            <el-switch v-model="setFormData.isEmail" active-text="Email" size="small" />
                        </div>
                        <div class="switch-box">
                            <el-switch v-model="setFormData.isDevice" active-text="行动装置" size="small" />
                        </div>
                    </el-form-item>
                    <el-form-item label="一般">
                        <div class="switch-box">
                            <el-switch v-model="setFormData.isUseKeys" active-text="使用输入键发送" size="small" />
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </div>

        <!-- 通讯录 + 好友请求 -->
        <div class="drawer-box" :class="{ open: addressBookDrawer }">
            <div class="drawer-head">
                <el-icon size="16" color="#4c4c4c" @click="addressBookDrawer = false"><CircleCloseFilled /></el-icon>
            </div>
            <div class="drawer-body">
                <div class="back-box">
                    <div class="cursor back-info" @click="addressBookDrawer = false">
                        <el-icon size="16" color="#4c4c4c"><ArrowLeftBold /></el-icon>
                        <span>通讯录</span>
                    </div>
                    <el-icon size="16" color="#4c4c4c" class="cursor" @click="chatSearchDrawer = true"><Search /></el-icon>
                </div>

                <AddressBook :openChat="openChatDrawer" />
            </div>
        </div>

        <!-- 聊天搜索 -->
        <div class="drawer-box" :class="{ open: chatSearchDrawer }">
            <div class="drawer-head">
                <el-icon size="16" color="#4c4c4c" @click="chatSearchDrawer = false"><CircleCloseFilled /></el-icon>
            </div>
            <div class="drawer-body">
                <div class="back-box">
                    <div class="cursor back-info" @click="chatSearchDrawer = false">
                        <el-icon size="16" color="#4c4c4c"><ArrowLeftBold /></el-icon>
                        <span>搜索</span>
                    </div>
                </div>
                <ChatSearch :openChat="openChatDrawer" />
            </div>
        </div>

        <!-- 聊天窗口 -->
        <div class="drawer-box" :class="{ open: chatDrawer }">
            <div class="drawer-head">
                <el-icon size="16" color="#4c4c4c" @click="chatDrawer = false"><CircleCloseFilled /></el-icon>
            </div>
            <div class="drawer-body">
                <ChatComp :isBack="true" :closeChat="closeChatDrawer" />
            </div>
        </div>

        <!-- 用户 -->
        <el-dialog v-model="userDialog" width="400" class="user-dialog-box">
            <div class="theme-loginform">
                <div class="user-picture-container">
                    <div class="user-picture pic">
                        <img v-if="userinfo?.avatar_url" :src="userinfo?.avatar_url" class="ele" />
                        <el-icon v-else size="40" color="#fff"><User /></el-icon>
                    </div>
                    <div class="user-description">
                        <span>
                            您以
                            <strong>Chan Tai Man {{ userinfo?.first_name }}{{ userinfo?.last_name }}</strong>
                            <span class="nline">({{ userinfo?.account }})</span>
                            登入
                        </span>
                        <div class="user-buttons">
                            <el-button color="#27323A" @click="goPage('/user/profile')">簡歷</el-button>
                            <el-button color="#F06423" @click="AuthService.logout()">登出</el-button>
                        </div>
                    </div>
                </div>
                <div class="usermenu">
                    <ul>
                        <li>
                            <router-link to="/" @click="userDialog = false">儀表板</router-link>
                        </li>
                        <li>
                            <router-link to="/user/profile" @click="userDialog = false">簡歷</router-link>
                            <router-link to="/grade/report/overview" @click="userDialog = false">成績</router-link>
                            <router-link to="/message" @click="userDialog = false">訊息</router-link>
                            <router-link to="/user/preferences" @click="userDialog = false">偏好</router-link>
                        </li>
                        <li>
                            <router-link to="" @click="userDialog = false">登出</router-link>
                        </li>
                    </ul>
                </div>
            </div>
        </el-dialog>

        <!-- token延续弹窗 -->
        <el-dialog v-model="authStore.isRenewal" width="500" title="沒有近期的活動" :close-on-click-modal="false">
            <p>您的連線即將逾時。 您要延長現在的連線時間嗎？</p>
            <template #footer>
                <el-button color="#F06423" @click="renewalHandle" v-loading="loading">延長連線時間</el-button>
                <el-button @click="authStore.isRenewal = false" color="#435764" v-loading="loading">取消</el-button>
            </template>
        </el-dialog>

        <!-- 重新登录 弹窗 -->
        <el-dialog v-model="authStore.isExpires" width="500" title="令牌续期" :close-on-click-modal="false">
            <p>{{ dialogCon }}</p>
            <template #footer>
                <el-button color="#F06423" @click="loginHandle">重新登录</el-button>
                <el-button @click="authStore.isExpires = false" color="#435764">取消</el-button>
            </template>
        </el-dialog>
    </header>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, reactive, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { useI18n } from 'vue-i18n'
import { setLocale, langList } from '@/locales/i18n'
import type { SupportedLocales } from '@/locales/i18n'
import type { MessageSchema } from '@/types/i18n'

import MessageList from '@/views/message/components/MessageList.vue'
import AddressBook from '@/views/message/components/AddressBook.vue'
import ChatSearch from '@/views/message/components/ChatSearch.vue'
import ChatComp from '@/views/message/components/ChatComp.vue'

import { useMsgSetDrawerStore } from '@/stores/msgSetDrawer'

import { useAuthStore } from '@/stores/auth'
import AuthService from '@/api/auth'

import CourseService from '@/api/course'
import type { CourseItem } from '@/types/course'

import { useUserStore } from '@/stores/user'

const router = useRouter()
const route = useRoute()
const msgSetDrawerStore = useMsgSetDrawerStore()
const authStore = useAuthStore()
const userStore = useUserStore()
const loading = ref(false)
const { locale } = useI18n<{ message: MessageSchema }, SupportedLocales>()
const goPage = (path: string) => {
    msgDrawer.value = false
    addressBookDrawer.value = false
    chatSearchDrawer.value = false
    userDialog.value = false
    closeMsgSetDrawer()
    closeChatDrawer()

    router.push(path)
}

// 语言切换
const switchLangHandle = (lang: SupportedLocales) => {
    setLocale(lang)
    locale.value = lang
}

// 课程列表
const courseList = ref<CourseItem[]>([])
const courseListLoading = ref(false)
function getCourseList() {
    courseListLoading.value = true
    CourseService.courseList()
        .then(res => {
            courseList.value = res
        })
        .finally(() => {
            courseListLoading.value = false
        })
}

// 消息
const msgDrawer = ref(false)
const addressBookDrawer = ref(false)
const chatSearchDrawer = ref(false)
const chatDrawer = ref(false)
const setFormData = reactive({
    privacy: '2',
    isEmail: true,
    isDevice: false,
    isUseKeys: false,
})

// 打开讯息搜索窗口
const openChatSearchDrawer = () => {
    chatSearchDrawer.value = true
}
// 打开讯息设置窗口
const openMsgSetDrawer = () => {
    msgSetDrawerStore.openMsgSetDrawer()
}
// 关闭讯息设置窗口
const closeMsgSetDrawer = () => {
    msgSetDrawerStore.closeMsgSetDrawer()
}
// 打开通讯录窗口
const openAddressBookDrawer = () => {
    addressBookDrawer.value = true
}
// 打开聊天窗口
const openChatDrawer = () => {
    chatDrawer.value = true
}
// 关闭聊天窗口
const closeChatDrawer = () => {
    chatDrawer.value = false
}

// 用户信息弹窗
const userDialog = ref(false)
const userinfo = computed(() => userStore.userinfoL)

// token续期弹窗
const renewalHandle = () => {
    loading.value = true
    AuthService.refreshToken()
        .then(res => {
            console.log(res)

            authStore.isRenewal = false
            if (!res) {
                dialogCon.value = '续期失败, 请重新登录！'
                authStore.isExpires = true
            }
        })
        .finally(() => {
            loading.value = false
        })
}

// 重新登录 弹窗
const dialogCon = ref('已超时长')
const loginHandle = () => {
    authStore.clearToken()
    localStorage.setItem('redirectPath', route.fullPath)
    router.push('/login')
}

onMounted(() => {
    authStore.scheduleTokenExpirationCheck()
    getCourseList()
})
</script>

<style lang="less" scoped>
header {
    position: sticky;
    top: 0;
    z-index: 999;
    background-color: rgba(240, 100, 35, 0.95);
    // background-color: rgba(255, 255, 255, 0.05);

    .container-fluid {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 95px;

        .logo-nav {
            display: flex;
            align-items: center;
            gap: 45px;

            .logo {
                filter: brightness(0) invert(1);
            }

            nav {
                & > ul {
                    display: flex;
                    align-items: center;
                    gap: 16px;

                    li {
                        font-size: var(--el-font-size-base);
                        line-height: 1;
                        position: relative;

                        a {
                            font-size: var(--el-font-size-base);
                            color: #fff;
                        }

                        .el-dropdown {
                            color: #fff;
                        }
                    }
                }
            }
        }

        .menu-toggle {
            display: none;
        }

        .header-tools {
            display: flex;
            align-items: center;
            gap: 15px;

            & > .el-icon {
                cursor: pointer;
            }

            .tool-login {
                display: flex;
                align-items: center;
                gap: 4px;
                color: #fff;
                cursor: pointer;
            }

            :deep(.el-popover) {
                --el-popover-border-radius: 0;
                --el-popover-padding: 0;

                .popover-head {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    border-bottom: 1px solid #ddd;
                    padding: 0 5px;
                    font-size: 14px;
                    color: #000;
                    height: 25px;
                    line-height: 25px;

                    .tools-list {
                        display: flex;
                        align-items: center;
                        gap: 15px;

                        .el-icon {
                            transition: color 0.35s ease-in-out;
                            cursor: pointer;

                            &:hover {
                                color: #242424;
                            }
                        }
                    }
                }

                .popover-body {
                    height: 442px;
                    overflow-y: auto;
                    overflow-x: hidden;
                }

                .popover-foot {
                    border-top: 1px solid #ddd;
                    text-align: center;
                    font-size: 15px;
                    color: #e63946;
                    transition: color 0.35s ease-in-out;
                    line-height: 30px;
                    height: 30px;
                    cursor: pointer;

                    &:hover {
                        color: #242424;
                    }
                }
            }
        }
    }

    .drawer-box {
        position: fixed;
        right: 0;
        top: 0;
        z-index: 99;
        transform: translateX(100%);
        transition: transform 0.35s ease-in-out;

        box-shadow: -2px 2px 4px rgba(0, 0, 0, 0.08);
        height: 100vh;
        width: 320px;
        background-color: #eff1f3;

        display: flex;
        flex-direction: column;

        &.open {
            transform: translateY(0);
        }

        .drawer-head {
            border-bottom: 1px solid #d5d5d5;
            padding: 4px;
            background-color: #f4f4f4;
            background-image: url(@/assets/img/strip1_black8.svg);
            height: 34px;
            line-height: 34px;

            display: flex;
            justify-content: flex-end;
            align-items: center;
            flex-shrink: 0;

            .el-icon {
                cursor: pointer;
            }
        }

        .drawer-body {
            flex-grow: 1;
            background-color: #ffffff;
            overflow: hidden;

            display: flex;
            flex-direction: column;

            .back-box {
                border-bottom: 1px solid #dee2e6 !important;
                padding: 0 10px;
                font-size: 15px;
                color: #4c4c4c;
                background-color: #eff1f3;
                min-height: 44px;

                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-shrink: 0;

                .cursor {
                    cursor: pointer;
                }

                .back-info {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                }

                .el-icon {
                    cursor: pointer;
                }
            }

            .el-form {
                padding: 15px;
                // height: calc(100% - 44px);
                flex-grow: 1;
                overflow-y: auto;

                :deep(.el-form-item) {
                    .el-form-item__label {
                        color: #000;
                    }
                }

                p.tips {
                    font-size: 14px;
                    color: #4c4c4c;
                }

                .switch-box {
                    width: 100%;
                }
            }
        }

        .drawer-foot {
            flex-shrink: 0;

            & > span {
                cursor: pointer;
                line-height: 25px;
                height: 25px;
                color: #4c4c4c;
                text-align: center;
                display: block;
                transition: color 0.35s ease-in-out;
                font-size: 15px;

                &:hover {
                    color: #242424;
                }
            }
        }
    }

    :deep(.user-dialog-box) {
        --el-dialog-box-shadow: none;
        --el-dialog-bg-color: transparent;
        --el-message-close-size: 30px;
        --el-dialog-margin-top: 13vh;
        background-color: transparent;

        .el-dialog__header {
            border: 0;
            padding: 0;

            .el-dialog__headerbtn {
                transform: translateX(100%);

                .el-dialog__close {
                    color: #fff;
                }
            }
        }
    }

    .theme-loginform {
        .user-picture-container {
            border-radius: 2px;
            padding: 35px 25px;
            background-color: #fff;

            display: flex;
            flex-direction: column;
            align-items: center;

            .user-picture {
                margin-bottom: 25px;
                border-radius: 50%;
                width: 80px;
                height: 80px;
                background-color: #e6e6e6;

                display: flex;
                align-items: center;
                justify-content: center;
            }

            .user-description {
                text-align: center;
                font-size: 15px;
                color: #4c4c4c;
                line-height: 1.666;

                span {
                    .nline {
                        display: block;
                    }
                }

                .user-buttons {
                    margin-top: 25px;
                }
            }
        }

        .usermenu {
            padding: 0 3%;

            ul {
                padding: 10px 15px;
                background-color: #f06423;

                li {
                    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
                    line-height: 40px;

                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 6px;

                    &:last-child {
                        border: none;
                    }

                    a {
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 12px;
                        transition: color 0.35s ease-in-out;

                        &:hover {
                            color: #fff;
                        }
                    }
                }
            }
        }
    }
}
</style>
