<template>
    <div class="profile-card card-box">
        <h5 class="card-title">
            <el-icon size="20" color="#f06423"><Postcard /></el-icon>
            已登入用戶
        </h5>
        <div class="card-text">
            <RouterLink to="/user/profile" class="user-picture pic" v-if="props.isShowUserPicture">
                <!-- <img src="" /> -->
                <el-icon size="30" color="#fff"><User /></el-icon>
            </RouterLink>
            <div class="user-info">
                <div class="fullname"><PERSON> {{ userinfo?.first_name }}{{ userinfo?.last_name }}</div>
                <div class="city">
                    <span>電子郵件信箱: </span>
                    <a :href="`mailto:${userinfo?.email}`">{{ userinfo?.email }}</a>
                </div>
                <div class="institution">
                    <span>機構:</span>
                    {{ userinfo?.department }}
                </div>
                <div class="lastaccess" v-if="props.isShowLastaccess">
                    <span>上次存取: </span>
                    <template v-if="userinfo">{{ formatLoginTime(userinfo.last_login_time) }}</template>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

import { useUserStore } from '@/stores/user'

import { useI18n } from 'vue-i18n'
import type { SupportedLocales } from '@/locales/i18n'
import type { MessageSchema } from '@/types/i18n'

import moment from 'moment'

const props = defineProps<{
    isShowUserPicture?: Boolean
    isShowLastaccess?: Boolean
}>()

const { locale } = useI18n<{ message: MessageSchema }, SupportedLocales>()
const userStore = useUserStore()

const userinfo = computed(() => userStore.userinfoL)
const formatLoginTime = (time: string | Date) => {
    switch (locale.value) {
        case 'zh-hk': {
            return moment(time).locale('zh-hk').format('YYYY年 MM月 DD日(ddd) HH:mm')
        }
        case 'en': {
            return moment(time).locale('en').format('dddd, D MMMM YYYY, h:mm A')
        }
    }
}
</script>

<style lang="less" scoped>
.card-text {
    display: flex;
    align-items: flex-start;

    .user-picture {
        margin-right: 15px;
        width: 50px;
        height: 50px;
        background-color: #e6e6e6;

        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .user-info {
        .fullname {
            margin-bottom: 8px;
            font-weight: 700;
            font-size: 22px;
            color: #4c4c4c;
        }

        .city,
        .institution,
        .lastaccess {
            font-size: 15px;
            line-height: 1.66;
            color: #4c4c4c;

            a {
                color: #e63946;

                &:hover {
                    color: #242424;
                }
            }
        }
    }
}
</style>
