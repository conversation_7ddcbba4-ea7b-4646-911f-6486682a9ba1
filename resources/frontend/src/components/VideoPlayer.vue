<template>
    <div class="video-container" @mouseenter="showControls = true" @mouseleave="startControlsTimer">
        <video ref="videoPlayer" :src="videoSrc" @timeupdate="handleTimeUpdate" @click="togglePlay"></video>

        <!-- 自定义控制条 -->
        <div class="custom-controls" v-show="showControls">
            <!-- 后退10秒 -->
            <button @click="seekBackward">
                <el-icon><DArrowLeft /></el-icon>
            </button>
            <!-- 播放/暂停按钮 -->
            <button @click="togglePlay">
                <el-icon v-if="!isPlaying"><VideoPlay /></el-icon>
                <el-icon v-else><VideoPause /></el-icon>
            </button>
            <!-- 前进10秒 -->
            <button @click="seekForward">
                <el-icon><DArrowRight /></el-icon>
            </button>

            <!-- 进度条 -->
            <div class="progress-container">
                <span class="time">{{ formatTime(currentTime) }}</span>
                <el-slider v-model="sliderValue" :max="duration" :step="0.1" @change="handleSliderChange" @input="handleSliderInput" @mousedown="handleSliderInputStart" />
                <span class="time">{{ formatTime(duration) }}</span>
            </div>

            <!-- 音量控制 -->
            <div class="volume-control">
                <el-popover placement="top" trigger="hover" :teleported="false" width="auto" :show-arrow="false">
                    <template #reference>
                        <button @click="toggleMute">
                            <el-icon v-if="isMuted"><Mute /></el-icon>
                            <el-icon v-else><Microphone /></el-icon>
                        </button>
                    </template>
                    <el-slider v-model="volume" vertical height="80px" :step="0.1" :max="1" @change="handleVolumeChange" />
                </el-popover>
            </div>

            <!-- 全屏按钮 -->
            <button @click="toggleFullscreen">
                <el-icon><FullScreen /></el-icon>
            </button>
        </div>

        <div class="last-time" v-if="lastPlayedTime > 0">上次观看到: {{ formatTime(lastPlayedTime) }}</div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'

const props = defineProps<{
    videoSrc: string
    videoId: string
}>()

const videoPlayer = ref<HTMLVideoElement | null>(null)
const currentTime = ref(0)
const duration = ref(0)
const sliderValue = ref(0)
const maxReachedTime = ref(0)
const lastPlayedTime = ref(0)
const isPlaying = ref(false)
const isMuted = ref(false)
const volume = ref(0.7)
const showControls = ref(true)
const controlsTimer = ref<number | null>(null)
const isDragging = ref(false)
const tempSliderValue = ref(0)

// 从本地存储加载上次播放时间
const loadLastPlayedTime = () => {
    const savedTime = localStorage.getItem(`videoTime_${props.videoId}`)
    lastPlayedTime.value = savedTime ? parseFloat(savedTime) : 0
    maxReachedTime.value = lastPlayedTime.value
}

// 保存播放时间到本地存储
const savePlayTime = (time: number) => {
    localStorage.setItem(`videoTime_${props.videoId}`, time.toString())
}

// 格式化时间为 MM:SS
const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`
}

// 播放/暂停切换
const togglePlay = () => {
    if (!videoPlayer.value) return

    if (videoPlayer.value.paused) {
        videoPlayer.value.play()
        isPlaying.value = true
    } else {
        videoPlayer.value.pause()
        isPlaying.value = false
    }

    // 重置控制条隐藏计时器
    startControlsTimer()
}

// 后退10秒
const seekBackward = () => {
    if (!videoPlayer.value) return

    const newTime = Math.max(0, videoPlayer.value.currentTime - 10)
    videoPlayer.value.currentTime = newTime
    startControlsTimer()
}

// 前进10秒
const seekForward = () => {
    if (!videoPlayer.value) return

    const newTime = Math.min(maxReachedTime.value, videoPlayer.value.currentTime + 10)
    videoPlayer.value.currentTime = newTime
    startControlsTimer()
}

// 处理进度条拖动开始
const handleSliderInputStart = () => {
    isDragging.value = true
    tempSliderValue.value = sliderValue.value
}

// 处理进度条跳转
const handleSeek = (value: number) => {
    if (!videoPlayer.value) return

    // 确保不能跳转到超过最大观看时间
    // console.log(value, maxReachedTime.value)
    const seekTime = Math.min(value, maxReachedTime.value)
    videoPlayer.value.currentTime = seekTime
    startControlsTimer()
}

// 处理进度条拖动
const handleSliderInput = (value: number) => {
    if (!isDragging.value) return

    // 限制不能拖动到超过最大观看时间
    tempSliderValue.value = Math.min(value, maxReachedTime.value)
}

// 处理进度条拖动结束
const handleSliderChange = (value: number) => {
    if (!videoPlayer.value) return

    // 确保应用的是限制后的值
    const seekTime = Math.min(value, maxReachedTime.value)
    videoPlayer.value.currentTime = seekTime
    sliderValue.value = seekTime
    currentTime.value = seekTime

    isDragging.value = false
    startControlsTimer()
}

// 处理时间更新
const handleTimeUpdate = () => {
    if (!videoPlayer.value || isDragging.value) return

    currentTime.value = videoPlayer.value.currentTime
    sliderValue.value = currentTime.value

    // 更新最大观看时间
    if (currentTime.value > maxReachedTime.value) {
        maxReachedTime.value = currentTime.value
        savePlayTime(maxReachedTime.value)
    }

    // 自动隐藏控制条
    if (isPlaying.value) {
        startControlsTimer()
    }
}

// 处理音量变化
const handleVolumeChange = (value: number) => {
    if (!videoPlayer.value) return

    videoPlayer.value.volume = value
    isMuted.value = value === 0
}

// 静音切换
const toggleMute = () => {
    if (!videoPlayer.value) return

    isMuted.value = !isMuted.value
    videoPlayer.value.muted = isMuted.value

    if (isMuted.value) {
        volume.value = 0
    } else {
        volume.value = 0.5
    }
    videoPlayer.value.volume = volume.value

    startControlsTimer()
}

// 全屏切换
const toggleFullscreen = () => {
    if (!videoPlayer.value) return

    const container = videoPlayer.value.parentElement
    if (!container) return

    if (!document.fullscreenElement) {
        container.requestFullscreen().catch(err => {
            console.error(`全屏错误: ${err.message}`)
        })
    } else {
        document.exitFullscreen()
    }

    startControlsTimer()
}

// 控制条自动隐藏计时器
const startControlsTimer = () => {
    if (controlsTimer.value) {
        clearTimeout(controlsTimer.value)
    }

    showControls.value = true
    controlsTimer.value = setTimeout(() => {
        if (isPlaying.value) {
            showControls.value = false
        }
    }, 3000)
}

// 初始化播放器
const initPlayer = () => {
    if (!videoPlayer.value) return

    // 设置初始时间为上次观看时间
    if (lastPlayedTime.value > 0) {
        videoPlayer.value.currentTime = lastPlayedTime.value
    }

    // 设置初始音量
    videoPlayer.value.volume = volume.value

    // 监听视频加载元数据
    videoPlayer.value.addEventListener('loadedmetadata', () => {
        duration.value = videoPlayer.value?.duration || 0
    })

    // 监听播放结束
    videoPlayer.value.addEventListener('ended', () => {
        isPlaying.value = false
    })

    // 开始控制条隐藏计时
    startControlsTimer()
}

onMounted(() => {
    loadLastPlayedTime()
    initPlayer()
})

// 当视频源变化时重置状态
watch(
    () => props.videoSrc,
    () => {
        loadLastPlayedTime()
        initPlayer()
    },
)
</script>

<style lang="less" scoped>
.video-container {
    position: relative;
    padding: 1px;
    background-color: #000;

    &:hover {
        .custom-controls {
            opacity: 1;
        }
    }

    video {
        width: 100%;
        display: block;
        cursor: pointer;
    }

    .custom-controls {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;

        padding: 10px;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        opacity: 0;
        transition: opacity 0.3s;

        display: flex;
        align-items: center;
        gap: 10px;

        button {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 18px;
            padding: 5px;
            border-radius: 4px;

            &:hover {
                background: rgba(255, 255, 255, 0.2);
            }
        }

        .progress-container {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 10px;

            .time {
                color: white;
                font-size: 12px;
                min-width: 40px;
            }

            :deep(.el-slider) {
                flex: 1;

                .el-slider__runway {
                    background-color: rgba(255, 255, 255, 0.3);
                    height: 4px;
                }

                .el-slider__bar {
                    height: 4px;
                    background-color: #409eff;
                }

                .el-slider__button {
                    width: 12px;
                    height: 12px;
                    border: 2px solid #409eff;
                }
            }
        }

        .volume-control {
            position: relative;

            :deep(.el-popper) {
                box-shadow: none;
                border: none;
                padding: 0;
                min-width: auto;
                background-color: transparent;
            }

            :deep(.el-slider) {
                --el-slider-button-size: 10px;
                --el-slider-button-wrapper-size: 10px;
                --el-slider-button-wrapper-offset: -2px;

                .el-slider__button {
                    display: block;
                }
            }
        }
    }

    .last-time {
        position: absolute;
        top: 10px;
        left: 10px;

        border-radius: 4px;
        padding: 4px 8px;
        background: rgba(0, 0, 0, 0.5);
        color: white;
        font-size: 12px;
    }
}
</style>
