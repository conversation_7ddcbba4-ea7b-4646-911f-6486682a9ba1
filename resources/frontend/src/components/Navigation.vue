<template>
    <div class="navigation-card card-box">
        <h5 class="card-title">
            <el-icon size="20" color="#1C77C3"><Operation /></el-icon>
            導覽
        </h5>
        <div class="card-text">
            <el-menu :default-active="activeIndex" class="el-menu-vertical-demo" @open="handleOpen" :default-openeds="openedMenus" v-if="menuReady" v-loading="courseListLoading">
                <el-sub-menu index="1">
                    <template #title>
                        <span @click="goPage('/')">首页</span>
                    </template>
                    <el-menu-item index="1-1" @click="goPage('/my')">
                        <el-icon color="#4c4c4c" size="16"><Grid /></el-icon>
                        <span>仪表盘</span>
                    </el-menu-item>
                    <el-sub-menu index="1-2">
                        <template #title>
                            <span>网站页面</span>
                        </template>
                        <el-menu-item index="1-2-1" @click="goPage('/tag/search')">
                            <el-icon color="#4c4c4c" size="16"><CollectionTag /></el-icon>
                            <span>标签</span>
                        </el-menu-item>
                        <el-menu-item index="1-2-2" @click="goPage('/calendar/view')">
                            <el-icon color="#4c4c4c" size="16"><Calendar /></el-icon>
                            <span>行事历</span>
                        </el-menu-item>
                        <el-menu-item index="1-2-3" @click="goPage('modForum', { id: 33 })">
                            <el-icon color="#4c4c4c" size="16"><ChatLineSquare /></el-icon>
                            <span>Site announcements</span>
                        </el-menu-item>
                        <el-menu-item index="1-2-4" @click="goPage('/mod/book')">
                            <el-icon color="#4c4c4c" size="16"><Memo /></el-icon>
                            <span>常見問題 FAQ</span>
                        </el-menu-item>
                        <el-menu-item index="1-2-5" @click="goPage('/mod/page')">
                            <el-icon color="#4c4c4c" size="16"><Document /></el-icon>
                            <span>聯絡我們</span>
                        </el-menu-item>
                    </el-sub-menu>
                    <el-sub-menu index="1-3">
                        <template #title>
                            <span>我的课程</span>
                        </template>
                        <el-sub-menu :index="`1-3-${courseItem.id}`" v-for="courseItem in courseList" :key="courseItem.id" @click="getUnitList(courseItem.id)">
                            <template #title>
                                <span @click="goPage('course', { id: courseItem.id })" :title="courseItem.name">{{ courseItem.name }}</span>
                            </template>
                            <el-sub-menu :index="`1-3-${courseItem.id}-1`" @click="getCourseMembersList(courseItem.id)">
                                <template #title>
                                    <span @click="goPage('/user/member')">成员</span>
                                </template>
                                <template v-if="courseItem.members && courseItem.members.length">
                                    <el-menu-item :index="`1-3-1-1-${member.id}`" v-for="member in courseItem.members" :key="member.id" @click="goPage(`/user/${member.id}`)">
                                        <span>{{ member.first_name }}{{ member.last_name }}</span>
                                    </el-menu-item>
                                </template>
                                <el-empty v-else description="暂无成员" image=" " />
                            </el-sub-menu>
                            <el-menu-item :index="`1-3-${courseItem.id}-2`">
                                <el-icon color="#4c4c4c" size="16"><Finished /></el-icon>
                                <span>能力</span>
                            </el-menu-item>
                            <el-menu-item :index="`1-3-${courseItem.id}-3`" @click="goPage(`/grade/report/overview?id=${courseItem.id}`)">
                                <el-icon color="#4c4c4c" size="16"><Files /></el-icon>
                                <span>成绩</span>
                            </el-menu-item>
                            <el-sub-menu :index="`1-3-${courseItem.id}-4`">
                                <template #title>
                                    <span>通用</span>
                                </template>
                                <el-menu-item index="1-3-1-4-1" @click="goPage('modForum', { id: 33292 })">
                                    <el-icon color="#80BFF5" size="16"><ChatLineSquare /></el-icon>
                                    <span>課程公告</span>
                                </el-menu-item>
                                <el-menu-item index="1-3-1-4-2" @click="goPage('modForum', { id: 33293 })">
                                    <el-icon color="#80BFF5" size="16"><ChatLineSquare /></el-icon>
                                    <span>課程討論區</span>
                                </el-menu-item>
                            </el-sub-menu>
                            <el-sub-menu :index="`1-3-${courseItem.id}-${unit.id + 5}`" v-for="unit in courseItem.units" :key="unit.id">
                                <template #title>
                                    <span :title="locale === 'zh-hk' ? unit.title_cn : unit.title_en">{{ locale === 'zh-hk' ? unit.title_cn : unit.title_en }}</span>
                                </template>
                                <template v-if="unit.resources && unit.resources.length">
                                    <el-menu-item :index="`1-3-1-5-${resources.id}`" v-for="resources in unit.resources" :key="resources.id">
                                        <el-icon color="#F23830" v-if="resources.type === 'file'" size="16"><Document /></el-icon>
                                        <el-icon color="#00A8FF" v-else-if="resources.type === 'video'" size="16"><VideoPlay /></el-icon>
                                        <el-icon color="#F23830" v-else-if="resources.type === 'folder'" size="16"><Document /></el-icon>
                                        <el-icon color="#80BFF5" v-else-if="resources.type === 'quiz'" size="16"><ChatLineSquare /></el-icon>
                                        <el-icon color="#80BFF5" v-else-if="resources.type === 'report'" size="16"><ChatLineSquare /></el-icon>
                                        <span :title="resources.title">{{ resources.title }}</span>
                                    </el-menu-item>
                                </template>
                                <el-empty v-else description="暂无内容" image=" " />
                                <!-- <el-menu-item index="1-3-1-5-1">
                                    <el-icon color="#F23830" size="16"><Document /></el-icon>
                                    <span>pdf</span>
                                </el-menu-item>
                                <el-menu-item index="1-3-1-5-2" @click="goPage('modVideoTime', { id: 1 })">
                                    <el-icon color="#00A8FF" size="16"><VideoPlay /></el-icon>
                                    <span>video</span>
                                </el-menu-item>
                                <el-menu-item index="1-3-1-5-3" @click="goPage('folderView', { id: 1 })">
                                    <el-icon color="#F23830" size="16"><Document /></el-icon>
                                    <span>folder</span>
                                </el-menu-item>
                                <el-menu-item index="1-3-1-5-4" @click="goPage('quizView', { id: 1 })">
                                    <el-icon color="#80BFF5" size="16"><ChatLineSquare /></el-icon>
                                    <span>quiz Quiz</span>
                                </el-menu-item>
                                <el-menu-item index="1-3-1-5-5" @click="goPage('assignView', { id: 1 })">
                                    <el-icon color="#80BFF5" size="16"><ChatLineSquare /></el-icon>
                                    <span>反思报考</span>
                                </el-menu-item> -->
                            </el-sub-menu>
                        </el-sub-menu>
                    </el-sub-menu>
                </el-sub-menu>
            </el-menu>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

import { useI18n } from 'vue-i18n'
import type { SupportedLocales } from '@/locales/i18n'
import type { MessageSchema } from '@/types/i18n'

import CourseService from '@/api/course'
import type { CourseItem } from '@/types/course'

import UserService from '@/api/user'

import UnitService from '@/api/unit'

import { goPage } from '@/utils/global'

const route = useRoute()
const router = useRouter()
const menuReady = ref(false)
const { locale } = useI18n<{ message: MessageSchema }, SupportedLocales>()

const activeIndex = ref('1')
const openedMenus = ref(['1'])
// 建立路由路径与菜单index的映射关系
const routeToMenuMap: { [key: string]: string } = {
    '/': '1',
    '/my': '1-1',
    '/tag/search': '1-2-1',
    '/calendar/view': '1-2-2',
    '/mod/forum/33': '1-2-3',
    '/mod/book': '1-2-4',
    '/mod/page': '1-2-5',

    // '/course/1': '1-3-1',
    // '/user/member': '1-3-1-1',
    // '/grade/report/overview': '1-3-1-3',
    // '/grade/report/user': '1-3-1-3',
    // '/mod/forum/33292': '1-3-1-4-1',
    // '/mod/forum/33293': '1-3-1-4-2',
    // '/mod/videoTime/1': '1-3-1-5-3',
}
// 根据当前路由设置激活状态和展开菜单
const setMenuState = async () => {
    const menuIndex = routeToMenuMap[route.path]
    if (!menuIndex) return

    activeIndex.value = menuIndex

    // 设置需要展开的菜单（所有父级菜单）
    const segments = menuIndex.split('-')
    const parents = []
    for (let i = 1; i < segments.length; i++) {
        parents.push(segments.slice(0, i).join('-'))
    }
    openedMenus.value = parents
}

const handleOpen = (key: string, keyPath: string[]) => {
    console.log(key, keyPath)
}

// 我的课程
const courseList = ref<CourseItem[]>([])
const courseListLoading = ref(false)
function getCourseList() {
    courseListLoading.value = true
    CourseService.courseList()
        .then(res => {
            courseList.value = res
        })
        .finally(() => {
            courseListLoading.value = false
            menuReady.value = true
        })
}
// 成员列表
const getCourseMembersList = async (courseId: number) => {
    const course = courseList.value.find(item => item.id === courseId)
    if (course) course.members = await UserService.courseMembers(course.id)
}
// 单元列表
const getUnitList = async (courseId: number) => {
    const course = courseList.value.find(item => item.id === courseId)
    if (course) course.units = await UnitService.unitList(course.id)
}

// 路由变化时更新
watch(() => route.path, setMenuState)

onMounted(() => {
    setMenuState()
    getCourseList()
})
</script>

<style lang="less" scoped>
.card-box::before {
    background-color: #1c77c3;
}

.el-menu {
    --el-menu-bg-color: transparent; // 背景色
    --el-menu-hover-bg-color: transparent; // hover时的行背景色
    --el-menu-base-level-padding: 1px;
    --el-menu-level-padding: 12px; // 左右内边距
    --el-menu-item-font-size: 15px; // 字体大小
    --el-menu-item-height: 25px; // 一级导航行高
    --el-menu-sub-item-height: 25px; // 二级导航行高

    border-right: none;

    .el-sub-menu {
        :deep(.el-menu) {
            padding: 4px 0;
        }

        :deep(.el-sub-menu__title) {
            flex-direction: row-reverse;
            justify-content: flex-end;

            & > span {
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .el-sub-menu__icon-arrow {
                margin-right: 6px;
                margin-top: 0;
                position: static;
                transform: rotate(-90deg) !important;
            }
        }

        &.is-opened {
            & > :deep(.el-sub-menu__title) {
                .el-sub-menu__icon-arrow {
                    transform: rotate(0) !important;
                }
            }
        }
    }

    .el-menu-item {
        & > span {
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

:deep(.el-empty) {
    --el-empty-padding: 0;
    --el-empty-description-margin-top: 0;

    .el-empty__image {
        display: none;
    }
}
</style>
