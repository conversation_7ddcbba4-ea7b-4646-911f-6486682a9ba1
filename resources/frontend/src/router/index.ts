import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useRoleStore } from '@/stores/role'
import { useUserStore } from '@/stores/user'

import HomeView from '../views/Home.vue'

const routes = [
    {
        path: '/',
        name: 'home',
        component: HomeView,
        meta: { requiresAuth: true },
    },

    // user
    {
        path: '/user/:id',
        name: 'user',
        component: () => import('../views/user/index.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/user/profile',
        name: 'userProfile',
        component: () => import('../views/user/Profile.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/user/preferences',
        name: 'userPreferences',
        component: () => import('../views/user/Preferences.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/user/edit',
        name: 'userEdit',
        component: () => import('../views/user/Edit.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/user/changePwd',
        name: 'userChangePwd',
        component: () => import('../views/user/ChangePwd.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/user/language',
        name: 'userLanguage',
        component: () => import('../views/user/Language.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/user/forum',
        name: 'userForum',
        component: () => import('../views/user/Forum.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/user/editor',
        name: 'userEditor',
        component: () => import('../views/user/Editor.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/user/calendar',
        name: 'userCalendar',
        component: () => import('../views/user/Calendar.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/user/contentbank',
        name: 'userContentbank',
        component: () => import('../views/user/Contentbank.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/user/member',
        name: 'userMember',
        component: () => import('../views/user/Member.vue'),
        meta: { requiresAuth: true },
    },

    // my
    {
        path: '/my',
        name: 'my',
        component: () => import('../views/my/index.vue'),
        meta: { requiresAuth: true },
    },

    // message
    {
        path: '/message',
        name: 'message',
        component: () => import('../views/message/index.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/message/notifications',
        name: 'messageNotifications',
        component: () => import('../views/message/Notifications.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/message/notificationPreferences',
        name: 'messageNotificationPreferences',
        component: () => import('../views/message/NotificationPreferences.vue'),
        meta: { requiresAuth: true },
    },

    // grade
    {
        path: '/grade/report/overview',
        name: 'gradeReportOverview',
        component: () => import('../views/grade/Report/Overview/index.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/grade/report/user',
        name: 'gradeReportUser',
        component: () => import('../views/grade/Report/User/index.vue'),
        meta: { requiresAuth: true },
    },

    // tag
    {
        path: '/tag/search',
        name: 'tagSearch',
        component: () => import('../views/tag/Search.vue'),
        meta: { requiresAuth: true },
    },

    // calendar
    {
        path: '/calendar/view',
        name: 'calendarView',
        component: () => import('../views/calendar/View.vue'),
        meta: { requiresAuth: true },
    },

    // mode
    {
        path: '/mod/forum/:id',
        name: 'modForum',
        component: () => import('../views/mod/Forum/index.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/mod/forum/discuss/:id',
        name: 'modForumDiscuss',
        component: () => import('../views/mod/Forum/Discuss.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/mod/forum/search',
        name: 'modForumSearch',
        component: () => import('../views/mod/Forum/Search.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/mod/book',
        name: 'modBook',
        component: () => import('../views/mod/Book/index.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/mod/page',
        name: 'modPage',
        component: () => import('../views/mod/Page/index.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/mod/videoTime/:id',
        name: 'modVideoTime',
        component: () => import('../views/mod/VideoTime/index.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/mod/quiz/view/:id',
        name: 'modQuizView',
        component: () => import('../views/mod/Quiz/View.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/mod/quiz/attempt/:id',
        name: 'modQuizAttempt',
        component: () => import('../views/mod/Quiz/Attempt.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/mod/folder/view/:id',
        name: 'modFolderView',
        component: () => import('../views/mod/Folder/View.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/mod/assign/view/:id',
        name: 'modAssignView',
        component: () => import('../views/mod/Assign/View.vue'),
        meta: { requiresAuth: true },
    },

    // course
    {
        path: '/course/:id',
        name: 'course',
        component: () => import('../views/course/index.vue'),
        meta: { requiresAuth: true },
    },
    {
        path: '/course/:id/view/:section',
        name: 'courseView',
        component: () => import('../views/course/View.vue'),
        meta: { requiresAuth: true },
    },

    // 登录
    {
        path: '/login',
        name: 'login',
        component: () => import('../views/Login.vue'),
        meta: {
            requiresAuth: false,
            layout: 'login',
            hideHeader: true,
            hideFooter: true,
        },
    },
]

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes,
})

router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    const roleStore = useRoleStore()

    if (!to.meta.requiresAuth) return next()
    if (!authStore.isAuthenticated) return next('/login')
    // if (roleStore.roles.length === 0) await roleStore.fetchRoles()

    if (to.meta.deniedRoleIds) {
        if (roleStore.isSuperAdmin) return next()
        if (roleStore.currentRoleId && to.meta.deniedRoleIds.includes(roleStore.currentRoleId)) return next('/unauthorized')
    }

    useUserStore().getUserInfoL()
    next()
})

export default router
