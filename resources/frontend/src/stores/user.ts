import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { UserInfo } from '@/types/user'
import UserService from '@/api/user'

export const useUserStore = defineStore('user', () => {
    const userinfoL = ref<UserInfo | undefined>()

    // 获取登录用户信息
    const getUserInfoL = () => {
        UserService.userInfoL().then(res => {
            userinfoL.value = res
        })
    }

    return { userinfoL, getUserInfoL }
})
