import type { UserInfo, CourseMember } from '@/types/user'
import http from './http'

const UserService = {
    userInfo: async (id: number): Promise<UserInfo> => {
        const res = await http.get(`/course/user/${id}/info`)
        return res.data.data
    },
    userInfoL: async (): Promise<UserInfo> => {
        const res = await http.get('/course/user/user-info')
        return res.data.data
    },
    courseMembers: async (courseId: number): Promise<CourseMember[]> => {
        const res = await http.get(`/course/user/course-members?course_id=${courseId}`)
        return res.data.data
    },
}

export default UserService
