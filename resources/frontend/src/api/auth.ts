import http from './http'
import { useAuthStore } from '@/stores/auth'
import { useRoleStore } from '@/stores/role'
import { useUserStore } from '@/stores/user'
import { apiMsgProp } from '@/utils/global'
import { useRoute, useRouter } from 'vue-router'

const AuthService = {
    // 登录
    login: async (credentials: { account: string; password: string }) => {
        const response = await http.post('/course/auth/login', credentials)
        if (response.data.code === 200) {
            const { token, user, token_expired_at } = response.data.data
            useAuthStore().setToken(token, token_expired_at)
            useRoleStore().setCurrentRole(user.role_id)
            useUserStore().userinfoL = user
        } else {
            apiMsgProp(response.data.code, response.data.message)
        }
        return response.data.data
    },
    // 登出
    logout: () => {
        useAuthStore().clearToken()
        useRoleStore().setCurrentRole(null)
        const route = useRoute()
        const router = useRouter()
        localStorage.setItem('redirectPath', route.fullPath)
        router.push('/login')
    },
    // 刷新token接口
    refreshToken: async () => {
        const response = await http.post('/course/user/refresh-token')
        if (response.data.code === 200) {
            const { token, token_expired_at } = response.data.data
            useAuthStore().setToken(token, token_expired_at)

            return true
        }
        return false
    },
}

export default AuthService
