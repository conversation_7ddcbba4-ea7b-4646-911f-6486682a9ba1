<template>
    <div class="back-box">
        <div class="back-info">
            <el-icon size="16" color="#4c4c4c" class="cursor" @click="props.closeChat" v-if="props.isBack"><ArrowLeftBold /></el-icon>
            <div class="user-item">
                <div class="user-picture">
                    <!-- <div class="pic">
                        <img src="" alt="">
                    </div> -->
                    <el-icon size="26" color="#fff"><UserFilled /></el-icon>
                </div>
                <div class="user-msg">
                    <span class="user-name">Chan Tai Man 陳大文</span>
                    <span class="msg">上线</span>
                </div>
            </div>
        </div>
        <el-dropdown trigger="click">
            <el-icon size="16" color="#4c4c4c" class="cursor"><MoreFilled /></el-icon>
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item>未标记星号的对话</el-dropdown-item>
                    <el-dropdown-item>删除对话</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </div>
    <div class="chat-list">
        <p class="tips">个人空间</p>
        <p class="tips"><i>存储讯息草稿、链接、备注等，以便日后存取。</i></p>
        <div class="msg-item time">02月12日</div>
        <div class="msg-item msg" @click="isChooseChat = true">
            <span class="msg-time">
                01:52
                <el-radio v-model="chooseChat" size="small" v-if="isChooseChat"></el-radio>
            </span>
            <span class="msg-text">11</span>
        </div>
        <div class="msg-item msg" @click="isChooseChat = true">
            <span class="msg-time">
                01:52
                <el-radio v-model="chooseChat" size="small" v-if="isChooseChat"></el-radio>
            </span>
            <span class="msg-text">表情</span>
        </div>
        <div class="msg-item time">04月18日</div>
        <div class="msg-item msg" @click="isChooseChat = true">
            <span class="msg-time">
                08:13
                <el-radio v-model="chooseChat" size="small" v-if="isChooseChat"></el-radio>
            </span>
            <span class="msg-text">》</span>
        </div>
        <div class="msg-item msg" @click="isChooseChat = true">
            <span class="msg-time">
                16:29
                <el-radio v-model="chooseChat" size="small" v-if="isChooseChat"></el-radio>
            </span>
            <span class="msg-text">？</span>
        </div>
    </div>
    <div class="chat-send-box">
        <el-input v-model="chatText" :rows="4" type="textarea" placeholder="撰写一讯息..." @keyup.enter="chatSendHandle" />
        <div class="tools-chat">
            <el-icon size="20" color="#4c4c4c"><Cherry /></el-icon>
            <el-icon size="20" color="#4c4c4c" @click="chatSendHandle"><Promotion /></el-icon>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const props = defineProps<{
    isBack?: Boolean
    closeChat?: () => void
}>()

const chooseChat = ref(false)
const isChooseChat = ref(false)
const chatText = ref('')
const chatSendHandle = () => {}
</script>

<style lang="less" scoped>
.back-box {
    border-bottom: 1px solid #dee2e6 !important;
    padding: 0 10px;
    font-size: 15px;
    color: #4c4c4c;
    background-color: #eff1f3;
    min-height: 44px;

    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .cursor {
        cursor: pointer;
    }

    .back-info {
        display: flex;
        align-items: center;
        gap: 4px;
    }
}

.user-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.085);
    padding: 8px;

    display: flex;
    align-items: center;

    &:last-child {
        border: none;
    }

    .user-picture {
        border-radius: 50%;
        background-color: #e5e5e5;
        width: 38px;
        height: 38px;

        display: flex;
        align-items: center;
        justify-content: center;
    }

    .user-msg {
        padding-left: 8px;
        flex-grow: 1;
        color: #4c4c4c;
        font-size: 15px;
        line-height: 1.666;

        .user-name {
            font-weight: bold;
            display: block;
        }

        .msg {
            display: block;
        }
    }

    .time {
        font-size: 12px;
        color: #4c4c4c;
    }
}

.chat-list {
    padding: 15px;
    height: 100%;
    overflow-y: auto;
    background-color: #f8f9fa;

    .tips {
        font-size: 12px;
        color: #4c4c4c;
        text-align: center;
        line-height: 1.75;

        &:first-child {
            margin-top: 15px;
        }
    }

    .msg-item {
        margin-bottom: 8px;
        color: #212529;

        &.time {
            margin-top: 15px;
            font-size: 15px;
            text-align: center;
        }

        &.msg {
            border-radius: 4px;
            border: 2px solid #d5d5d5;
            padding: 8px;
            background-color: #dee2e6;
            cursor: pointer;
            transition: all 0.35s ease-in-out;

            display: flex;
            flex-direction: column;

            .msg-time {
                text-align: right;
                font-size: 12px;
                line-height: 1.75;

                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 10px;
            }

            .msg-text {
                font-size: 15px;
                line-height: 1.75;
            }

            &:hover {
                filter: drop-shadow(2px 2px 2px rgba(0, 0, 0, 0.3));
                border-color: #fb8b24;
            }
        }
    }

    .tips + .msg-item {
        margin-top: 30px;
    }
}

.chat-send-box {
    border-top: 1px solid #d5d5d5;
    padding: 15px;

    display: flex;

    .el-textarea {
        margin-right: 15px;
        flex-grow: 1;
        font-size: 15px;

        --el-input-border-radius: 0;
        --el-input-focus-border: #f06423;
        --el-input-focus-border-color: #f06423;
    }

    .tools-chat {
        padding: 10px 0;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .el-icon {
            cursor: pointer;
            transition: color 0.35s ease-in-out;

            &:hover {
                color: #f06423;
            }
        }
    }
}
</style>
