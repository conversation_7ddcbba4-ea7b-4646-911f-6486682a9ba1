<template>
    <div class="search-box">
        <el-input v-model="searchKey" placeholder="回车搜索" suffix-icon="Search" @keyup.enter="handleSearch" />
        <el-button @click="closeChatSearch" v-if="isBack">返回</el-button>
    </div>
    <div class="search-list">
        <!-- <el-empty :description="searchEmptyDesc" image=" " /> -->
        <p>不在通讯录</p>
        <div class="user-list">
            <div class="user-item" @click="props.openChat">
                <div class="user-picture">
                    <!-- <div class="pic">
                        <img src="" alt="">
                    </div> -->
                    <el-icon size="26" color="#fff"><UserFilled /></el-icon>
                </div>
                <div class="user-msg">
                    <span class="user-name">Chan Tai Man 陳大文</span>
                </div>
            </div>
            <div class="user-item" @click="props.openChat">
                <div class="user-picture">
                    <!-- <div class="pic">
                        <img src="" alt="">
                    </div> -->
                    <el-icon size="26" color="#fff"><UserFilled /></el-icon>
                </div>
                <div class="user-msg">
                    <span class="user-name">Chan Tai Man 陳大文</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

const props = defineProps<{
    isBack?: Boolean
    openChat?: () => void
    closeChatSearch?: () => void
}>()

const searchKey = ref<string>('')
const searchEmptyDesc = ref<string>('搜索人员和信息')
const handleSearch = () => {
    searchEmptyDesc.value = '没有结果'
}
</script>

<style lang="less" scoped>
.search-box {
    border-bottom: 1px solid #d5d5d5;
    padding: 15px;
    background-color: #eff1f3;
    flex-shrink: 0;

    display: flex;
    gap: 10px;

    .el-button {
        --el-button-hover-bg-color: rgba(240, 100, 35, 0.1);
        --el-button-hover-border-color: rgba(240, 100, 35, 0.6);
        --el-button-hover-text-color: rgba(240, 100, 35);
    }
}

.search-list {
    flex-grow: 1;
    overflow-y: auto;
    background-color: #eff1f3;

    p {
        padding: 15px 15px 10px;
        font-size: 14px;
        color: #242424;
        background-color: #fff;
    }

    .user-list {
        .user-item {
            border-bottom: 1px solid rgba(0, 0, 0, 0.085);
            padding: 8px;
            cursor: pointer;
            background-color: #fff;

            display: flex;
            align-items: center;

            &:hover {
                background-color: #f8f9fa;
            }

            &:last-child {
                border: none;
            }

            .user-picture {
                border-radius: 50%;
                background-color: #e5e5e5;
                width: 38px;
                height: 38px;

                display: flex;
                align-items: center;
                justify-content: center;
            }

            .user-msg {
                padding-left: 8px;
                flex-grow: 1;
                color: #4c4c4c;
                font-size: 15px;
                line-height: 1.666;

                .user-name {
                    font-weight: bold;
                    display: block;
                }

                .msg {
                    display: block;
                }
            }

            .time {
                font-size: 12px;
                color: #4c4c4c;
            }
        }
    }
}
</style>
