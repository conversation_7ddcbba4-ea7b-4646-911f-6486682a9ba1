<template>
    <Banner />
    <Breadcrumb />
    <div class="page-box user-page">
        <div class="container-fluid">
            <main>
                <div class="left-box">
                    <CourseContacts />
                    <Navigation />
                </div>

                <div class="right-box">
                    <template v-if="userinfo">
                        <div class="user-info">
                            <div class="user-picture">
                                <div class="pic" v-if="userinfo.avatar_url">
                                    <img :src="userinfo.avatar_url" :alt="userinfo.avatar_description" class="ele" />
                                </div>
                                <el-icon v-else size="50" color="#fff"><User /></el-icon>
                            </div>
                            <div class="fullname"><PERSON>u <PERSON> {{ userinfo.first_name }}{{ userinfo.last_name }} Wong</div>
                            <el-button color="#435764">
                                <el-icon size="16"><Comment /></el-icon>
                                讯息
                            </el-button>
                            <el-button color="#435764" v-if="userStore.userinfoL?.id !== userinfo.id">
                                <el-icon size="16"><Remove /></el-icon>
                                從您的通訊錄中移除
                            </el-button>
                        </div>
                        <div class="profile-tree">
                            <div class="node-category">
                                <h3 class="lead">用戶的詳細資料</h3>
                                <ul>
                                    <li class="content-node">
                                        <dl>
                                            <dt>編號</dt>
                                            <dd>{{ userinfo.code }}</dd>
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                            <div class="node-category">
                                <h3 class="lead">課程細節</h3>
                                <ul>
                                    <li class="content-node">
                                        <dl>
                                            <dt>課程簡介</dt>
                                            <dd>
                                                <router-link to="/">2425-BC-DY-1</router-link>
                                            </dd>
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                            <div class="node-category">
                                <h3 class="lead">其它</h3>
                                <ul>
                                    <li>
                                        <RouterLink to="/">討論區文章</RouterLink>
                                    </li>
                                    <li>
                                        <RouterLink to="/">討論區議題</RouterLink>
                                    </li>
                                </ul>
                            </div>
                            <div class="node-category">
                                <h3 class="lead">登入活動</h3>
                                <ul>
                                    <li class="content-node">
                                        <dl>
                                            <dt>首次登入網站時間</dt>
                                            <dd>{{ moment(userinfo.created_at).locale(locale).format('YYYY年 MM月 DD日(ddd) HH:mm') }} ({{ calculatedDuration(userinfo.created_at) }})</dd>
                                        </dl>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </template>
                    <el-empty v-else description="暂未找到该用户" />
                </div>
            </main>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'

import { useI18n } from 'vue-i18n'
import type { SupportedLocales } from '@/locales/i18n'
import type { MessageSchema } from '@/types/i18n'

import { useUserStore } from '@/stores/user'

import type { UserInfo } from '@/types/user.ts'
import UserService from '@/api/user'

import Banner from '@/components/Banner.vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import Navigation from '@/components/Navigation.vue'
import CourseContacts from '@/components/CourseContacts.vue'
import moment from 'moment'

const route = useRoute()
const userId = Number(route.params.id)
const loading = ref(false)
const userStore = useUserStore()
const { locale } = useI18n<{ message: MessageSchema }, SupportedLocales>()

const userinfo = ref<UserInfo | null>()
function getUserInfo() {
    loading.value = true
    UserService.userInfo(userId)
        .then(res => {
            userinfo.value = res
        })
        .finally(() => {
            loading.value = false
        })
}
function calculatedDuration(date: string | Date) {
    const createdAt = moment(date)
    const now = moment()
    const duration = moment.duration(now.diff(createdAt))

    const durationText = `(${Math.floor(duration.asDays())} 日 ${duration.hours()} 小時)`

    return durationText
}

onMounted(() => {
    getUserInfo()
})
</script>

<style lang="less" scoped>
main {
    display: flex;
    align-items: flex-start;
    width: 100%;

    .left-box {
        width: 25%;

        .faq-list-box {
            margin-bottom: 24px;
        }
    }

    .right-box {
        margin-left: 30px;
        width: calc(75% - 30px);

        .user-info {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 12px;

            .user-picture {
                border-radius: 50%;
                width: 84px;
                height: 84px;
                background-color: #e6e6e6;

                display: flex;
                align-items: center;
                justify-content: center;
            }

            .fullname {
                font-weight: 400;
                color: #000;
                font-size: 26px;
                line-height: 1.2;
            }

            :deep(.el-button) {
                & > span {
                    gap: 4px;
                }
            }
        }

        .profile-tree {
            column-count: 2;

            .node-category {
                margin-bottom: 15px;
                border-radius: 2px;
                padding: 15px;
                border: 1px solid #e3e3e3;
                background-color: #fcfcfc;

                .lead {
                    margin-bottom: 18px;
                    border-radius: 2px;
                    border-left: 2px solid #f06423;
                    padding: 4px 15px;
                    font-size: 18px;
                    font-weight: 400;
                    background-color: rgba(0, 0, 0, 0.05);
                    background-image: url(@/assets/img/strip2_black8.svg);
                }

                ul {
                    li {
                        margin-bottom: 5px;
                        color: #4c4c4c;
                        font-size: 15px;
                        line-height: 1.666;

                        a {
                            color: #4c4c4c;
                            transition: color 0.35s ease-in-out;

                            &:hover {
                                color: #f06423;
                            }
                        }

                        &.content-node {
                            dl {
                                margin-bottom: 15px;

                                dt {
                                    color: #000;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
