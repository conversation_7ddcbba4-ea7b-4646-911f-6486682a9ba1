export interface UserInfo {
    id: number
    account: string
    role_id: number
    first_name: string
    last_name: string
    first_initial: string
    last_initial: string
    email: string
    show_email_type: number
    moodlenet_account: string
    city_address: string
    country: string
    timezone: string
    introduction: string
    code: string
    avatar_url: string
    avatar_description: string
    interests: string
    phone_number: string
    mobile_number: string
    detailed_address: string
    department: string
    institution: null
    additional_last_name: string
    additional_first_name: string
    additional_first_initial: null
    additional_last_initial: null
    additional_middle_name: string
    additional_alias: string
    status: number
    last_login_time: string
    creator_id: number
    created_at: string
    updated_at: string
}

export interface CourseMember {
    email: string
    first_name: string
    id: number
    last_name: string
}
