import type { UnitItem } from './unit'
import type { CourseMember } from './user'

export interface CourseItem {
    id: number
    name: string
    short_name: string
    category_id: number
    status: number
    is_show: number
    start_date: string
    end_date: string
    course_id: string
    is_user: number
    roles: string
    image_url: string
    description: string
    sort_order: number
    report_deadline: null
    max_students: number
    is_active: number
    creator_id: number
    created_at: string
    updated_at: string
    category: CourseCategoryItem
    members?: CourseMember[]
    units?: UnitItem[]
}

export interface CourseCategoryItem {
    id: number
    parent_id: number
    name: string
    description: string
    cate_id_num: string
    sort_order: number
    creator_id: number
    created_at: string
    updated_at: string
    course_num: number
}
