<template>
    <div :class="layoutClass">
        <el-config-provider :locale="elementLang">
            <HeaderComp v-if="!hideHeader" />
            <RouterView />
            <FooterComp v-if="!hideFooter" />
        </el-config-provider>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

import { useI18n } from 'vue-i18n'
import type { SupportedLocales } from '@/locales/i18n'
import type { MessageSchema } from './types/i18n'

import elementPlusLangEn from 'element-plus/dist/locale/en'
import elementPlusLangZhHk from 'element-plus/dist/locale/zh-hk'

import HeaderComp from '@/components/HeaderComp.vue'
import FooterComp from './components/FooterComp.vue'

const { locale } = useI18n<{ message: MessageSchema }, SupportedLocales>()
const route = useRoute()
const elementLang = computed(() => {
    return locale.value === 'en' ? elementPlusLangEn : elementPlusLangZhHk
})

const layoutClass = computed(() => {
    return route.meta.layout === 'login' ? 'login-page-container' : 'lms-page-container'
})

const hideHeader = computed(() => {
    if (Object.keys(route.meta).length === 0) return true

    return route.meta.hideHeader
})
const hideFooter = computed(() => {
    if (Object.keys(route.meta).length === 0) return true

    return route.meta.hideFooter
})

onMounted(() => {})
</script>

<style lang="less">
.lms-page-container,
.login-page-container {
    width: 100vw;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;

    display: flex;
    flex-direction: column;

    header,
    footer {
        flex-shrink: 0;
    }

    .page-box {
        padding: 45px 0;
        flex-grow: 1;
    }
}

.login-page-container {
    background: url(@/assets/img/loginBg.jpg) no-repeat center center / cover;
}
</style>
