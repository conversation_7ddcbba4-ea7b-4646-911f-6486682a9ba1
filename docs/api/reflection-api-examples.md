# 反思报告 API 文档和请求示例

## 1. 获取评分标准 API

### 接口信息
- **URL**: `/api/course/reflection/getScoringCriteria`
- **方法**: `GET`
- **描述**: 获取反思报告的评分标准

### 请求示例

#### cURL 请求
```bash
curl -X GET "http://localhost/api/course/reflection/getScoringCriteria" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### JavaScript (Axios) 请求
```javascript
const response = await axios.get('/api/course/reflection/getScoringCriteria', {
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN_HERE'
  }
});
```

### 响应示例

#### 成功响应 (200 OK)
```json
{
  "criteria": [
    {
      "name": "understanding",
      "title": "理解能力",
      "positive": "对相关理论和题目展示足够的理解",
      "negative": "对相关理论和题目不甚理解/误解"
    },
    {
      "name": "cognition",
      "title": "认知能力",
      "positive": "展示合理的分析、批判性思维及综合能力",
      "negative": "未能展示任何分析、批判性思维及综合能力"
    },
    {
      "name": "discussion",
      "title": "讨论能力",
      "positive": "讨论清晰、具合理思路及理据",
      "negative": "讨论散乱、欠缺逻辑及理据"
    },
    {
      "name": "writing",
      "title": "写作能力",
      "positive": "结构清晰、表达准确、文笔流畅",
      "negative": "文不达意，难于理解"
    }
  ]
}
```

#### 英文响应示例 (Accept-Language: en)
```json
{
  "criteria": [
    {
      "name": "understanding",
      "title": "Understanding",
      "positive": "Demonstrates sufficient understanding of relevant theories and topics",
      "negative": "Limited understanding or misunderstanding of relevant theories and topics"
    },
    {
      "name": "cognition",
      "title": "Cognition",
      "positive": "Demonstrates reasonable analysis, critical thinking, and synthesis abilities",
      "negative": "Fails to demonstrate any analysis, critical thinking, or synthesis abilities"
    },
    {
      "name": "discussion",
      "title": "Discussion",
      "positive": "Discussion is clear, with logical flow and reasoning",
      "negative": "Discussion is disorganized, lacks logic and reasoning"
    },
    {
      "name": "writing",
      "title": "Writing",
      "positive": "Clear structure, accurate expression, and fluent writing",
      "negative": "Writing is unclear and difficult to understand"
    }
  ]
}
```

#### 繁体中文响应示例 (Accept-Language: zh-HK)
```json
{
  "criteria": [
    {
      "name": "understanding",
      "title": "理解能力",
      "positive": "對相關理論和題目展示足夠的理解",
      "negative": "對相關理論和題目不甚理解/誤解"
    },
    {
      "name": "cognition",
      "title": "認知能力",
      "positive": "展示合理的分析、批判性思維及綜合能力",
      "negative": "未能展示任何分析、批判性思維及綜合能力"
    },
    {
      "name": "discussion",
      "title": "討論能力",
      "positive": "討論清晰、具合理思路及理據",
      "negative": "討論散亂、欠缺邏輯及理據"
    },
    {
      "name": "writing",
      "title": "寫作能力",
      "positive": "結構清晰、表達準確、文筆流暢",
      "negative": "文不達意，難於理解"
    }
  ]
}
```

## 2. 反思报告评分 API

### 接口信息
- **URL**: `/api/course/reflection/score`
- **方法**: `POST`
- **描述**: 提交反思报告评分

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | integer | 是 | 反思报告ID |
| teacher_comment | string | 否 | 教师评语 |
| score_result | object | 是 | 评分结果 |
| score_result.understanding | object | 是 | 理解能力评分 |
| score_result.understanding.is_passed | integer | 是 | 是否通过 (0=未通过, 1=通过) |
| score_result.understanding.comment | string | 否 | 理解能力评语 |
| score_result.cognition | object | 是 | 认知能力评分 |
| score_result.cognition.is_passed | integer | 是 | 是否通过 (0=未通过, 1=通过) |
| score_result.cognition.comment | string | 否 | 认知能力评语 |
| score_result.discussion | object | 是 | 讨论能力评分 |
| score_result.discussion.is_passed | integer | 是 | 是否通过 (0=未通过, 1=通过) |
| score_result.discussion.comment | string | 否 | 讨论能力评语 |
| score_result.writing | object | 是 | 写作能力评分 |
| score_result.writing.is_passed | integer | 是 | 是否通过 (0=未通过, 1=通过) |
| score_result.writing.comment | string | 否 | 写作能力评语 |

### 请求示例

#### cURL 请求
```bash
curl -X POST "http://localhost/api/course/reflection/score" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "id": 123,
    "teacher_comment": "整体表现良好，需要在批判性思维方面加强。",
    "score_result": {
      "understanding": {
        "is_passed": 1,
        "comment": "对理论理解透彻"
      },
      "cognition": {
        "is_passed": 0,
        "comment": "批判性思维需要加强"
      },
      "discussion": {
        "is_passed": 1,
        "comment": "讨论逻辑清晰"
      },
      "writing": {
        "is_passed": 1,
        "comment": "文笔流畅，表达准确"
      }
    }
  }'
```

#### JavaScript (Axios) 请求
```javascript
const scoreData = {
  id: 123,
  teacher_comment: "整体表现良好，需要在批判性思维方面加强。",
  score_result: {
    understanding: {
      is_passed: 1,
      comment: "对理论理解透彻"
    },
    cognition: {
      is_passed: 0,
      comment: "批判性思维需要加强"
    },
    discussion: {
      is_passed: 1,
      comment: "讨论逻辑清晰"
    },
    writing: {
      is_passed: 1,
      comment: "文笔流畅，表达准确"
    }
  }
};

const response = await axios.post('/api/course/reflection/score', scoreData, {
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN_HERE'
  }
});
```

### 响应示例

#### 成功响应 (200 OK)
```json
{
  "message": "success",
  "item": {
    "id": 123,
    "status": "scored"
  }
}
```

#### 验证错误响应 (422 Unprocessable Entity)

##### 简体中文错误信息
```json
{
  "message": "验证失败",
  "errors": {
    "id": ["ID不能为空"],
    "score_result.understanding.is_passed": ["理解能力通过状态不能为空"],
    "score_result.cognition.is_passed": ["认知能力通过状态必须为整数"],
    "teacher_comment": ["教师评语必须为字符串"]
  }
}
```

##### 英文错误信息 (Accept-Language: en)
```json
{
  "message": "Validation failed",
  "errors": {
    "id": ["ID is required"],
    "score_result.understanding.is_passed": ["Understanding pass status is required"],
    "score_result.cognition.is_passed": ["Cognition pass status must be an integer"],
    "teacher_comment": ["Teacher comment must be a string"]
  }
}
```

##### 繁体中文错误信息 (Accept-Language: zh-HK)
```json
{
  "message": "驗證失敗",
  "errors": {
    "id": ["ID不能為空"],
    "score_result.understanding.is_passed": ["理解能力通過狀態不能為空"],
    "score_result.cognition.is_passed": ["認知能力通過狀態必須為整數"],
    "teacher_comment": ["教師評語必須為字符串"]
  }
}
```

## 3. 多语言支持

### 语言设置方式

系统通过URL参数 `lang` 来设置语言：

- 有 `lang` 参数：使用指定语言
- 无 `lang` 参数：使用默认语言 `zh_cn` (简体中文)

### 支持的语言

| lang参数值 | 语言名称 | 示例URL |
|------------|----------|---------|
| `zh_cn` | 简体中文 | `?lang=zh_cn` |
| `en` | English | `?lang=en` |
| `zh_hk` | 香港繁體中文 | `?lang=zh_hk` |

### API调用示例

#### 基础API调用
```bash
# 简体中文 (默认)
curl "http://localhost/api/course/reflection/getScoringCriteria"

# 英文
curl "http://localhost/api/course/reflection/getScoringCriteria?lang=en"

# 香港繁体中文
curl "http://localhost/api/course/reflection/getScoringCriteria?lang=zh_hk"

# 简体中文 (显式指定)
curl "http://localhost/api/course/reflection/getScoringCriteria?lang=zh_cn"
```

#### 路径参数格式的API调用
```bash
# 获取课程1中学生7的反思报告 - 简体中文
curl "http://localhost/api/course/reflection/course/1/student/7"

# 获取课程1中学生7的反思报告 - 英文
curl "http://localhost/api/course/reflection/course/1/student/7?lang=en"

# 获取课程1中学生7的反思报告 - 香港繁体中文
curl "http://localhost/api/course/reflection/course/1/student/7?lang=zh_hk"
```

#### JavaScript示例
```javascript
// 基础调用
const getCriteria = async (lang = 'zh_cn') => {
  const response = await axios.get(`/api/course/reflection/getScoringCriteria?lang=${lang}`);
  return response.data;
};

// 获取不同语言的评分标准
const criteriaZhCn = await getCriteria('zh_cn');  // 简体中文
const criteriaEn = await getCriteria('en');       // 英文
const criteriaZhHk = await getCriteria('zh_hk');  // 香港繁体中文

// 路径参数格式
const getStudentReflection = async (courseId, studentId, lang = 'zh_cn') => {
  const response = await axios.get(`/api/course/reflection/course/${courseId}/student/${studentId}?lang=${lang}`);
  return response.data;
};

// 使用示例
const reflection = await getStudentReflection(1, 7, 'zh_hk');
```

## 4. 错误代码说明

| HTTP状态码 | 描述 | 示例场景 |
|------------|------|----------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 请求参数格式错误 |
| 401 | 未授权 | Token无效或过期 |
| 403 | 禁止访问 | 没有权限访问该资源 |
| 404 | 资源不存在 | 反思报告不存在 |
| 422 | 验证失败 | 请求参数验证不通过 |
| 500 | 服务器错误 | 服务器内部错误 |

## 5. 完整的API调用示例

### 获取评分标准 - 完整示例

```bash
#!/bin/bash

# 设置基础变量
BASE_URL="http://localhost"
TOKEN="your_bearer_token_here"

echo "=== 获取评分标准 - 多语言测试 ==="

# 简体中文 (默认)
echo "1. 简体中文 (默认):"
curl -s -X GET "${BASE_URL}/api/course/reflection/getScoringCriteria" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer ${TOKEN}" | jq '.criteria[0].title'

# 简体中文 (显式指定)
echo "2. 简体中文 (显式指定):"
curl -s -X GET "${BASE_URL}/api/course/reflection/getScoringCriteria?lang=zh_cn" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer ${TOKEN}" | jq '.criteria[0].title'

# 英文
echo "3. 英文:"
curl -s -X GET "${BASE_URL}/api/course/reflection/getScoringCriteria?lang=en" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer ${TOKEN}" | jq '.criteria[0].title'

# 香港繁体中文
echo "4. 香港繁体中文:"
curl -s -X GET "${BASE_URL}/api/course/reflection/getScoringCriteria?lang=zh_hk" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer ${TOKEN}" | jq '.criteria[0].title'
```

### 路径参数格式API - 完整示例

```bash
#!/bin/bash

# 设置基础变量
BASE_URL="http://localhost"
TOKEN="your_bearer_token_here"
COURSE_ID=1
STUDENT_ID=7

echo "=== 获取课程学生反思报告 - 多语言测试 ==="

# 简体中文 (默认)
echo "1. 简体中文 (默认):"
curl -s -X GET "${BASE_URL}/api/course/reflection/course/${COURSE_ID}/student/${STUDENT_ID}" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer ${TOKEN}" | jq '.item.current_language'

# 英文
echo "2. 英文:"
curl -s -X GET "${BASE_URL}/api/course/reflection/course/${COURSE_ID}/student/${STUDENT_ID}?lang=en" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer ${TOKEN}" | jq '.item.current_language'

# 香港繁体中文
echo "3. 香港繁体中文:"
curl -s -X GET "${BASE_URL}/api/course/reflection/course/${COURSE_ID}/student/${STUDENT_ID}?lang=zh_hk" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer ${TOKEN}" | jq '.item.current_language'
```

### 提交评分 - 完整示例

```bash
#!/bin/bash

# 提交反思报告评分 - 简体中文
echo "=== 提交评分 - 简体中文 ==="
curl -X POST "${BASE_URL}/api/course/reflection/score?lang=zh_cn" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "id": 123,
    "teacher_comment": "整体表现良好，需要在批判性思维方面加强。",
    "score_result": {
      "understanding": {
        "is_passed": 1,
        "comment": "对理论理解透彻"
      },
      "cognition": {
        "is_passed": 0,
        "comment": "批判性思维需要加强"
      },
      "discussion": {
        "is_passed": 1,
        "comment": "讨论逻辑清晰"
      },
      "writing": {
        "is_passed": 1,
        "comment": "文笔流畅，表达准确"
      }
    }
  }'

# 提交反思报告评分 - 英文
echo "=== 提交评分 - 英文 ==="
curl -X POST "${BASE_URL}/api/course/reflection/score?lang=en" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "id": 124,
    "teacher_comment": "Good overall performance, need to strengthen critical thinking.",
    "score_result": {
      "understanding": {
        "is_passed": 1,
        "comment": "Thorough understanding of theory"
      },
      "cognition": {
        "is_passed": 0,
        "comment": "Critical thinking needs improvement"
      },
      "discussion": {
        "is_passed": 1,
        "comment": "Clear logical discussion"
      },
      "writing": {
        "is_passed": 1,
        "comment": "Fluent and accurate expression"
      }
    }
  }'
```

### 错误处理示例

```javascript
// JavaScript 错误处理示例
async function submitReflectionScore(scoreData, language = 'zh-CN') {
  try {
    const response = await axios.post('/api/course/reflection/score', scoreData, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Accept-Language': language,
        'Authorization': `Bearer ${token}`
      }
    });

    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    if (error.response) {
      // 服务器返回错误响应
      const { status, data } = error.response;

      switch (status) {
        case 422:
          // 验证错误
          return {
            success: false,
            type: 'validation',
            message: data.message,
            errors: data.errors
          };
        case 401:
          // 未授权
          return {
            success: false,
            type: 'auth',
            message: '请重新登录'
          };
        case 404:
          // 资源不存在
          return {
            success: false,
            type: 'not_found',
            message: '反思报告不存在'
          };
        default:
          return {
            success: false,
            type: 'server',
            message: '服务器错误'
          };
      }
    } else {
      // 网络错误
      return {
        success: false,
        type: 'network',
        message: '网络连接失败'
      };
    }
  }
}

// 使用示例
const result = await submitReflectionScore({
  id: 123,
  score_result: {
    understanding: { is_passed: 1, comment: "理解透彻" },
    cognition: { is_passed: 0, comment: "需要加强" },
    discussion: { is_passed: 1, comment: "逻辑清晰" },
    writing: { is_passed: 1, comment: "表达准确" }
  }
}, 'zh-CN');

if (result.success) {
  console.log('评分提交成功:', result.data);
} else {
  console.error('评分提交失败:', result.message);
  if (result.type === 'validation') {
    console.error('验证错误:', result.errors);
  }
}
```

## 6. 注意事项

1. **认证**: 所有API请求都需要在请求头中包含有效的Bearer Token
2. **内容类型**: 请求和响应的Content-Type都应该是`application/json`
3. **字符编码**: 使用UTF-8编码
4. **评分规则**: `is_passed`字段只接受0（未通过）或1（通过）
5. **多语言参数**:
   - 使用URL参数 `lang` 来设置语言
   - 支持的值: `zh_cn`(简体中文), `en`(英文), `zh_hk`(香港繁体中文)
   - 参数值不区分大小写 (`zh_hk` 和 `ZH_HK` 都有效)
   - 无效或缺失的lang参数会回退到默认语言 `zh_cn`
6. **URL格式**:
   - 基础格式: `/api/course/reflection/getScoringCriteria?lang=en`
   - 路径参数格式: `/api/course/reflection/course/1/student/7?lang=zh_hk`
7. **错误处理**: 建议实现完整的错误处理机制，包括网络错误、验证错误等
8. **性能优化**: 对于频繁调用的API，建议实现适当的缓存机制
9. **语言一致性**: 同一个请求中的所有翻译内容（包括错误信息）都会使用相同的语言
