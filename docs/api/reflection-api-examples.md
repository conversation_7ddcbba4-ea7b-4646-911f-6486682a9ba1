# 反思报告 API 文档和请求示例

## 1. 获取评分标准 API

### 接口信息
- **URL**: `/api/course/reflection/getScoringCriteria`
- **方法**: `GET`
- **描述**: 获取反思报告的评分标准

### 请求示例

#### cURL 请求
```bash
curl -X GET "http://localhost/api/course/reflection/getScoringCriteria" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### JavaScript (Axios) 请求
```javascript
const response = await axios.get('/api/course/reflection/getScoringCriteria', {
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN_HERE'
  }
});
```

### 响应示例

#### 成功响应 (200 OK)
```json
{
  "criteria": [
    {
      "name": "understanding",
      "title": "理解能力",
      "positive": "对相关理论和题目展示足够的理解",
      "negative": "对相关理论和题目不甚理解/误解"
    },
    {
      "name": "cognition",
      "title": "认知能力",
      "positive": "展示合理的分析、批判性思维及综合能力",
      "negative": "未能展示任何分析、批判性思维及综合能力"
    },
    {
      "name": "discussion",
      "title": "讨论能力",
      "positive": "讨论清晰、具合理思路及理据",
      "negative": "讨论散乱、欠缺逻辑及理据"
    },
    {
      "name": "writing",
      "title": "写作能力",
      "positive": "结构清晰、表达准确、文笔流畅",
      "negative": "文不达意，难于理解"
    }
  ]
}
```

#### 英文响应示例 (Accept-Language: en)
```json
{
  "criteria": [
    {
      "name": "understanding",
      "title": "Understanding",
      "positive": "Demonstrates sufficient understanding of relevant theories and topics",
      "negative": "Limited understanding or misunderstanding of relevant theories and topics"
    },
    {
      "name": "cognition",
      "title": "Cognition",
      "positive": "Demonstrates reasonable analysis, critical thinking, and synthesis abilities",
      "negative": "Fails to demonstrate any analysis, critical thinking, or synthesis abilities"
    },
    {
      "name": "discussion",
      "title": "Discussion",
      "positive": "Discussion is clear, with logical flow and reasoning",
      "negative": "Discussion is disorganized, lacks logic and reasoning"
    },
    {
      "name": "writing",
      "title": "Writing",
      "positive": "Clear structure, accurate expression, and fluent writing",
      "negative": "Writing is unclear and difficult to understand"
    }
  ]
}
```

#### 繁体中文响应示例 (Accept-Language: zh-HK)
```json
{
  "criteria": [
    {
      "name": "understanding",
      "title": "理解能力",
      "positive": "對相關理論和題目展示足夠的理解",
      "negative": "對相關理論和題目不甚理解/誤解"
    },
    {
      "name": "cognition",
      "title": "認知能力",
      "positive": "展示合理的分析、批判性思維及綜合能力",
      "negative": "未能展示任何分析、批判性思維及綜合能力"
    },
    {
      "name": "discussion",
      "title": "討論能力",
      "positive": "討論清晰、具合理思路及理據",
      "negative": "討論散亂、欠缺邏輯及理據"
    },
    {
      "name": "writing",
      "title": "寫作能力",
      "positive": "結構清晰、表達準確、文筆流暢",
      "negative": "文不達意，難於理解"
    }
  ]
}
```

## 2. 反思报告评分 API

### 接口信息
- **URL**: `/api/course/reflection/score`
- **方法**: `POST`
- **描述**: 提交反思报告评分

### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | integer | 是 | 反思报告ID |
| teacher_comment | string | 否 | 教师评语 |
| score_result | object | 是 | 评分结果 |
| score_result.understanding | object | 是 | 理解能力评分 |
| score_result.understanding.is_passed | integer | 是 | 是否通过 (0=未通过, 1=通过) |
| score_result.understanding.comment | string | 否 | 理解能力评语 |
| score_result.cognition | object | 是 | 认知能力评分 |
| score_result.cognition.is_passed | integer | 是 | 是否通过 (0=未通过, 1=通过) |
| score_result.cognition.comment | string | 否 | 认知能力评语 |
| score_result.discussion | object | 是 | 讨论能力评分 |
| score_result.discussion.is_passed | integer | 是 | 是否通过 (0=未通过, 1=通过) |
| score_result.discussion.comment | string | 否 | 讨论能力评语 |
| score_result.writing | object | 是 | 写作能力评分 |
| score_result.writing.is_passed | integer | 是 | 是否通过 (0=未通过, 1=通过) |
| score_result.writing.comment | string | 否 | 写作能力评语 |

### 请求示例

#### cURL 请求
```bash
curl -X POST "http://localhost/api/course/reflection/score" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "id": 123,
    "teacher_comment": "整体表现良好，需要在批判性思维方面加强。",
    "score_result": {
      "understanding": {
        "is_passed": 1,
        "comment": "对理论理解透彻"
      },
      "cognition": {
        "is_passed": 0,
        "comment": "批判性思维需要加强"
      },
      "discussion": {
        "is_passed": 1,
        "comment": "讨论逻辑清晰"
      },
      "writing": {
        "is_passed": 1,
        "comment": "文笔流畅，表达准确"
      }
    }
  }'
```

#### JavaScript (Axios) 请求
```javascript
const scoreData = {
  id: 123,
  teacher_comment: "整体表现良好，需要在批判性思维方面加强。",
  score_result: {
    understanding: {
      is_passed: 1,
      comment: "对理论理解透彻"
    },
    cognition: {
      is_passed: 0,
      comment: "批判性思维需要加强"
    },
    discussion: {
      is_passed: 1,
      comment: "讨论逻辑清晰"
    },
    writing: {
      is_passed: 1,
      comment: "文笔流畅，表达准确"
    }
  }
};

const response = await axios.post('/api/course/reflection/score', scoreData, {
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN_HERE'
  }
});
```

### 响应示例

#### 成功响应 (200 OK)
```json
{
  "message": "success",
  "item": {
    "id": 123,
    "status": "scored"
  }
}
```

#### 验证错误响应 (422 Unprocessable Entity)

##### 简体中文错误信息
```json
{
  "message": "验证失败",
  "errors": {
    "id": ["ID不能为空"],
    "score_result.understanding.is_passed": ["理解能力通过状态不能为空"],
    "score_result.cognition.is_passed": ["认知能力通过状态必须为整数"],
    "teacher_comment": ["教师评语必须为字符串"]
  }
}
```

##### 英文错误信息 (Accept-Language: en)
```json
{
  "message": "Validation failed",
  "errors": {
    "id": ["ID is required"],
    "score_result.understanding.is_passed": ["Understanding pass status is required"],
    "score_result.cognition.is_passed": ["Cognition pass status must be an integer"],
    "teacher_comment": ["Teacher comment must be a string"]
  }
}
```

##### 繁体中文错误信息 (Accept-Language: zh-HK)
```json
{
  "message": "驗證失敗",
  "errors": {
    "id": ["ID不能為空"],
    "score_result.understanding.is_passed": ["理解能力通過狀態不能為空"],
    "score_result.cognition.is_passed": ["認知能力通過狀態必須為整數"],
    "teacher_comment": ["教師評語必須為字符串"]
  }
}
```

## 3. 多语言支持

### 语言设置方式

系统支持三种方式设置语言，优先级从高到低：

1. **URL参数** - `?locale=en`
2. **Accept-Language请求头** - `Accept-Language: en`
3. **系统默认** - `zh_CN` (简体中文)

### 支持的语言

| 语言代码 | 语言名称 | Accept-Language值 |
|----------|----------|-------------------|
| `zh_CN` | 简体中文 | `zh-CN`, `zh` |
| `en` | English | `en` |
| `zh_HK` | 繁體中文 | `zh-HK`, `zh-TW` |

### 设置语言示例

#### 方式1: URL参数
```bash
# 英文
curl "http://localhost/api/course/reflection/getScoringCriteria?locale=en"

# 繁体中文
curl "http://localhost/api/course/reflection/getScoringCriteria?locale=zh_HK"

# 简体中文
curl "http://localhost/api/course/reflection/getScoringCriteria?locale=zh_CN"
```

#### 方式2: Accept-Language请求头
```bash
# 英文响应
curl -H "Accept-Language: en" \
  "http://localhost/api/course/reflection/getScoringCriteria"

# 繁体中文响应
curl -H "Accept-Language: zh-HK" \
  "http://localhost/api/course/reflection/getScoringCriteria"

# 简体中文响应 (默认)
curl -H "Accept-Language: zh-CN" \
  "http://localhost/api/course/reflection/getScoringCriteria"

# 复杂的Accept-Language头 (浏览器常见格式)
curl -H "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" \
  "http://localhost/api/course/reflection/getScoringCriteria"
```

#### 方式3: JavaScript示例
```javascript
// 使用URL参数
const response1 = await axios.get('/api/course/reflection/getScoringCriteria?locale=en');

// 使用请求头
const response2 = await axios.get('/api/course/reflection/getScoringCriteria', {
  headers: {
    'Accept-Language': 'zh-HK'
  }
});

// 动态设置语言
const userLanguage = navigator.language; // 获取用户浏览器语言
const response3 = await axios.get('/api/course/reflection/getScoringCriteria', {
  headers: {
    'Accept-Language': userLanguage
  }
});
```

### 语言优先级处理

系统会按以下优先级处理语言设置：

1. **URL参数优先**: `?locale=en` 会覆盖请求头设置
2. **Accept-Language解析**: 支持质量值 (q值) 排序
3. **回退机制**: 不支持的语言会回退到默认语言

#### Accept-Language解析示例
```
Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,zh-HK;q=0.7
```
解析结果：
1. `zh-CN` (q=1.0, 默认) → 映射到 `zh_CN`
2. `zh` (q=0.9) → 映射到 `zh_CN`
3. `en` (q=0.8) → 映射到 `en`
4. `zh-HK` (q=0.7) → 映射到 `zh_HK`

系统会选择质量值最高且支持的语言。

## 4. 错误代码说明

| HTTP状态码 | 描述 | 示例场景 |
|------------|------|----------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 请求参数格式错误 |
| 401 | 未授权 | Token无效或过期 |
| 403 | 禁止访问 | 没有权限访问该资源 |
| 404 | 资源不存在 | 反思报告不存在 |
| 422 | 验证失败 | 请求参数验证不通过 |
| 500 | 服务器错误 | 服务器内部错误 |

## 5. 完整的API调用示例

### 获取评分标准 - 完整示例

```bash
#!/bin/bash

# 设置基础变量
BASE_URL="http://localhost"
TOKEN="your_bearer_token_here"

echo "=== 获取评分标准 - 多语言测试 ==="

# 简体中文
echo "1. 简体中文:"
curl -s -X GET "${BASE_URL}/api/course/reflection/getScoringCriteria?locale=zh_CN" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer ${TOKEN}" | jq '.criteria[0].title'

# 英文
echo "2. 英文:"
curl -s -X GET "${BASE_URL}/api/course/reflection/getScoringCriteria" \
  -H "Accept: application/json" \
  -H "Accept-Language: en" \
  -H "Authorization: Bearer ${TOKEN}" | jq '.criteria[0].title'

# 繁体中文
echo "3. 繁体中文:"
curl -s -X GET "${BASE_URL}/api/course/reflection/getScoringCriteria" \
  -H "Accept: application/json" \
  -H "Accept-Language: zh-HK" \
  -H "Authorization: Bearer ${TOKEN}" | jq '.criteria[0].title'
```

### 提交评分 - 完整示例

```bash
#!/bin/bash

# 提交反思报告评分
curl -X POST "${BASE_URL}/api/course/reflection/score" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Accept-Language: zh-CN" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d '{
    "id": 123,
    "teacher_comment": "整体表现良好，需要在批判性思维方面加强。",
    "score_result": {
      "understanding": {
        "is_passed": 1,
        "comment": "对理论理解透彻"
      },
      "cognition": {
        "is_passed": 0,
        "comment": "批判性思维需要加强"
      },
      "discussion": {
        "is_passed": 1,
        "comment": "讨论逻辑清晰"
      },
      "writing": {
        "is_passed": 1,
        "comment": "文笔流畅，表达准确"
      }
    }
  }'
```

### 错误处理示例

```javascript
// JavaScript 错误处理示例
async function submitReflectionScore(scoreData, language = 'zh-CN') {
  try {
    const response = await axios.post('/api/course/reflection/score', scoreData, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Accept-Language': language,
        'Authorization': `Bearer ${token}`
      }
    });

    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    if (error.response) {
      // 服务器返回错误响应
      const { status, data } = error.response;

      switch (status) {
        case 422:
          // 验证错误
          return {
            success: false,
            type: 'validation',
            message: data.message,
            errors: data.errors
          };
        case 401:
          // 未授权
          return {
            success: false,
            type: 'auth',
            message: '请重新登录'
          };
        case 404:
          // 资源不存在
          return {
            success: false,
            type: 'not_found',
            message: '反思报告不存在'
          };
        default:
          return {
            success: false,
            type: 'server',
            message: '服务器错误'
          };
      }
    } else {
      // 网络错误
      return {
        success: false,
        type: 'network',
        message: '网络连接失败'
      };
    }
  }
}

// 使用示例
const result = await submitReflectionScore({
  id: 123,
  score_result: {
    understanding: { is_passed: 1, comment: "理解透彻" },
    cognition: { is_passed: 0, comment: "需要加强" },
    discussion: { is_passed: 1, comment: "逻辑清晰" },
    writing: { is_passed: 1, comment: "表达准确" }
  }
}, 'zh-CN');

if (result.success) {
  console.log('评分提交成功:', result.data);
} else {
  console.error('评分提交失败:', result.message);
  if (result.type === 'validation') {
    console.error('验证错误:', result.errors);
  }
}
```

## 6. 注意事项

1. **认证**: 所有API请求都需要在请求头中包含有效的Bearer Token
2. **内容类型**: 请求和响应的Content-Type都应该是`application/json`
3. **字符编码**: 使用UTF-8编码
4. **评分规则**: `is_passed`字段只接受0（未通过）或1（通过）
5. **多语言**: 根据Accept-Language头或URL参数返回对应语言的错误信息
6. **语言优先级**: URL参数 > Accept-Language头 > 系统默认
7. **错误处理**: 建议实现完整的错误处理机制，包括网络错误、验证错误等
8. **性能优化**: 对于频繁调用的API，建议实现适当的缓存机制
